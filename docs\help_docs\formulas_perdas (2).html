<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhamento dos Cálculos de Perdas - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <!-- marked.js is removed as content is pre-converted -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF; /* Mais claro para melhor contraste */
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E; /* Fundo mais escuro */
            --card-bg-color: #2D2D2D; /* Fundo do card mais escuro */
            --sidebar-bg-color: #252525; /* Fundo da barra lateral mais escuro */
            --border-color: #6c757d;
            --link-color: #4DA3FF; /* Cor de link mais clara para melhor contraste */
            --link-hover-color: #80BDFF; /* Cor de hover mais clara */
            --heading-color: #FFFFFF; /* Cor de cabeçalho branca para melhor contraste */
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--link-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content">                        <!-- Conteúdo Markdown convertido para HTML aqui -->
                        <h1 id="detalhamento-dos-calculos-de-perdas-em-transformadores">Detalhamento dos Cálculos de Perdas em Transformadores</h1>

<h2 id="sumario-do-documento">Sumário do Documento</h2>

<ul>
<li><strong>1. Introdução</strong>: Conceitos básicos de perdas em transformadores</li>
<li><strong>2. Perdas em Vazio (No-Load Losses)</strong>: Parâmetros, cálculos e análise SUT/EPS</li>
<li><strong>3. Perdas em Carga (Load Losses)</strong>: Cenários, bancos de capacitores e compensação</li>
<li><strong>4. Configuração dos Bancos de Capacitores</strong>: Disponibilidade e lógica de seleção</li>
<li><strong>5. Considerações Finais</strong>: Implementação e observações técnicas</li>
</ul>
<hr />

<h2 id="1-introducao">1. Introdução</h2>

<p>As perdas em transformadores são divididas em duas categorias principais:</p>

<ul>
<li><strong>Perdas em Vazio (No-Load Losses) - P<sub>Fe</sub></strong>: Perdas no núcleo magnético devido à histerese e correntes parasitas. Ocorrem quando o transformador está energizado, mesmo sem carga. São dependentes da tensão e frequência aplicadas.</li>
<li><strong>Perdas em Carga (Load Losses) - P<sub>Cu</sub></strong>: Perdas nos enrolamentos devido à resistência ôhmica. Ocorrem quando há corrente circulando pelos enrolamentos. São proporcionais ao quadrado da corrente de carga.</li>
</ul>

<p>Este documento detalha as fórmulas e metodologias implementadas no sistema para calcular e analisar essas perdas, baseando-se no serviço <code>losses_service.py</code>.</p>

<hr />

<h2 id="2-perdas-em-vazio">2. Perdas em Vazio (No-Load Losses)</h2>

<p>As perdas em vazio são medidas aplicando tensão nominal (ou suas variações como 1,1 pu e 1,2 pu) no lado de baixa tensão do transformador, mantendo o lado de alta tensão em circuito aberto.</p>

<h3 id="21-parametros-de-entrada">2.1. Parâmetros de Entrada</h3>

<table>
<thead>
<tr>
<th><strong>Parâmetro</strong></th>
<th><strong>Descrição</strong></th>
<th><strong>Unidade</strong></th>
<th><strong>Origem</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>perdas_vazio_ui</td>
<td>Perdas em vazio de projeto</td>
<td>kW</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>peso_nucleo_ui</td>
<td>Peso do núcleo</td>
<td>ton</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>corrente_excitacao_ui</td>
<td>Corrente de excitação nominal</td>
<td>%</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>inducao_ui</td>
<td>Indução magnética nominal</td>
<td>T</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>corrente_exc_1_1_ui</td>
<td>Corrente de excitação a 110% Un</td>
<td>%</td>
<td>Interface do usuário (opcional)</td>
</tr>
<tr>
<td>corrente_exc_1_2_ui</td>
<td>Corrente de excitação a 120% Un</td>
<td>%</td>
<td>Interface do usuário (opcional)</td>
</tr>
<tr>
<td>frequencia</td>
<td>Frequência nominal</td>
<td>Hz</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>tensao_bt_kv</td>
<td>Tensão nominal BT</td>
<td>kV</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>corrente_nominal_bt</td>
<td>Corrente nominal BT</td>
<td>A</td>
<td>Dados do transformador</td>
</tr>
</tbody>
</table>
<thead>
<tr class="header">
<th><strong>Parâmetro</strong></th>
<th><strong>Descrição</strong></th>
<th><strong>Unidade</strong></th>
<th><strong>Variável Python (losses.py)</strong></th>
<th><strong>Origem</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>perdas_vazio_ui</td>
<td>Perdas em vazio (no-load) de projeto</td>
<td>kW</td>
<td>perdas_vazio_ui</td>
<td>UI</td>
</tr>
<tr class="even">
<td>peso_nucleo_ui</td>
<td>Peso do núcleo de projeto</td>
<td>Ton</td>
<td>peso_nucleo_ui</td>
<td>UI</td>
</tr>
<tr class="odd">
<td>corrente_excitacao_ui</td>
<td>Corrente de excitação nominal de projeto</td>
<td>%</td>
<td>corrente_excitacao_ui</td>
<td>UI</td>
</tr>
<tr class="even">
<td>inducao_ui</td>
<td>Indução magnética nominal de projeto no núcleo</td>
<td>T</td>
<td>inducao_ui</td>
<td>UI</td>
</tr>
<tr class="odd">
<td>corrente_exc_1_1_ui</td>
<td>Corrente de excitação de projeto a 110% V nominal</td>
<td>%</td>
<td>corrente_exc_1_1_ui</td>
<td>UI (Opcional)</td>
</tr>
<tr class="even">
<td>corrente_exc_1_2_ui</td>
<td>Corrente de excitação de projeto a 120% V nominal</td>
<td>%</td>
<td>corrente_exc_1_2_ui</td>
<td>UI (Opcional)</td>
</tr>
<tr class="odd">
<td>frequencia</td>
<td>Frequência nominal do DUT</td>
<td>Hz</td>
<td>frequencia</td>
<td>Dados Transf.</td>
</tr>
<tr class="even">
<td>tensao_bt_kv</td>
<td>Tensão nominal BT do DUT</td>
<td>kV</td>
<td>tensao_bt_kv</td>
<td>Dados Transf.</td>
</tr>
<tr class="odd">
<td>corrente_nominal_bt</td>
<td>Corrente nominal BT do DUT</td>
<td>A</td>
<td>corrente_nominal_bt</td>
<td>Dados Transf.</td>
</tr>
<tr class="even">
<td>tipo_transformador</td>
<td>Monofásico ou Trifásico</td>
<td>-</td>
<td>tipo_transformador</td>
<td>Dados Transf.</td>
</tr>
</tbody>
</table>
<h3 id="3.2-tabelas-de-referência-para-aços-8">3.2. Tabelas de Referência para Aços</h3>
<h4 id="3.2.1-aço-m4-referência-9">3.2.1. Aço M4 (Referência)</h4>
<h5 id="*******-tabela-de-perdas-específicas-do-núcleo-wkg-10">*******. Tabela de Perdas Específicas do Núcleo (W/kg)</h5>
<p>Valores de perdas específicas (W/kg) em função da indução magnética (T) e frequência (Hz). Estes dados são armazenados em <strong>perdas_nucleo_data</strong>.</p>
<table>
<thead>
<tr class="header">
<th><strong>Indução (T)</strong></th>
<th><strong>50 Hz</strong></th>
<th><strong>60 Hz</strong></th>
<th><strong>100 Hz</strong></th>
<th><strong>120 Hz</strong></th>
<th><strong>150 Hz</strong></th>
<th><strong>200 Hz</strong></th>
<th><strong>240 Hz</strong></th>
<th><strong>250 Hz</strong></th>
<th><strong>300 Hz</strong></th>
<th><strong>350 Hz</strong></th>
<th><strong>400 Hz</strong></th>
<th><strong>500 Hz</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>0.5</strong></td>
<td><strong>0.10</strong></td>
<td><strong>0.13</strong></td>
<td><strong>0.25</strong></td>
<td><strong>0.35</strong></td>
<td><strong>0.50</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.10</strong></td>
<td><strong>1.15</strong></td>
<td><strong>1.30</strong></td>
<td><strong>1.50</strong></td>
<td><strong>1.70</strong></td>
<td><strong>2.10</strong></td>
</tr>
<tr class="even">
<td><strong>0.6</strong></td>
<td><strong>0.12</strong></td>
<td><strong>0.18</strong></td>
<td><strong>0.38</strong></td>
<td><strong>0.48</strong></td>
<td><strong>0.70</strong></td>
<td><strong>1.10</strong></td>
<td><strong>1.50</strong></td>
<td><strong>1.60</strong></td>
<td><strong>2.00</strong></td>
<td><strong>2.40</strong></td>
<td><strong>2.80</strong></td>
<td><strong>3.50</strong></td>
</tr>
<tr class="odd">
<td><strong>0.7</strong></td>
<td><strong>0.15</strong></td>
<td><strong>0.23</strong></td>
<td><strong>0.50</strong></td>
<td><strong>0.62</strong></td>
<td><strong>0.95</strong></td>
<td><strong>1.55</strong></td>
<td><strong>2.10</strong></td>
<td><strong>2.30</strong></td>
<td><strong>3.00</strong></td>
<td><strong>3.60</strong></td>
<td><strong>4.20</strong></td>
<td><strong>5.50</strong></td>
</tr>
<tr class="even">
<td><strong>0.8</strong></td>
<td><strong>0.20</strong></td>
<td><strong>0.30</strong></td>
<td><strong>0.65</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.20</strong></td>
<td><strong>2.00</strong></td>
<td><strong>2.80</strong></td>
<td><strong>3.00</strong></td>
<td><strong>3.90</strong></td>
<td><strong>4.70</strong></td>
<td><strong>5.50</strong></td>
<td><strong>7.50</strong></td>
</tr>
<tr class="odd">
<td><strong>0.9</strong></td>
<td><strong>0.25</strong></td>
<td><strong>0.37</strong></td>
<td><strong>0.82</strong></td>
<td><strong>1.00</strong></td>
<td><strong>1.50</strong></td>
<td><strong>2.50</strong></td>
<td><strong>3.50</strong></td>
<td><strong>3.80</strong></td>
<td><strong>4.80</strong></td>
<td><strong>5.80</strong></td>
<td><strong>6.80</strong></td>
<td><strong>9.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.0</strong></td>
<td><strong>0.32</strong></td>
<td><strong>0.46</strong></td>
<td><strong>1.00</strong></td>
<td><strong>1.25</strong></td>
<td><strong>1.85</strong></td>
<td><strong>3.10</strong></td>
<td><strong>4.20</strong></td>
<td><strong>4.50</strong></td>
<td><strong>5.90</strong></td>
<td><strong>7.00</strong></td>
<td><strong>8.50</strong></td>
<td><strong>11.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.1</strong></td>
<td><strong>0.41</strong></td>
<td><strong>0.55</strong></td>
<td><strong>1.21</strong></td>
<td><strong>1.55</strong></td>
<td><strong>2.20</strong></td>
<td><strong>3.70</strong></td>
<td><strong>5.00</strong></td>
<td><strong>5.40</strong></td>
<td><strong>6.90</strong></td>
<td><strong>8.50</strong></td>
<td><strong>10.00</strong></td>
<td><strong>14.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.2</strong></td>
<td><strong>0.50</strong></td>
<td><strong>0.65</strong></td>
<td><strong>1.41</strong></td>
<td><strong>1.90</strong></td>
<td><strong>2.70</strong></td>
<td><strong>4.50</strong></td>
<td><strong>6.00</strong></td>
<td><strong>6.40</strong></td>
<td><strong>8.10</strong></td>
<td><strong>10.00</strong></td>
<td><strong>12.00</strong></td>
<td><strong>17.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.3</strong></td>
<td><strong>0.60</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.65</strong></td>
<td><strong>2.30</strong></td>
<td><strong>3.20</strong></td>
<td><strong>5.20</strong></td>
<td><strong>7.00</strong></td>
<td><strong>7.50</strong></td>
<td><strong>9.50</strong></td>
<td><strong>11.50</strong></td>
<td><strong>14.00</strong></td>
<td><strong>20.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.4</strong></td>
<td><strong>0.71</strong></td>
<td><strong>0.95</strong></td>
<td><strong>1.95</strong></td>
<td><strong>2.80</strong></td>
<td><strong>3.80</strong></td>
<td><strong>6.00</strong></td>
<td><strong>8.50</strong></td>
<td><strong>9.00</strong></td>
<td><strong>11.00</strong></td>
<td><strong>13.50</strong></td>
<td><strong>16.00</strong></td>
<td><strong>24.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.5</strong></td>
<td><strong>0.85</strong></td>
<td><strong>1.10</strong></td>
<td><strong>2.30</strong></td>
<td><strong>3.30</strong></td>
<td><strong>4.50</strong></td>
<td><strong>7.00</strong></td>
<td><strong>10.00</strong></td>
<td><strong>10.60</strong></td>
<td><strong>13.00</strong></td>
<td><strong>15.50</strong></td>
<td><strong>19.00</strong></td>
<td><strong>29.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.6</strong></td>
<td><strong>1.00</strong></td>
<td><strong>1.30</strong></td>
<td><strong>2.80</strong></td>
<td><strong>3.80</strong></td>
<td><strong>5.30</strong></td>
<td><strong>8.00</strong></td>
<td><strong>12.00</strong></td>
<td><strong>12.60</strong></td>
<td><strong>15.00</strong></td>
<td><strong>18.00</strong></td>
<td><strong>23.00</strong></td>
<td><strong>35.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.7</strong></td>
<td><strong>1.20</strong></td>
<td><strong>1.55</strong></td>
<td><strong>3.50</strong></td>
<td><strong>4.40</strong></td>
<td><strong>6.00</strong></td>
<td><strong>9.00</strong></td>
<td><strong>15.00</strong></td>
<td><strong>15.60</strong></td>
<td><strong>18.00</strong></td>
<td><strong>22.00</strong></td>
<td><strong>28.00</strong></td>
<td><strong>42.00</strong></td>
</tr>
</tbody>
</table>
<h5 id="3.2.1.2-tabela-de-potência-magnetizante-específica-varkg-11">3.2.1.2. Tabela de Potência Magnetizante Específica (VAR/kg)</h5>
<p>Valores de potência magnetizante específica (VAR/kg) em função da indução magnética (T) e frequência (Hz). Estes dados são armazenados em <strong>potencia_magnet_data</strong>.</p>
<table>
<thead>
<tr class="header">
<th><strong>Indução (T)</strong></th>
<th><strong>50 Hz</strong></th>
<th><strong>60 Hz</strong></th>
<th><strong>100 Hz</strong></th>
<th><strong>120 Hz</strong></th>
<th><strong>150 Hz</strong></th>
<th><strong>200 Hz</strong></th>
<th><strong>240 Hz</strong></th>
<th><strong>250 Hz</strong></th>
<th><strong>300 Hz</strong></th>
<th><strong>350 Hz</strong></th>
<th><strong>400 Hz</strong></th>
<th><strong>500 Hz</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>0.5</strong></td>
<td><strong>0.10</strong></td>
<td><strong>0.15</strong></td>
<td><strong>0.35</strong></td>
<td><strong>0.45</strong></td>
<td><strong>0.70</strong></td>
<td><strong>1.00</strong></td>
<td><strong>1.30</strong></td>
<td><strong>1.40</strong></td>
<td><strong>1.70</strong></td>
<td><strong>2.10</strong></td>
<td><strong>3.00</strong></td>
<td><strong>4.00</strong></td>
</tr>
<tr class="even">
<td><strong>0.6</strong></td>
<td><strong>0.15</strong></td>
<td><strong>0.20</strong></td>
<td><strong>0.45</strong></td>
<td><strong>0.60</strong></td>
<td><strong>0.90</strong></td>
<td><strong>1.40</strong></td>
<td><strong>1.80</strong></td>
<td><strong>1.90</strong></td>
<td><strong>2.50</strong></td>
<td><strong>3.30</strong></td>
<td><strong>4.00</strong></td>
<td><strong>5.50</strong></td>
</tr>
<tr class="odd">
<td><strong>0.7</strong></td>
<td><strong>0.23</strong></td>
<td><strong>0.28</strong></td>
<td><strong>0.60</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.10</strong></td>
<td><strong>1.70</strong></td>
<td><strong>2.30</strong></td>
<td><strong>2.50</strong></td>
<td><strong>3.40</strong></td>
<td><strong>4.20</strong></td>
<td><strong>5.20</strong></td>
<td><strong>7.50</strong></td>
</tr>
<tr class="even">
<td><strong>0.8</strong></td>
<td><strong>0.30</strong></td>
<td><strong>0.35</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.00</strong></td>
<td><strong>1.40</strong></td>
<td><strong>2.20</strong></td>
<td><strong>3.00</strong></td>
<td><strong>3.30</strong></td>
<td><strong>4.50</strong></td>
<td><strong>5.50</strong></td>
<td><strong>7.00</strong></td>
<td><strong>9.50</strong></td>
</tr>
<tr class="odd">
<td><strong>0.9</strong></td>
<td><strong>0.38</strong></td>
<td><strong>0.45</strong></td>
<td><strong>0.95</strong></td>
<td><strong>1.30</strong></td>
<td><strong>1.70</strong></td>
<td><strong>2.80</strong></td>
<td><strong>3.80</strong></td>
<td><strong>4.00</strong></td>
<td><strong>5.60</strong></td>
<td><strong>7.00</strong></td>
<td><strong>8.80</strong></td>
<td><strong>12.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.0</strong></td>
<td><strong>0.45</strong></td>
<td><strong>0.55</strong></td>
<td><strong>1.10</strong></td>
<td><strong>1.60</strong></td>
<td><strong>2.20</strong></td>
<td><strong>3.50</strong></td>
<td><strong>4.50</strong></td>
<td><strong>4.80</strong></td>
<td><strong>6.90</strong></td>
<td><strong>8.50</strong></td>
<td><strong>11.00</strong></td>
<td><strong>15.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.1</strong></td>
<td><strong>0.55</strong></td>
<td><strong>0.70</strong></td>
<td><strong>1.50</strong></td>
<td><strong>2.00</strong></td>
<td><strong>2.80</strong></td>
<td><strong>4.10</strong></td>
<td><strong>5.50</strong></td>
<td><strong>5.80</strong></td>
<td><strong>8.10</strong></td>
<td><strong>10.00</strong></td>
<td><strong>13.00</strong></td>
<td><strong>18.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.2</strong></td>
<td><strong>0.65</strong></td>
<td><strong>0.85</strong></td>
<td><strong>2.00</strong></td>
<td><strong>2.40</strong></td>
<td><strong>3.30</strong></td>
<td><strong>5.00</strong></td>
<td><strong>6.50</strong></td>
<td><strong>7.00</strong></td>
<td><strong>9.50</strong></td>
<td><strong>12.00</strong></td>
<td><strong>15.00</strong></td>
<td><strong>22.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.3</strong></td>
<td><strong>0.80</strong></td>
<td><strong>1.00</strong></td>
<td><strong>2.20</strong></td>
<td><strong>2.85</strong></td>
<td><strong>3.80</strong></td>
<td><strong>6.00</strong></td>
<td><strong>7.50</strong></td>
<td><strong>8.00</strong></td>
<td><strong>11.20</strong></td>
<td><strong>13.50</strong></td>
<td><strong>17.00</strong></td>
<td><strong>26.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.4</strong></td>
<td><strong>0.95</strong></td>
<td><strong>1.20</strong></td>
<td><strong>2.50</strong></td>
<td><strong>3.30</strong></td>
<td><strong>4.50</strong></td>
<td><strong>7.00</strong></td>
<td><strong>9.00</strong></td>
<td><strong>9.90</strong></td>
<td><strong>13.50</strong></td>
<td><strong>16.00</strong></td>
<td><strong>20.00</strong></td>
<td><strong>30.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.5</strong></td>
<td><strong>1.10</strong></td>
<td><strong>1.40</strong></td>
<td><strong>3.00</strong></td>
<td><strong>4.00</strong></td>
<td><strong>5.50</strong></td>
<td><strong>9.00</strong></td>
<td><strong>11.00</strong></td>
<td><strong>12.00</strong></td>
<td><strong>15.50</strong></td>
<td><strong>18.00</strong></td>
<td><strong>24.00</strong></td>
<td><strong>37.00</strong></td>
</tr>
<tr class="even">
<td><strong>1.6</strong></td>
<td><strong>1.30</strong></td>
<td><strong>1.60</strong></td>
<td><strong>3.50</strong></td>
<td><strong>4.80</strong></td>
<td><strong>6.50</strong></td>
<td><strong>12.00</strong></td>
<td><strong>14.00</strong></td>
<td><strong>15.00</strong></td>
<td><strong>18.00</strong></td>
<td><strong>22.00</strong></td>
<td><strong>30.00</strong></td>
<td><strong>45.00</strong></td>
</tr>
<tr class="odd">
<td><strong>1.7</strong></td>
<td><strong>1.60</strong></td>
<td><strong>2.00</strong></td>
<td><strong>4.00</strong></td>
<td><strong>5.50</strong></td>
<td><strong>7.00</strong></td>
<td><strong>15.00</strong></td>
<td><strong>17.00</strong></td>
<td><strong>18.00</strong></td>
<td><strong>22.00</strong></td>
<td><strong>28.00</strong></td>
<td><strong>38.00</strong></td>
<td><strong>55.00</strong></td>
</tr>
</tbody>
</table>
<h4 id="3.2.2-aço-h110-27-12">3.2.2. Aço H110-27</h4>
<h5 id="3.2.2.1-tabela-de-perdas-específicas-do-núcleo-wkg-13">3.2.2.1. Tabela de Perdas Específicas do Núcleo (W/kg)</h5>
<p>Valores de perdas específicas (W/kg) em função da indução magnética (T) e frequência (Hz). Estes dados são armazenados em <strong>perdas_nucleo_data_H110_27</strong>.</p>
<table>
<thead>
<tr class="header">
<th><strong>Indução (T)</strong></th>
<th><strong>50 Hz</strong></th>
<th><strong>60 Hz</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>0.2</strong></td>
<td><strong>0.018</strong></td>
<td><strong>0.023</strong></td>
</tr>
<tr class="even">
<td><strong>0.3</strong></td>
<td><strong>0.038</strong></td>
<td><strong>0.050</strong></td>
</tr>
<tr class="odd">
<td><strong>0.4</strong></td>
<td><strong>0.065</strong></td>
<td><strong>0.086</strong></td>
</tr>
<tr class="even">
<td><strong>0.5</strong></td>
<td><strong>0.097</strong></td>
<td><strong>0.128</strong></td>
</tr>
<tr class="odd">
<td><strong>0.6</strong></td>
<td><strong>0.135</strong></td>
<td><strong>0.178</strong></td>
</tr>
<tr class="even">
<td><strong>0.7</strong></td>
<td><strong>0.178</strong></td>
<td><strong>0.236</strong></td>
</tr>
<tr class="odd">
<td><strong>0.8</strong></td>
<td><strong>0.228</strong></td>
<td><strong>0.301</strong></td>
</tr>
<tr class="even">
<td><strong>0.9</strong></td>
<td><strong>0.284</strong></td>
<td><strong>0.377</strong></td>
</tr>
<tr class="odd">
<td><strong>1.0</strong></td>
<td><strong>0.346</strong></td>
<td><strong>0.459</strong></td>
</tr>
<tr class="even">
<td><strong>1.1</strong></td>
<td><strong>0.414</strong></td>
<td><strong>0.549</strong></td>
</tr>
<tr class="odd">
<td><strong>1.2</strong></td>
<td><strong>0.488</strong></td>
<td><strong>0.648</strong></td>
</tr>
<tr class="even">
<td><strong>1.3</strong></td>
<td><strong>0.569</strong></td>
<td><strong>0.755</strong></td>
</tr>
<tr class="odd">
<td><strong>1.4</strong></td>
<td><strong>0.658</strong></td>
<td><strong>0.873</strong></td>
</tr>
<tr class="even">
<td><strong>1.5</strong></td>
<td><strong>0.760</strong></td>
<td><strong>1.006</strong></td>
</tr>
<tr class="odd">
<td><strong>1.6</strong></td>
<td><strong>0.882</strong></td>
<td><strong>1.165</strong></td>
</tr>
<tr class="even">
<td><strong>1.7</strong></td>
<td><strong>1.052</strong></td>
<td><strong>1.383</strong></td>
</tr>
<tr class="odd">
<td><strong>1.8</strong></td>
<td><strong>1.398</strong></td>
<td><strong>1.816</strong></td>
</tr>
<tr class="even">
<td><strong>1.9</strong></td>
<td><strong>2.010</strong></td>
<td><strong>2.595</strong></td>
</tr>
</tbody>
</table>
<p><strong>Nota: Para o aço H110-27, os dados estão disponíveis apenas para as frequências de 50 Hz e 60 Hz.</strong></p>
<h5 id="3.2.2.2-tabela-de-potência-magnetizante-específica-vakg-14">3.2.2.2. Tabela de Potência Magnetizante Específica (VA/kg)</h5>
<p>Valores de potência magnetizante específica (VA/kg) em função da indução magnética (T) e frequência (Hz). Estes dados são armazenados em <strong>potencia_magnet_data_H110_27</strong>.</p>
<table>
<thead>
<tr class="header">
<th><strong>Indução (T)</strong></th>
<th><strong>50 Hz</strong></th>
<th><strong>60 Hz</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><strong>0.2</strong></td>
<td><strong>0.032</strong></td>
<td><strong>0.040</strong></td>
</tr>
<tr class="even">
<td><strong>0.3</strong></td>
<td><strong>0.064</strong></td>
<td><strong>0.081</strong></td>
</tr>
<tr class="odd">
<td><strong>0.4</strong></td>
<td><strong>0.103</strong></td>
<td><strong>0.130</strong></td>
</tr>
<tr class="even">
<td><strong>0.5</strong></td>
<td><strong>0.147</strong></td>
<td><strong>0.186</strong></td>
</tr>
<tr class="odd">
<td><strong>0.6</strong></td>
<td><strong>0.196</strong></td>
<td><strong>0.249</strong></td>
</tr>
<tr class="even">
<td><strong>0.7</strong></td>
<td><strong>0.250</strong></td>
<td><strong>0.319</strong></td>
</tr>
<tr class="odd">
<td><strong>0.8</strong></td>
<td><strong>0.308</strong></td>
<td><strong>0.395</strong></td>
</tr>
<tr class="even">
<td><strong>0.9</strong></td>
<td><strong>0.372</strong></td>
<td><strong>0.477</strong></td>
</tr>
<tr class="odd">
<td><strong>1.0</strong></td>
<td><strong>0.441</strong></td>
<td><strong>0.568</strong></td>
</tr>
<tr class="even">
<td><strong>1.1</strong></td>
<td><strong>0.517</strong></td>
<td><strong>0.667</strong></td>
</tr>
<tr class="odd">
<td><strong>1.2</strong></td>
<td><strong>0.602</strong></td>
<td><strong>0.777</strong></td>
</tr>
<tr class="even">
<td><strong>1.3</strong></td>
<td><strong>0.698</strong></td>
<td><strong>0.900</strong></td>
</tr>
<tr class="odd">
<td><strong>1.4</strong></td>
<td><strong>0.812</strong></td>
<td><strong>1.045</strong></td>
</tr>
<tr class="even">
<td><strong>1.5</strong></td>
<td><strong>0.962</strong></td>
<td><strong>1.230</strong></td>
</tr>
<tr class="odd">
<td><strong>1.6</strong></td>
<td><strong>1.188</strong></td>
<td><strong>1.507</strong></td>
</tr>
<tr class="even">
<td><strong>1.7</strong></td>
<td><strong>1.661</strong></td>
<td><strong>2.070</strong></td>
</tr>
<tr class="odd">
<td><strong>1.8</strong></td>
<td><strong>3.438</strong></td>
<td><strong>4.178</strong></td>
</tr>
<tr class="even">
<td><strong>1.9</strong></td>
<td><strong>14.434</strong></td>
<td><strong>17.589</strong></td>
</tr>
</tbody>
</table>
<p><strong>Nota: Para o aço H110-27, os dados estão disponíveis apenas para as frequências de 50 Hz e 60 Hz.</strong></p>
<h3 id="3.3.-variáveis-calculadas-para-perdas-em-vazio-15">3.3. Variáveis Calculadas para Perdas em Vazio</h3>
<p><strong>Constante:</strong> <code>sqrt_3 = math.sqrt(3)</code> <strong>(para trifásicos, 1.0 para monofásicos, usado como</strong> <code>sqrt_3_factor</code> <strong>no código).</strong></p>
<h4 id="3.3.1-fatores-de-correção-para-aço-h110-27-16">3.3.1. Fatores de Correção para Aço H110-27</h4>
<p>Ao utilizar os dados do aço H110-27, os seguintes fatores de correção devem ser aplicados:</p>
<ul>
<li><strong>Fator de Construção (Build Factor - BF):</strong> Este fator compensa as perdas adicionais devido ao corte das chapas e à distribuição do fluxo nos cantos.
<ul>
<li><strong>Fator para perdas (W/kg):</strong> <strong>1.15</strong> - Multiplicar os valores de perdas específicas por 1.15</li>
<li><strong>Fator para potência magnetizante (VA/kg):</strong> <strong>1.2</strong> - Multiplicar os valores de VA/kg por 1.2</li>
</ul>
</li>
<li><strong>Divisor para Potência Magnetizante:</strong> Na implementação em callbacks/losses.py, o fator de potência magnetizante (VAR/kg) para o aço H110-27 deve ser dividido por <strong>1000</strong> (não por 1.000.000 como em algumas implementações anteriores).</li>
<li><strong>Implementação no Código:</strong> No arquivo callbacks/losses.py, os fatores são aplicados da seguinte forma:</li>
</ul>
<pre><code class="language-python"># Aplicar fatores construtivos
fator_perdas_H110_27 = fator_perdas_H110_27_base * 1.15 if fator_perdas_H110_27_base is not None else None
fator_potencia_mag_H110_27 = fator_potencia_mag_H110_27_base * 1.2 if fator_potencia_mag_H110_27_base is not None else None

# Cálculo da potência magnética
# 1. Multiplicar por peso_nucleo_calc_h110_27 em toneladas
# 2. Multiplicar por 1000 para converter toneladas para kg
# 3. Dividir por 1000 para converter VA para kVA (ou VAR para kVAR)
potencia_mag_h110_27 = (fator_potencia_mag_H110_27 * peso_nucleo_calc_h110_27 * 1000) / 1000 if h110_27_valid else 0  # kVAR
</code></pre>
<ul>
<li><strong>Observações Importantes:</strong>
<ul>
<li>Os fatores de construção (1.15 para perdas e 1.2 para potência magnetizante) devem ser sempre aplicados aos valores base do aço H110-27.</li>
<li>Estes fatores compensam as perdas nas bordas por perda de propriedade em função do corte e distribuição de fluxo nas quinas.</li>
<li>Ao comparar resultados entre diferentes tipos de aço, certifique-se de que os fatores de correção foram aplicados corretamente.</li>
</ul>
</li>
</ul>
<h4 id="3.3.2-cálculos-baseados-em-aço-m4-estimativa-17">3.3.2. Cálculos Baseados em Aço M4 (Estimativa)</h4>
<ul>
<li><code>perdas_vazio</code> <strong>(kW):</strong> <code>safe_float(perdas_vazio_ui, 0.0)</code></li>
<li><code>inducao</code> <strong>(T):</strong> <code>safe_float(inducao_ui, 0.0)</code></li>
<li><code>inducao_arredondada</code> <strong>(T):</strong> <code>round(inducao * 10) / 10</code></li>
<li><code>frequencia_arredondada</code> <strong>(Hz): Frequência da tabela mais próxima de</strong> <code>frequencia</code>.</li>
<li><code>lookup_key</code>: <code>(inducao_arredondada, frequencia_arredondada)</code></li>
<li><code>fator_perdas</code> <strong>(W/kg): Valor de</strong> <code>df_perdas_nucleo</code> <strong>para</strong> <code>lookup_key</code>.</li>
<li><code>fator_potencia_mag</code> <strong>(VAR/kg): Valor de</strong> <code>df_potencia_magnet</code> <strong>para</strong> <code>lookup_key</code>.</li>
<li><code>peso_nucleo_calc</code> <strong>(Ton):</strong>  <code>perdas_vazio / fator_perdas</code>. (Se <code>perdas_vazio</code> <strong>é kW e</strong> <code>fator_perdas</code> <strong>W/kg, esta fórmula resulta em kTon. Para Ton, seria</strong>  <code>(perdas_vazio * 1000) / fator_perdas / 1000</code>). <strong>O código usa esta fórmula, implicando que</strong> <code>fator_perdas</code> <strong>é tratado como kW/Ton neste contexto ou as unidades da tabela são interpretadas como tal para este cálculo específico.</strong></li>
<li><code>potencia_mag</code> <strong>(kVAR):</strong>  <code>fator_potencia_mag * peso_nucleo_calc</code>. (Se <code>fator_potencia_mag</code> <strong>é VAR/kg e</strong> <code>peso_nucleo_calc</code> <strong>é Ton, então</strong> <code>potencia_mag</code> <strong>[kVAR] =</strong> <code>fator_potencia_mag</code> ***** <code>peso_nucleo_calc</code> *** 1000 [** kg/Ton **] / 1000 [VAR/kVAR] =</strong> <code>fator_potencia_mag</code> *****  <code>peso_nucleo_calc</code>).</li>
<li><code>corrente_excitacao_calc</code> <strong>(A):</strong> <code>potencia_mag / (tensao_bt_kv * sqrt_3)</code></li>
<li><code>corrente_excitacao_percentual_calc</code> <strong>(%):</strong> <code>(corrente_excitacao_calc / corrente_nominal_bt) * 100</code></li>
<li><code>tensao_teste_1_1_kv</code> <strong>(kV):</strong> <code>tensao_bt_kv * 1.1</code></li>
<li><code>tensao_teste_1_2_kv</code> <strong>(kV):</strong> <code>tensao_bt_kv * 1.2</code></li>
<li><code>corrente_excitacao_1_1_calc</code> <strong>(A):</strong> <code>2 * corrente_excitacao_calc</code></li>
<li><code>corrente_excitacao_1_2_calc</code> <strong>(A):</strong> <code>4 * corrente_excitacao_calc</code></li>
<li><code>potencia_ensaio_1pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_bt_kv * corrente_excitacao_calc * sqrt_3</code></li>
<li><code>potencia_ensaio_1_1pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_teste_1_1_kv * corrente_excitacao_1_1_calc * sqrt_3</code></li>
<li><code>potencia_ensaio_1_2pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_teste_1_2_kv * corrente_excitacao_1_2_calc * sqrt_3</code></li>
</ul>
<h4 id="3.3.3-cálculos-baseados-em-dados-de-projeto-18">3.3.3. Cálculos Baseados em Dados de Projeto</h4>
<ul>
<li><code>perdas_vazio</code> <strong>(kW):</strong> <code>safe_float(perdas_vazio_ui, 0.0)</code></li>
<li><code>peso_nucleo</code> <strong>(Ton):</strong> <code>safe_float(peso_nucleo_ui, 0.0)</code></li>
<li><code>corrente_excitacao_percentual</code> <strong>(%):</strong> <code>safe_float(corrente_excitacao_ui, 0.0)</code></li>
<li><code>corrente_exc_1_1_input</code> <strong>(%):</strong> <code>safe_float(corrente_exc_1_1_ui)</code></li>
<li><code>corrente_exc_1_2_input</code> <strong>(%):</strong> <code>safe_float(corrente_exc_1_2_ui)</code></li>
<li><code>fator_perdas_projeto</code> <strong>(kW/Ton):</strong>  <code>perdas_vazio / peso_nucleo</code>. Para W/kg:  <code>(perdas_vazio * 1000) / (peso_nucleo * 1000)</code>.</li>
<li><code>corrente_excitacao_projeto</code> <strong>(A):</strong> <code>corrente_nominal_bt * (corrente_excitacao_percentual / 100.0)</code></li>
<li><code>potencia_ensaio_1pu_projeto_kva</code> <strong>(kVA):</strong> <code>tensao_bt_kv * corrente_excitacao_projeto * sqrt_3</code></li>
<li><code>potencia_mag_projeto_kvar</code> <strong>(kVAR):</strong> <code>potencia_ensaio_1pu_projeto_kva</code></li>
<li><code>fator_potencia_mag_projeto</code> <strong>(VAR/kg):</strong> <code>(potencia_mag_projeto_kvar * 1000) / (peso_nucleo * 1000)</code></li>
<li><code>fator_excitacao_default</code>: 3 (Trifásico) ou 5 (Monofásico).</li>
<li><code>corrente_excitacao_1_1</code> <strong>(A): Se</strong> <code>corrente_exc_1_1_input</code> <strong>não nulo,</strong>  <code>corrente_nominal_bt * (corrente_exc_1_1_input / 100.0)</code>. Senão,  <code>fator_excitacao_default * corrente_excitacao_projeto</code>.</li>
<li><code>corrente_excitacao_1_2</code> <strong>(A): Se</strong> <code>corrente_exc_1_2_input</code> <strong>não nulo,</strong>  <code>corrente_nominal_bt * (corrente_exc_1_2_input / 100.0)</code>. Senão,  <code>None</code>.</li>
<li><code>potencia_ensaio_1_1pu_projeto_kva</code> <strong>(kVA):</strong> <code>tensao_teste_1_1_kv * corrente_excitacao_1_1 * sqrt_3</code></li>
<li><code>potencia_ensaio_1_2pu_projeto_kva</code> <strong>(kVA): Se</strong> <code>corrente_excitacao_1_2</code> <strong>não nulo,</strong>  <code>tensao_teste_1_2_kv * corrente_excitacao_1_2 * sqrt_3</code>.</li>
</ul>
<h3 id="3.4-análise-suteps-para-perdas-em-vazio-valores-de-projeto-19">3.4. Análise SUT/EPS para Perdas em Vazio (Valores de Projeto)</h3>
<p><strong>Para níveis pu de 1.0, 1.1, 1.2 do DUT, usando tensões (V_teste_dut_lv_kv) e correntes (I_exc_dut_lv) de</strong>  <strong>projeto</strong> :</p>
<ul>
<li><code>V_target_sut_hv</code> <strong>(V):</strong>  <code>V_teste_dut_lv_kv * 1000</code>.</li>
<li><code>taps_sut_hv</code> <strong>(V): Array de</strong> <code>SUT_AT_MIN_VOLTAGE</code> <strong>a</strong> <code>SUT_AT_MAX_VOLTAGE</code> <strong>com passo</strong>  <code>SUT_AT_STEP_VOLTAGE</code>.</li>
<li><strong>Top 5 taps do SUT (</strong><code>V_sut_hv_tap</code> <strong>em V) mais próximos e adequados a</strong>  <code>V_target_sut_hv</code>.</li>
<li><strong>Para cada</strong>  <code>V_sut_hv_tap</code>:
<ul>
<li><code>ratio_sut</code>: <code>V_sut_hv_tap / SUT_BT_VOLTAGE</code></li>
<li><code>I_sut_lv</code> <strong>(A) (Corrente no EPS):</strong> <code>I_exc_dut_lv * ratio_sut</code></li>
<li><code>percent_limite</code> <strong>(%):</strong> <code>(I_sut_lv / EPS_CURRENT_LIMIT) * 100</code></li>
</ul>
</li>
</ul>
<h3 id="3.5.-exemplo-numérico-detalhado-de-perdas-em-vazio-20">3.5. Exemplo Numérico Detalhado de Perdas em Vazio</h3>
<p><strong>Dados de Entrada (UI e Transformador):</strong></p>
<ul>
<li><code>perdas_vazio_ui</code>: 10 kW</li>
<li><code>peso_nucleo_ui</code>: 5 Ton</li>
<li><code>corrente_excitacao_ui</code>: 0.5 %</li>
<li><code>inducao_ui</code>: 1.6 T</li>
<li><code>corrente_exc_1_1_ui</code>: 1.0 %</li>
<li><code>corrente_exc_1_2_ui</code>: 2.5 % (Exemplo, se fornecido)</li>
<li><code>frequencia</code>: 60 Hz</li>
<li><code>tensao_bt_kv</code> <strong>(DUT): 13.8 kV</strong></li>
<li><code>corrente_nominal_bt</code> <strong>(DUT): 418 A</strong></li>
<li><code>tipo_transformador</code>: Trifásico (<code>sqrt_3</code> <strong>= 1.732)</strong></li>
<li><code>SUT_BT_VOLTAGE</code>: 480 V = 0.48 kV</li>
<li><code>EPS_CURRENT_LIMIT</code>: 2000 A</li>
</ul>
<p><strong>Cálculos Baseados em Aço M4:</strong></p>
<ul>
<li><code>perdas_vazio</code> <strong>= 10 kW</strong></li>
<li><code>inducao</code> <strong>= 1.6 T</strong></li>
<li><code>inducao_arredondada</code> <strong>= 1.6 T</strong></li>
<li><code>frequencia_arredondada</code> <strong>= 60 Hz</strong></li>
<li><code>lookup_key</code> <strong>= (1.6, 60)</strong></li>
<li><code>fator_perdas</code> <strong>(da tabela</strong> <code>perdas_nucleo_data</code> <strong>para 1.6T @ 60Hz) = 1.30 W/kg</strong></li>
<li><code>fator_potencia_mag</code> <strong>(da tabela</strong> <code>potencia_magnet_data</code> <strong>para 1.6T @ 60Hz) = 1.60 VAR/kg</strong></li>
<li><code>peso_nucleo_calc</code> <strong>= 10 kW / 1.30 W/kg = 10000 W / 1.30 W/kg = 7692.31 kg = 7.692 Ton</strong></li>
<li><code>potencia_mag</code> <strong>= 1.60 VAR/kg * 7692.31 kg = 12307.69 VAR = 12.31 kVAR</strong></li>
<li><code>corrente_excitacao_calc</code> <strong>= 12.31 kVAR / (13.8 kV * 1.732) = 12.31 / 23.9016 = 0.515 A</strong></li>
<li><code>corrente_excitacao_percentual_calc</code> <strong>= (0.515 A / 418 A) * 100 = 0.123 %</strong></li>
<li><code>tensao_teste_1_1_kv</code> <strong>= 13.8 kV * 1.1 = 15.18 kV</strong></li>
<li><code>tensao_teste_1_2_kv</code> <strong>= 13.8 kV * 1.2 = 16.56 kV</strong></li>
<li><code>corrente_excitacao_1_1_calc</code> <strong>= 2 * 0.515 A = 1.03 A</strong></li>
<li><code>corrente_excitacao_1_2_calc</code> <strong>= 4 * 0.515 A = 2.06 A</strong></li>
<li><code>potencia_ensaio_1pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_bt_kv * corrente_excitacao_calc * 1.732</code> **= 12.31 kVA**</li>
<li><code>potencia_ensaio_1_1pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_teste_1_1_kv * corrente_excitacao_1_1_calc * 1.732</code> **= 27.08 kVA**</li>
<li><code>potencia_ensaio_1_2pu_calc_kva</code> <strong>(kVA):</strong> <code>tensao_teste_1_2_kv * corrente_excitacao_1_2_calc * 1.732</code> **= 59.12 kVA**</li>
</ul>
<p><strong>Cálculos Baseados em Dados de Projeto:</strong></p>
<ul>
<li><code>perdas_vazio</code> <strong>= 10 kW</strong></li>
<li><code>peso_nucleo</code> <strong>= 5 Ton</strong></li>
<li><code>corrente_excitacao_percentual</code> <strong>= 0.5 %</strong></li>
<li><code>corrente_exc_1_1_input</code> <strong>= 1.0 %</strong></li>
<li><code>corrente_exc_1_2_input</code> <strong>= 2.5 %</strong></li>
<li><code>fator_perdas_projeto</code> <strong>= (10 kW * 1000) / (5 Ton * 1000) = 2.0 W/kg (ou 10kW/5Ton = 2 kW/Ton)</strong></li>
<li><code>corrente_excitacao_projeto</code> <strong>= 418 A * (0.5 / 100.0) = 2.09 A</strong></li>
<li><code>potencia_ensaio_1pu_projeto_kva</code> <strong>(kVA):</strong> <code>13.8 kV * 2.09 A * 1.732</code> **= 49.87 kVA**</li>
<li><code>potencia_mag_projeto_kvar</code> <strong>= 49.87 kVAR</strong></li>
<li><code>fator_potencia_mag_projeto</code> <strong>= (49.87 kVAR * 1000) / (5 Ton * 1000) = 9.974 VAR/kg</strong></li>
<li><code>fator_excitacao_default</code> <strong>= 3</strong></li>
<li><code>corrente_excitacao_1_1</code> <strong>= 418 A * (1.0 / 100.0) = 4.18 A (pois</strong> <code>corrente_exc_1_1_input</code> <strong>foi fornecido)</strong></li>
<li><code>corrente_excitacao_1_2</code> <strong>= 418 A * (2.5 / 100.0) = 10.45 A (pois</strong> <code>corrente_exc_1_2_input</code> <strong>foi fornecido)</strong></li>
<li><code>potencia_ensaio_1_1pu_projeto_kva</code> <strong>(kVA):</strong> <code>15.18 kV * 4.18 A * 1.732</code> **= 109.93 kVA**</li>
<li><code>potencia_ensaio_1_2pu_projeto_kva</code> <strong>(kVA):</strong> <code>16.56 kV * 10.45 A * 1.732</code> **= 299.71 kVA**</li>
</ul>
<p><strong>Análise SUT/EPS para 1.0 pu do DUT (Projeto):</strong></p>
<ul>
<li><code>V_teste_dut_lv_kv</code> <strong>= 13.8 kV.</strong></li>
<li><code>I_exc_dut_lv</code> <strong>= 2.09 A.</strong></li>
<li><code>V_target_sut_hv</code> <strong>= 13800 V.</strong></li>
<li><code>taps_sut_hv</code> <strong>(V): [14000, 17000, 20000, ...]</strong></li>
<li><strong>Tap SUT mais próximo e adequado: 14000 V (</strong><code>V_sut_hv_tap</code> <strong>= 14000 V)</strong>
<ul>
<li><code>ratio_sut</code> <strong>= 14000 V / 480 V = 29.167</strong></li>
<li><code>I_sut_lv</code> <strong>= 2.09 A * 29.167 = 61.06 A</strong></li>
<li><code>percent_limite</code> <strong>= (61.06 A / 2000 A) * 100 = 3.05 %</strong></li>
</ul>
</li>
<li><strong>Outro Tap SUT (ex: 20000 V):</strong>
<ul>
<li><code>ratio_sut</code> <strong>= 20000 V / 480 V = 41.667</strong></li>
<li><code>I_sut_lv</code> <strong>= 2.09 A * 41.667 = 87.08 A</strong></li>
<li><code>percent_limite</code> <strong>= (87.08 A / 2000 A) * 100 = 4.35 %</strong></li>
</ul>
</li>
</ul>
<hr />
<h2 id="4.-perdas-em-carga-load-losses-21">4. Perdas em Carga (Load Losses)</h2>
<p>Cálculos das perdas nos enrolamentos do DUT sob carga.</p>
<h3 id="4.1-parâmetros-de-entrada-22">4.1. Parâmetros de Entrada</h3>
<table>
<thead>
<tr class="header">
<th><strong>Parâmetro</strong></th>
<th><strong>Descrição</strong></th>
<th><strong>Unidade</strong></th>
<th><strong>Variável Python (losses.py)</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><code>perdas_carga_nom_ui</code></td>
<td>Perdas totais em carga no tap Nominal @ Tref</td>
<td>kW</td>
<td><code>perdas_totais_nom_input</code></td>
</tr>
<tr class="even">
<td><code>perdas_carga_min_ui</code></td>
<td>Perdas totais em carga no tap Menor @ Tref</td>
<td>kW</td>
<td><code>perdas_totais_min_input</code></td>
</tr>
<tr class="odd">
<td><code>perdas_carga_max_ui</code></td>
<td>Perdas totais em carga no tap Maior @ Tref</td>
<td>kW</td>
<td><code>perdas_totais_max_input</code></td>
</tr>
<tr class="even">
<td><code>temperatura_referencia_ui</code></td>
<td>Temperatura de referência para as perdas em carga</td>
<td>°C</td>
<td><code>temperatura_ref</code></td>
</tr>
<tr class="odd">
<td><code>perdas_vazio_nom</code></td>
<td>Perdas em vazio nominais (do cálculo anterior)</td>
<td>kW</td>
<td><code>perdas_vazio_nom</code></td>
</tr>
<tr class="even">
<td><code>potencia</code> (DUT)</td>
<td>Potência nominal do DUT</td>
<td>MVA</td>
<td><code>potencia</code></td>
</tr>
<tr class="odd">
<td><code>tensao_nominal_at</code> (DUT)</td>
<td>Tensão nominal AT do DUT (Tap Nominal)</td>
<td>kV</td>
<td><code>tensao_nominal_at</code></td>
</tr>
<tr class="even">
<td>... (demais tensões e impedâncias dos taps do DUT) ...</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr class="odd">
<td><code>corrente_at_nom/min/max</code> (DUT)</td>
<td>Correntes AT nominais para os taps</td>
<td>A</td>
<td><code>corrente_at_nom</code>, etc.</td>
</tr>
</tbody>
</table>
<h3 id="4.2.-cenários-de-cálculo-23">4.2. Cenários de Cálculo</h3>
<p><strong>Realizados para taps Nominal, Menor, Maior do DUT:</strong></p>
<ul>
<li><strong>Energização a Elevação de temperatura condição a Frio (Total)</strong></li>
<li><strong>Condição a Elevação de temperatura condição a Quente (Perdas Carga nominal)</strong></li>
<li><strong>Condição a 25°C (Perdas Carga @ 25°C)</strong></li>
<li><strong>Sobrecarga 1.2 pu / 1.4 pu</strong> <strong>(se</strong>  <code>tensao_nominal_at >= 230kV</code> )</li>
</ul>
<h3 id="4.3.-variáveis-calculadas-por-tap-dut-e-por-cenário-de-ensaio-24">4.3. Variáveis Calculadas (Por Tap DUT e Por Cenário de Ensaio)</h3>
<p><strong>Para um tap DUT com</strong> <code>tensao</code> <strong>(kV),</strong> <code>corrente</code> <strong>(A),</strong> <code>imp</code> <strong>(%):</strong></p>
<ul>
<li><code>vcc</code> <strong>(kV) (Tensão de CC do tap):</strong> <code>tensao * (imp / 100.0)</code></li>
<li><code>perdas_carga_sem_vazio</code> <strong>(kW @</strong>  <code>temperatura_ref</code>): <code>perdas_totais_input_tap - perdas_vazio_nom</code></li>
<li><code>temp_factor</code>: <code>(235.0 + 25.0) / (235.0 + temperatura_ref)</code></li>
<li><code>perdas_cc_a_frio</code> <strong>(kW @ 25°C):</strong> <code>perdas_carga_sem_vazio * temp_factor</code></li>
</ul>
<p><strong>Para cada cenário de ensaio (Frio, Quente, 25C, Sobrecarga):</strong></p>
<ul>
<li><strong>Cálculo de</strong>  <code>tensao_ensaio</code>, <code>corrente_ensaio</code> <strong>(no DUT).</strong></li>
<li><code>pteste_..._kva</code> <strong>(kVA):</strong> <code>tensao_ensaio * corrente_ensaio * sqrt_3_factor</code></li>
<li><code>pteste_..._mva</code> <strong>(MVA):</strong> <code>pteste_..._kva / 1000.0</code></li>
<li><code>potencia_ativa_eps_..._kw</code> <strong>(kW): Potência ativa que o EPS deve fornecer ao DUT.</strong>
<ul>
<li><strong>Energização a Frio:</strong> <code>perdas_totais_input_tap</code></li>
<li><strong>Condição a Quente:</strong> <code>perdas_carga_sem_vazio</code></li>
<li><strong>Condição a 25°C:</strong> <code>perdas_cc_a_frio</code></li>
<li><strong>Sobrecarga (e.g., 1.2pu):</strong> <code>perdas_carga_sem_vazio * (1.2^2)</code></li>
</ul>
</li>
<li><code>pteste_..._mvar</code> <strong>(MVAr) (Reativo do DUT):</strong> <code>sqrt(max(0, pteste_... *kva^2 - potencia_ativa_eps* ..._kw^2)) / 1000.0</code></li>
</ul>
<h3 id="4.4.-nova-lógica-de-seleção-de-bancos-v>-e-v≤-25">4.4. Nova Lógica de Seleção de Bancos V> e V≤</h3>
<p><strong>FATOR V> = FACTOR_CAP_BANC_OVERVOLTAGE = 1.1</strong></p>
<p><strong>Lógica de Seleção:</strong>
-   <strong>V≤</strong>: Sempre a menor tensão disponível ≥ tensão de teste (tensão teste menor ou igual ao banco)
-   <strong>V></strong>: Existe apenas se tensão_teste ≤ tensão_menor_próxima × FACTOR_CAP_BANC_OVERVOLTAGE (tensão teste maior, banco com sobretensão)
    -   Se existir, usa a tensão menor próxima
-   <strong>Quando V> existe</strong>: Calcular AMBOS V≤ e V>
-   <strong>Quando V> não existe</strong>: Calcular apenas V≤</p>
<p><strong>Exemplo:</strong>
-   Tensão teste: 15 kV
-   Tensões disponíveis: [13.8, 23.9, 27.6, ...] kV
-   Verificação: 15 ≤ 13.8 × 1.1 = 15.18 ✅
-   Resultado: V> EXISTE usando 13.8 kV, V≤ usa 23.9 kV</p>
<h3 id="4.5.-cálculo-do-banco-de-capacitores-requerido-calculate_cap_bank-26">4.5. Cálculo do Banco de Capacitores <strong>Requerido</strong> <strong>(calculate_cap_bank)</strong></h3>
<ul>
<li><strong>Entradas:</strong> <code>voltage</code> <strong>(kV, e.g.,</strong>  <code>tensao_frio</code>), <code>power</code> <strong>(MVA, e.g.,</strong> <code>pteste_frio_mva</code> <strong>- potência aparente do DUT no ensaio).</strong></li>
<li><strong>Saídas (para V> e V≤):</strong> <code>cap_bank_voltage_..._fator</code> <strong>(kV, tensão nominal do banco selecionado) e</strong> <code>pot_cap_bank_..._fator</code> <strong>(MVAr, potência nominal que este banco precisaria ter para compensar a potência reativa do DUT na tensão de ensaio).</strong></li>
<li><strong>CORREÇÃO IMPLEMENTADA:</strong> <strong>A função agora usa corretamente a componente reativa</strong> <code>pteste_..._mvar</code> <strong>como base para dimensionar o banco para compensação reativa.</strong>
<ul>
<li><strong>Fórmula para potência nominal do banco de capacitores:</strong>
        <p><span class="math display">\[ Q_{requerida\_banco\_nominal} = Q_{DUT} \times \left( \frac{V_{banco\_nominal}}{V_{ensaio}} \right)^2 \]</span>
        (Onde <span class="math inline">\( Q_{DUT} \)</span> é a potência reativa do DUT na condição de ensaio, e <span class="math inline">\( V_{ensaio} \)</span> é a tensão de ensaio do DUT).</p>
</li>
<li><code>i_requerida_banco_nominal S/S</code> <strong>(A) (Corrente necessária do banco de capacitores):</strong> <code>(Q_requerida_banco_nominal * 1000) / (V_ensaio * sqrt_3_factor)</code></li>
<li><code>i_requerida_banco_nominal C/S</code> <strong>(A) (Corrente necessária do banco de capacitores):</strong> <code>(Q_requerida_banco_nominal * 1000) / (V_ensaio * sqrt_3_factor)</code></li>
</ul>
</li>
</ul>
<h3 id="4.6.-configuração-do-banco-de-capacitores-disponívelsugerido-27">4.6. Configuração do Banco de Capacitores (Disponível/Sugerido)</h3>
<p><strong>Detalhado na Seção 5. Para cada cenário, após calcular o</strong> <code>Q Power Provided ... (MVAr)</code> <strong>(potência efetiva do banco configurado), este valor é usado na compensação.</strong></p>
<h3 id="4.7.-análise-suteps-para-perdas-em-carga-corrente-compensada-28">4.7. Análise SUT/EPS para Perdas em Carga (Corrente Compensada)</h3>
<p><strong>Função</strong>  <code>calculate_sut_eps_current_compensated</code>:</p>
<ul>
<li><strong>Entradas:</strong>
<ul>
<li><code>tensao_ref_dut_kv</code>: e.g., <code>tensao_frio</code></li>
<li><code>corrente_ref_dut_a</code>: e.g., <code>corrente_frio</code> <strong>(corrente total do DUT no ensaio)</strong></li>
<li><code>q_power_scenario_v≤/v>_mvar</code>: Potência <strong>fornecida</strong> <strong>pelo banco V≤ ou V> (e.g.,</strong>  <code>Q Power Provided Frio V≤ (MVAr)</code>), na sua tensão nominal.</li>
<li><code>cap_bank_voltage_scenario_v≤/v>_kv</code>: Tensão <strong>nominal</strong> <strong>do banco V≤ ou V>.</strong></li>
<li><strong>... (outros parâmetros SUT).</strong></li>
</ul>
</li>
<li><strong>Cálculos (exemplo para V≤):</strong>
<ul>
<li><code>ratio_sut</code>:  <code>V_sut_hv_tap_v / tensao_sut_bt_v</code>.</li>
<li><code>I_dut_reflected</code> <strong>(A) =</strong> <code>corrente_ref_dut_a * ratio_sut</code> <strong>(Corrente</strong> <strong>total</strong> <strong>do DUT refletida para BT SUT).</strong></li>
<li><code>pteste_mvar_corrected_sf</code> <strong>(MVAr):</strong>
        <p><span class="math display">\[ Q_{banco\_efetiva} = Q_{banco\_fornecida\_nominal} \times \left( \frac{V_{ensaio\_DUT}}{V_{banco\_nominal}} \right)^2 \]</span></p>
</li>
<li><code>I_cap_base_sf</code> <strong>(A) (Corrente capacitiva no lado DUT):</strong>
        <p><span class="math display">\[ I_{cap\_base} = \frac{Q_{banco\_efetiva} \times 1000}{V_{ensaio\_DUT} \times \sqrt{3}} \]</span>
        (para trifásicos)</p>
</li>
<li><code>I_cap_adjustment_sf</code> <strong>(A) (Corrente capacitiva refletida para BT SUT):</strong> <code>I_cap_base_sf * ratio_sut</code></li>
<li><code>I_eps_sf_net</code> <strong>(A) (Corrente no EPS):</strong>  <code>I_dut_reflected - I_cap_adjustment_sf</code>. <strong>Esta é uma subtração escalar direta, o que implica que</strong> <code>I_dut_reflected</code> <strong>é tratada como puramente reativa ou que as fases são tais que a subtração escalar é uma aproximação válida. Uma análise vetorial completa seria mais precisa, onde as componentes ativa (<span class="math inline">\( I_{Pa} \)</span>) e reativa (<span class="math inline">\( I_{Pr} \)</span>) da corrente do DUT seriam consideradas.</strong></li>
<li><code>percent_limite_sf</code> <strong>(%):</strong>  <code>(abs(I_eps_sf_net) / EPS_CURRENT_LIMIT) * 100</code>.</li>
</ul>
</li>
</ul>
<h3 id="4.8.-exemplo-numérico-detalhado-de-perdas-em-carga-29">4.8. Exemplo Numérico Detalhado de Perdas em Carga</h3>
<p><strong>Dados Iniciais:</strong></p>
<ul>
<li><strong>DUT Trifásico: Potência = 10 MVA, Tensão AT (Nominal) = 69 kV, Taps AT: Menor 65kV, Maior 72kV.</strong></li>
<li><strong>Impedâncias (%): Nominal 8%, Menor 7.5%, Maior 8.5%.</strong></li>
<li><strong>Entrada UI:</strong>  <code>perdas_carga_nom_ui</code>=80kW,  <code>perdas_carga_min_ui</code>=75kW,  <code>perdas_carga_max_ui</code>=85kW.  <code>temperatura_referencia_ui</code>=85°C.</li>
<li><strong>Calculado:</strong> <code>perdas_vazio_nom</code> <strong>= 12 kW.</strong></li>
<li><strong>SUT:</strong>  <code>SUT_BT_VOLTAGE</code>=480V.  <code>EPS_CURRENT_LIMIT</code>=2000A.</li>
</ul>
<p><strong>Cálculos para TAP NOMINAL (69 kV):</strong></p>
<ul>
<li><code>tensao</code>=69kV,  <code>imp</code>=8%,  <code>perdas_totais_input_tap</code>=80kW.</li>
<li><code>corrente_at_nom</code> <strong>= (10000 kVA) / (69 kV * 1.732) = 83.67 A.</strong></li>
<li><code>vcc</code> <strong>= 69 kV * (8/100) = 5.52 kV.</strong></li>
<li><code>perdas_carga_sem_vazio</code> <strong>(@85°C) = 80 kW - 12 kW = 68 kW.</strong></li>
<li><code>temp_factor</code> <strong>= (235+25) / (235+85) = 260 / 320 = 0.8125.</strong></li>
<li><code>perdas_cc_a_frio</code> <strong>(@25°C) = 68 kW * 0.8125 = 55.25 kW.</strong></li>
</ul>
<p><strong>Cenário: Energização a Frio (Tap Nominal DUT):</strong></p>
<ul>
<li><code>frio_ratio_sqrt</code> <strong>= sqrt(80 kW / 55.25 kW) = sqrt(1.4479) = 1.2033.</strong></li>
<li><code>tensao_frio</code> <strong>= 1.2033 * 5.52 kV = 6.642 kV (Tensão ensaio DUT).</strong></li>
<li><code>corrente_frio</code> <strong>= 1.2033 * 83.67 A = 100.68 A (Corrente ensaio DUT).</strong></li>
<li><code>pteste_frio_kva</code> <strong>= 6.642 kV * 100.68 A * 1.732 = 1158.2 kVA.</strong></li>
<li><code>pteste_frio_mva</code> <strong>= 1.1582 MVA.</strong></li>
<li><code>potencia_ativa_eps_frio_kw</code> <strong>= 80 kW.</strong></li>
<li><code>pteste_frio_mvar</code> <strong>= sqrt(1158.2^2 - 80^2)/1000 = sqrt(1341427 - 6400)/1000 = 1.1554 MVAr (Reativo DUT).</strong></li>
</ul>
<p><strong>Banco de Capacitores</strong> <strong>Requerido</strong> <strong>(Cenário Frio, V>, Tap Nominal):</strong></p>
<ul>
<li><code>voltage</code> <strong>= 6.642 kV (ensaio DUT).</strong></li>
<li><strong>Potência Reativa a Compensar</strong> <strong>= 1.1554 MVAr.</strong></li>
<li><code>select_target_bank_voltage(6.642)</code> <strong>-></strong>  <code>target_v_cf_key</code>="13.8" (Assumindo 6.642 <= 13.8 * FACTOR_CAP_BANC_OVERVOLTAGE). <code>cap_bank_voltage_com_fator</code> <strong>= 13.8 kV.</strong>
<strong>NOVA LÓGICA V>: Como 6.642 kV ≤ 13.8 kV × 1.1 = 15.18 kV, V> EXISTE e usa 13.8kV</strong></li>
<li><code>pot_cap_bank_com_fator</code> <strong>(MVAr requerido do banco) = 1.1554 MVAr</strong> <span class="math inline">\( \times \left( \frac{13.8}{6.642} \right)^2 \)</span> <strong>= 1.1554</strong> <span class="math inline">\( \times \)</span> <strong>(2.0776)</strong><span class="math inline">\( ^2 \)</span> <strong>= 1.1554</strong> <span class="math inline">\( \times \)</span> <strong>4.316 = 4.989 MVAr.</strong>
<ul>
<li><strong>Este é</strong>  <code>res_dict["Cap Bank Power Frio Com Fator (MVAr)"]</code>.</li>
</ul>
</li>
</ul>
<p><strong>Seleção Configuração Banco (Cenário Frio, V>, Tap Nominal):</strong></p>
<ul>
<li><code>target_bank_voltage_key</code> <strong>= "13.8".</strong> <code>required_power_mvar</code> <strong>= 4.989 MVAr.</strong></li>
<li><strong>Grupo 1 para 13.8kV: 3 unidades, máx 11.7 MVAr.</strong> <code>use_group1_only</code> <strong>= True.</strong></li>
<li><code>available_caps</code> <strong>= 3 unidades (CP2A1, CP2B1, CP2C1).</strong></li>
<li><code>find_best_q_configuration("13.8", 4.989, True)</code>:</li>
<li><strong>Necessário ~1.663 MVAr/unidade (4.989 / 3).</strong></li>
<li><strong>Q5 (1.6 MVAr) é menor. Q4+Q1 (1.2+0.1=1.3) é menor. Q3+Q4 (0.8+1.2=2.0) é > 1.663.</strong></li>
<li><strong>Combinação "Q3, Q4" (2.0 MVAr/unidade).</strong></li>
<li><strong>Potência total fornecida pelo banco = 2.0 MVAr/unidade * 3 unidades = 6.0 MVAr.</strong></li>
<li><code>res_dict["Q Config Frio"]</code> <strong>= "Q3, Q4".</strong></li>
<li><code>res_dict["Q Power Provided Frio (MVAr)"]</code> <strong>= 6.0 MVAr.</strong></li>
<li><code>res_dict["Cap Bank Voltage Frio Com Fator (kV)"]</code> <strong>= 13.8 kV.</strong></li>
</ul>
<p><strong>Análise SUT/EPS (Cenário Frio, V>, Tap Nominal DUT, Tap SUT AT = 14kV):</strong></p>
<ul>
<li><code>tensao_ref_dut_kv</code> <strong>= 6.642 kV.</strong></li>
<li><code>corrente_ref_dut_a</code> <strong>= 100.68 A.</strong></li>
<li><code>q_power_scenario_v>_mvar</code> <strong>= 6.0 MVAr (Potência</strong> <strong>fornecida</strong> <strong>pelo banco 13.8kV).</strong></li>
<li><code>cap_bank_voltage_scenario_v>_kv</code> <strong>= 13.8 kV (Tensão</strong> <strong>nominal</strong> <strong>do banco).</strong></li>
<li><code>V_sut_hv_tap_v</code> <strong>= 14000 V.</strong> <code>tensao_sut_bt_v</code> <strong>= 480 V.</strong></li>
<li><strong>Cálculos:</strong>
<ul>
<li><code>ratio_sut</code> <strong>= 14000 / 480 = 29.167.</strong></li>
<li><code>I_dut_reflected</code> <strong>= 100.68 A * 29.167 = 2936.5 A.</strong></li>
<li><code>pteste_mvar_corrected_cf</code> <strong>= 6.0 MVAr</strong> <span class="math inline">\( \times \left( \frac{6.642}{13.8} \right)^2 \)</span> <strong>= 6.0</strong> <span class="math inline">\( \times \)</span> <strong>0.2319 = 1.391 MVAr.</strong></li>
<li><code>I_cap_base_cf</code> <strong>= (1.391 * 1000) / (6.642 * 1.732) = 120.85 A.</strong></li>
<li><code>I_cap_adjustment_cf</code> <strong>= 120.85 A * 29.167 = 3524.8 A.</strong></li>
<li><code>I_eps_cf_net</code> <strong>= 2936.5 A - 3524.8 A = -588.3 A.</strong></li>
<li><code>percent_limite_cf</code> <strong>= (588.3 / 2000) * 100 * (-1) = -29.4 % (Excesso de compensação).</strong></li>
</ul>
</li>
</ul>
<hr />
<h2 id="5.-configuração-detalhada-dos-bancos-de-capacitores-30">5. Configuração Detalhada dos Bancos de Capacitores</h2>
<h3 id="5.1.-capacitores-disponíveis-por-nível-de-tensão-nominal-do-banco-31">5.1. Capacitores Disponíveis por Nível de Tensão Nominal do Banco</h3>
<p><strong>Conforme</strong>  <code>utils.constants.CAPACITORS_BY_VOLTAGE</code>:</p>
<table>
<thead>
<tr class="header">
<th><strong>Tensão Nominal Banco (kV)</strong></th>
<th><strong>Capacitores Físicos Disponíveis (CAPACITORS_BY_VOLTAGE[chave])</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>"13.8"</td>
<td>["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"]</td>
</tr>
<tr class="even">
<td>"23.9"</td>
<td>["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"]</td>
</tr>
<tr class="odd">
<td>"27.6"</td>
<td>["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
<tr class="even">
<td>"41.4"</td>
<td>["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
<tr class="odd">
<td>"47.8"</td>
<td>["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
<tr class="even">
<td>"55.2"</td>
<td>["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
<tr class="odd">
<td>"71.7"</td>
<td>["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
<tr class="even">
<td>"95.6"</td>
<td>["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"]</td>
</tr>
</tbody>
</table>
<p><strong>Nomenclatura</strong>  <code>CPxAy</code>:  <code>x</code>=posição (1-4),  <code>A</code>=Fase (A,B,C),  <code>y</code>=Grupo (1 ou 2).</p>
<h3 id="5.2.-potência-das-chaves-q-por-unidade-de-capacitor-32">5.2. Potência das Chaves Q por Unidade de Capacitor</h3>
<p><strong>Conforme</strong>  <code>utils.constants.Q_SWITCH_POWERS["generic_cp"]</code>:</p>
<table>
<thead>
<tr class="header">
<th><strong>Chave</strong></th>
<th><strong>Potência (MVAr)</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Q1</td>
<td>0.1</td>
</tr>
<tr class="even">
<td>Q2</td>
<td>0.2</td>
</tr>
<tr class="odd">
<td>Q3</td>
<td>0.8</td>
</tr>
<tr class="even">
<td>Q4</td>
<td>1.2</td>
</tr>
<tr class="odd">
<td>Q5</td>
<td>1.6</td>
</tr>
<tr class="even">
<td>Total (Q1-Q5)</td>
<td>3.9</td>
</tr>
</tbody>
</table>
<h3 id="5.3.-lógica-de-seleção-implementada-33">5.3. Lógica de Seleção Implementada</h3>
<p><strong>A lógica em</strong> <code>losses.py</code> <strong>(find_best_q_configuration,</strong>  <code>suggest_capacitor_bank_config</code>) opera como descrito na Seção 2.5.2 e 2.5.3. A potência fornecida por uma combinação de chaves Q em um conjunto de <code>available_caps</code> <strong>(unidades monofásicas) é</strong>  <code>sum(Q_potencias_individuais) * len(available_caps)</code>.</p>
<h3 id="5.4.-potências-mínima-e-máxima-teóricas-por-nível-de-tensão-continuação-34">5.4. Potências Mínima e Máxima Teóricas por Nível de Tensão (Continuação)</h3>
<p><strong>Estas são as potências trifásicas totais que podem ser obtidas, considerando o uso apenas do Grupo 1 de capacitores (CPxy</strong>1**) ou de todos os capacitores disponíveis (Grupo 1 + Grupo 2, i.e., CPxy <strong>1 + CPxy2</strong>), aplicando a mesma configuração de chaves Q (Q1 para mínima, Q1 a Q5 para máxima) em todas as unidades monofásicas selecionadas.**</p>
<ul>
<li><strong>13.8 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["13.8"]</code> <strong>= 6 unidades no total, 3 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=3):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 3 unid =</strong> <strong>0.3 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 3 unid =</strong> <strong>11.7 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=6):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 6 unid =</strong> <strong>0.6 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 6 unid =</strong> <strong>23.4 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>23.9 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["23.9"]</code> <strong>= 6 unidades no total, 3 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=3):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 3 unid =</strong> <strong>0.3 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 3 unid =</strong> <strong>11.7 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=6):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 6 unid =</strong> <strong>0.6 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 6 unid =</strong> <strong>23.4 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>27.6 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["27.6"]</code> <strong>= 24 unidades no total, 12 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=12):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 12 unid =</strong> <strong>1.2 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 12 unid =</strong> <strong>46.8 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=24):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 24 unid =</strong> <strong>2.4 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 24 unid =</strong> <strong>93.6 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>41.4 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["41.4"]</code> <strong>= 18 unidades no total, 9 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=9):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 9 unid =</strong> <strong>0.9 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 9 unid =</strong> <strong>35.1 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=18):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 18 unid =</strong> <strong>1.8 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 18 unid =</strong> <strong>70.2 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>47.8 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["47.8"]</code> <strong>= 24 unidades no total, 12 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=12):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 12 unid =</strong> <strong>1.2 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 12 unid =</strong> <strong>46.8 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=24):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 24 unid =</strong> <strong>2.4 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 24 unid =</strong> <strong>93.6 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>55.2 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["55.2"]</code> <strong>= 24 unidades no total, 12 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=12):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 12 unid =</strong> <strong>1.2 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 12 unid =</strong> <strong>46.8 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=24):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 24 unid =</strong> <strong>2.4 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 24 unid =</strong> <strong>93.6 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>71.7 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["71.7"]</code> <strong>= 18 unidades no total, 9 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=9):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 9 unid =</strong> <strong>0.9 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 9 unid =</strong> <strong>35.1 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=18):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 18 unid =</strong> <strong>1.8 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 18 unid =</strong> <strong>70.2 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
<li><strong>95.6 kV</strong> <strong>(</strong><code>CAPACITORS_BY_VOLTAGE["95.6"]</code> <strong>= 24 unidades no total, 12 unidades no Grupo 1)</strong>
<ul>
<li><strong>Grupo 1 Apenas (len(available_caps)=12):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 12 unid =</strong> <strong>1.2 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 12 unid =</strong> <strong>46.8 MVAr</strong></li>
</ul>
</li>
<li><strong>Grupo 1 + Grupo 2 (len(available_caps)=24):</strong>
<ul>
<li><strong>Mínima (Q1): 0.1 MVAr/unid * 24 unid =</strong> <strong>2.4 MVAr</strong></li>
<li><strong>Máxima (Q1-Q5): 3.9 MVAr/unid * 24 unid =</strong> <strong>93.6 MVAr</strong></li>
</ul>
</li>
</ul>
</li>
</ul>
<p>**(Nota: "Unidade" refere-se a uma unidade monofásica de capacitor. Para um sistema trifásico,** <code>len(available_caps)</code> **será um múltiplo de 3 se os capacitores estiverem distribuídos igualmente entre as fases, como é o caso na constante**  <code>CAPACITORS_BY_VOLTAGE</code>.)</p>
<h3 id="5.5.-considerações-para-seleção-avançada-de-capacitores-lógica-desejada-vs.-implementada-35">5.5. Considerações para Seleção Avançada de Capacitores (Lógica Desejada vs. Implementada)</h3>
<p><strong>A solicitação original incluía uma lógica mais complexa para seleção de capacitores, com apresentação de múltiplas opções (exata, acima, abaixo) e a possibilidade de configurações desequilibradas se aceito pelo usuário.</strong></p>
<p><strong>Lógica Desejada (Resumo da Solicitação):</strong></p>
<ul>
<li><strong>Receber potência desejada e nível de tensão.</strong></li>
<li><strong>Verificar se a potência desejada está dentro do intervalo mínimo/máximo possível para o nível de tensão.</strong></li>
<li><strong>Identificar os grupos de capacitores (Grupo 1: CPxy1; Grupo 1+2: CPxy1 + CPxy2).</strong></li>
<li><strong>Priorizar Grupo 1:</strong> <strong>Se a potência desejada puder ser alcançada apenas com o Grupo 1, usar este grupo.</strong></li>
<li><strong>Senão, usar Grupo 1+2.</strong></li>
<li><strong>Método Equilibrado:</strong>
<ul>
<li><strong>Gerar todas as 31 combinações de chaves Q.</strong></li>
<li><strong>Para cada combinação, calcular a potência total por unidade e multiplicar pelo número de unidades</strong> <strong>por fase</strong> <strong>(assumindo equilíbrio).</strong></li>
<li><strong>Ordenar as potências trifásicas resultantes.</strong></li>
<li><strong>Selecionar opções: Exata (se houver), melhor acima, melhor abaixo.</strong></li>
</ul>
</li>
<li><strong>Método Desequilibrado (Opcional):</strong> <strong>Se o método equilibrado não fornecer uma solução exata ou satisfatória, e o usuário permitir, considerar configurações onde diferentes unidades de capacitor (mesmo dentro da mesma fase, ou entre fases se o número de unidades por fase for diferente) podem ter diferentes combinações de Q. Isso é significativamente mais complexo.</strong></li>
<li><strong>Apresentar opções ao usuário.</strong></li>
</ul>
<p><strong>Comparação com a Lógica Implementada em</strong>  <code>losses.py</code>:</p>
<ul>
<li><strong>Verificação de Intervalo:</strong> <strong>Não é feita explicitamente no início;</strong> <code>find_best_q_configuration</code> <strong>retorna "N/A" se não encontrar solução.</strong></li>
<li><strong>Priorização Grupo 1:</strong> <strong>Implementada através do parâmetro</strong> <code>use_group1_only</code> <strong>passado para</strong>  <code>find_best_q_configuration</code>.</li>
<li><strong>Método Equilibrado:</strong> <strong>A função</strong> <code>find_best_q_configuration</code> <strong>aplica a</strong> <strong>mesma</strong> <strong>combinação de chaves Q a</strong> <strong>todas</strong> <strong>as</strong> <code>available_caps</code> <strong>selecionadas. Se</strong> <code>available_caps</code> <strong>representa um conjunto equilibrado de unidades por fase, o resultado é equilibrado.</strong></li>
<li><strong>Seleção de Opções:</strong> <strong>Apenas a "melhor acima ou igual" é retornada. Não há opções "exata" ou "melhor abaixo" distintas.</strong></li>
<li><strong>Método Desequilibrado:</strong> <strong>Não implementado.</strong></li>
</ul>
<p><strong>A lógica atual é uma simplificação funcional do algoritmo mais detalhado solicitado. A implementação de um método desequilibrado e a apresentação de múltiplas opções exigiriam uma refatoração significativa das funções de busca e seleção de configuração do banco.</strong></p>
<hr />
<h2 id="6.-considerações-finais-sobre-fórmulas-e-implementação-36">6. Considerações Finais sobre Fórmulas e Implementação</h2>
<ul>
<li><strong>Unidades:</strong> <strong>É crucial a consistência nas unidades (kW vs W, Ton vs kg, kV vs V) ao longo dos cálculos. O</strong> <code>formulas_perdas.md</code> <strong>tenta esclarecer as unidades usadas em cada etapa conforme interpretado do código.</strong></li>
<li><strong>Compensação de Corrente (calculate_sut_eps_current_compensated):</strong> <strong>A subtração escalar direta da corrente do capacitor da corrente refletida do DUT é uma aproximação. Uma análise vetorial completa, considerando as componentes ativa e reativa da corrente do DUT, seria mais precisa, especialmente se o fator de potência do ensaio não for muito baixo.</strong></li>
<li><strong>Dimensionamento do Banco (calculate_cap_bank):</strong> <strong>A função</strong> <code>calculate_cap_bank</code> <strong>recebe a potência</strong> <strong>aparente</strong> <strong>(<span class="math inline">\(S_{DUT}\)</span>) do ensaio no DUT como entrada</strong> <code>power</code>. Para dimensionar um banco para compensação <strong>reativa</strong>, a entrada <code>power</code> <strong>para esta função deveria ser a potência</strong> <strong>reativa</strong> <strong>(<span class="math inline">\(Q_{DUT}\)</span>) do ensaio (e.g.,</strong> <code>pteste_frio_mvar</code>). O resultado (<code>pot_cap_bank_..._fator</code>) seria então a potência nominal reativa que o banco selecionado precisaria ter. O código atual, ao passar <span class="math inline">\(S_{DUT}\)</span>, pode estar calculando uma potência de banco nominal que compensaria a potência aparente total, o que não é o objetivo usual da compensação por bancos de capacitores. Esta é uma área potencial para revisão no código para garantir que o banco seja dimensionado para compensar <span class="math inline">\(Q_{DUT}\)</span>.</li>
</ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to generate TOC and add IDs to headings in the actual document
        function generateTOC(htmlContent) { // Renamed parameter to avoid confusion
            const container = document.createElement('div');
            container.innerHTML = htmlContent; // Use a temporary div to parse the HTML string

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const toc = document.getElementById('toc');
            toc.innerHTML = ''; // Clear existing TOC

            const contentDiv = document.getElementById('markdown-content'); // Get the actual content div
            const actualHeadings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

            // Map headings from the temporary container to the actual DOM to ensure consistent IDs
            const headingMap = new Map();
            headings.forEach((heading, index) => {
                const headingText = heading.textContent.trim();
                const slug = headingText
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special chars
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen
                const uniqueId = slug ? `${slug}-${index}` : `heading-${index}`;
                heading.id = uniqueId; // Set ID on temporary heading
                if (actualHeadings[index]) {
                    actualHeadings[index].id = uniqueId; // Set ID on actual DOM heading
                }
                headingMap.set(heading, uniqueId);
            });

            // Now build the TOC with links to the actual headings
            headings.forEach((heading) => {
                const level = parseInt(heading.tagName.substring(1));
                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;

                const link = document.createElement('a');
                link.href = `#${heading.id}`;
                link.textContent = heading.textContent;
                link.setAttribute('data-heading-id', heading.id);

                // Add click handler to ensure smooth scrolling and proper focus
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-heading-id');
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        targetElement.setAttribute('tabindex', '-1');
                        targetElement.focus();
                        history.pushState(null, null, `#${targetId}`);

                        document.querySelectorAll('.toc a').forEach(a => {
                            a.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }

        // Function to highlight search terms
        function highlightSearchTerms(content, term) {
            if (!term) return content;

            // Use a temporary span to avoid interfering with existing HTML structure
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;

            // Iterate over text nodes to avoid breaking HTML tags
            const walk = document.createTreeWalker(tempDiv, NodeFilter.SHOW_TEXT, null, false);
            let node;
            const textNodes = [];
            while (node = walk.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const parentTag = textNode.parentNode.tagName;
                // Avoid highlighting within code tags or already highlighted spans
                if (parentTag !== 'CODE' && parentTag !== 'PRE' && parentTag !== 'SPAN') {
                    const originalText = textNode.nodeValue;
                    const regex = new RegExp(`(${term})`, 'gi');
                    const newText = originalText.replace(regex, '<span class="highlight">$1</span>');
                    if (newText !== originalText) {
                        const newSpan = document.createElement('span');
                        newSpan.innerHTML = newText;
                        textNode.parentNode.replaceChild(newSpan, textNode);
                    }
                }
            });

            return tempDiv.innerHTML;
        }

        // Function to handle search and re-highlight
        function applySearchAndHighlight() {
            const contentDiv = document.getElementById('markdown-content');
            let currentContentHTML = contentDiv.innerHTML;
            const searchTerm = document.getElementById('search-input').value.trim();

            // First, remove any previous highlights to re-highlight cleanly
            currentContentHTML = currentContentHTML.replace(/<span class="highlight">([^<]+)<\/span>/g, '$1');
            
            // Apply new highlights if a search term exists
            if (searchTerm) {
                contentDiv.innerHTML = highlightSearchTerms(currentContentHTML, searchTerm);
            } else {
                contentDiv.innerHTML = currentContentHTML; // Just remove highlights if search term is empty
            }

            // After potentially modifying the content, re-render MathJax
            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            // And ensure code blocks are still highlighted (should be fine for static code blocks)
            hljs.highlightAll(); // Re-run for safety, or target specific elements if performance is an issue
        }

        // Initialize on DOMContentLoaded
        document.addEventListener('DOMContentLoaded', () => {
            const contentDiv = document.getElementById('markdown-content');

            // Initial highlighting for code blocks
            hljs.highlightAll();

            // Initial generation of TOC
            generateTOC(contentDiv.innerHTML);

            // Initial rendering of MathJax expressions
            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            // Check if there's a hash in the URL and scroll to it after rendering
            setTimeout(() => {
                if (window.location.hash) {
                    const hash = window.location.hash.substring(1);
                    const targetElement = document.getElementById(hash);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Highlight the corresponding TOC item
                        const tocLink = document.querySelector(`.toc a[data-heading-id="${hash}"]`);
                        if (tocLink) {
                            document.querySelectorAll('.toc a').forEach(a => {
                                a.classList.remove('active');
                            });
                            tocLink.classList.add('active');
                        }
                    }
                }
            }, 500); // Small delay to ensure rendering is complete

            // Set up search
            document.getElementById('search-input').addEventListener('input', (e) => {
                applySearchAndHighlight();

                // Scroll to first highlight if there is one
                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    </script>
</body>
</html>