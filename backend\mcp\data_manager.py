# backend/mcp/data_manager.py
import json
import os
import sqlite3
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone, timedelta
import threading
import requests # Importação crucial

try:
    from ..models.test_data import (
        TestDataSerializer, HistorySessionData, LossesResult,
        InducedVoltageResult, AppliedVoltageResult, validate_losses_data,
        validate_induced_voltage_data, validate_applied_voltage_data
    )
except ImportError:
    # Fallback for when models are not available
    TestDataSerializer = None
    HistorySessionData = None

class MCPDataManager:
    @staticmethod
    def get_local_timestamp():
        """Retorna timestamp local do Brasil (UTC-3)"""
        # Fuso horário do Brasil (UTC-3)
        brazil_tz = timezone(timedelta(hours=-3))
        return datetime.now(brazil_tz).strftime('%Y-%m-%d %H:%M:%S')

    def _migrate_legacy_data(self, stores_data: dict) -> dict:
        """
        Migra dados antigos para garantir compatibilidade com novas versões.
        """
        try:
            # Importar função de migração do losses_service
            from services.losses_service import migrate_legacy_sut_data

            migrated_stores = {}
            for store_id, store_data in stores_data.items():
                if store_id in ['noLoadLossesResults', 'loadLossesResults']:
                    # Aplicar migração para dados de perdas
                    migrated_stores[store_id] = migrate_legacy_sut_data(store_data)
                    print(f"[MCPDataManager] Migração aplicada para store '{store_id}'")
                elif store_id == 'transformerInputs':
                    # Aplicar migração para dados de entrada do transformador
                    migrated_stores[store_id] = self._migrate_transformer_inputs_data(store_data)
                    print(f"[MCPDataManager] Migração aplicada para store '{store_id}'")
                else:
                    # Manter outros stores sem alteração
                    migrated_stores[store_id] = store_data

            return migrated_stores

        except Exception as e:
            print(f"[MCPDataManager] Erro na migração de dados: {e}")
            # Em caso de erro, retornar dados originais
            return stores_data

    def _migrate_transformer_inputs_data(self, store_data: dict) -> dict:
        """
        Migra dados de entrada do transformador para garantir compatibilidade com grupos vetoriais.
        """
        try:
            migrated_data = store_data.copy()

            # Verificar se há dados de formulário para migrar
            if 'formData' in migrated_data:
                form_data = migrated_data['formData'].copy()

                # Migração de grupos vetoriais antigos
                if 'grupo_ligacao' in form_data:
                    grupo_ligacao = form_data['grupo_ligacao']

                    # Mapeamento de grupos antigos para novos (se necessário)
                    legacy_group_mapping = {
                        # Mapeamentos específicos se houver mudanças de nomenclatura
                        "Znyn0": "ZNyn0",
                        "Znynd11": "ZNd11",
                        # Adicionar outros mapeamentos conforme necessário
                    }

                    # Aplicar mapeamento se o grupo estiver na lista de migração
                    if grupo_ligacao in legacy_group_mapping:
                        form_data['grupo_ligacao'] = legacy_group_mapping[grupo_ligacao]
                        print(f"[MCPDataManager] Grupo vetorial migrado: {grupo_ligacao} -> {legacy_group_mapping[grupo_ligacao]}")

                # Migração de tipos de transformador se necessário
                if 'tipo_transformador' in form_data:
                    tipo_transformador = form_data['tipo_transformador']

                    # Mapeamento de tipos antigos para novos (se necessário)
                    legacy_type_mapping = {
                        "trifasico": "Trifásico",
                        "monofasico": "Monofásico",
                        "autotransformador": "Autotransformador",
                        "trifasico_terciario": "Trifásico",
                        "trifasico_neutro": "Trifásico",
                        "monofasico_zigzag": "Monofásico",
                        "trifasico_terciario_neutro": "Trifásico",
                        "trifasico_neutro_terciario": "Trifásico",
                        "trifasico_terciario_neutro_zigzag": "Trifásico",
                        "trifasico_neutro_zigzag": "Trifásico",
                        "trifasico_terciario_zigzag": "Trifásico",
                        "trifasico_zigzag": "Trifásico"
                    }

                    # Aplicar mapeamento se o tipo estiver na lista de migração
                    if tipo_transformador in legacy_type_mapping:
                        form_data['tipo_transformador'] = legacy_type_mapping[tipo_transformador]
                        print(f"[MCPDataManager] Tipo de transformador migrado: {tipo_transformador} -> {legacy_type_mapping[tipo_transformador]}")

                # Adicionar campo sut_eps_type se não existir
                if 'sut_eps_type' not in form_data:
                    try:
                        from ..utils.constants import determine_sut_eps_type
                        transformer_data = {
                            "tipo_transformador": form_data.get("tipo_transformador", ""),
                            "grupo_ligacao": form_data.get("grupo_ligacao", "")
                        }
                        form_data['sut_eps_type'] = determine_sut_eps_type(transformer_data)
                        print(f"[MCPDataManager] Adicionado sut_eps_type: {form_data['sut_eps_type']}")
                    except Exception as e:
                        print(f"[MCPDataManager] Erro ao determinar sut_eps_type: {e}")
                        form_data['sut_eps_type'] = "Trifásico"  # Padrão

                migrated_data['formData'] = form_data

            return migrated_data

        except Exception as e:
            print(f"[MCPDataManager] Erro na migração de dados do transformador: {e}")
            # Em caso de erro, retornar dados originais
            return store_data
    def __init__(self, db_path: Optional[str] = None):
        # Se não especificado, usa path absoluto baseado no diretório do projeto
        if db_path is None:
            # Encontra o diretório raiz do projeto (onde deveria estar o tts_data.db)
            # Caminho: backend/mcp/data_manager.py -> backend/mcp -> backend -> TTS_0 (raiz)
            current_file = os.path.abspath(__file__)
            backend_dir = os.path.dirname(os.path.dirname(current_file))  # Sobe 2 níveis: mcp -> backend
            project_root = os.path.dirname(backend_dir)  # Sobe mais 1: backend -> TTS_0 (raiz do projeto)
            db_path = os.path.join(project_root, "tts_data.db")

        self.db_path = db_path
        print(f"[MCPDataManager] Database path: {self.db_path}")
        self._memory_store: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        self._auto_propagation_enabled = False  # Desabilitada por padrão para evitar problemas durante digitação
        
        # Definições dos stores, incluindo dependências e endpoints de atualização
        self.store_definitions = {
            'transformerInputs': {
                'key': 'transformerInputs',
                'dependencies': [],
                'propagates_to': ['losses', 'impulse', 'appliedVoltage', 'inducedVoltage', 'shortCircuit', 'temperatureRise', 'dielectricAnalysis', 'globalInfo'],
                'update_logic_endpoint': None 
            },
            'losses': {
                'key': 'losses',
                'dependencies': ['transformerInputs'],
                'propagates_to': ['temperatureRise'], # temperatureRise depende de losses
                'update_logic_endpoint': 'api/transformer/modules/losses/process' 
            },
            'impulse': {
                'key': 'impulse',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/impulse/process'
            },
            'appliedVoltage': {
                'key': 'appliedVoltage',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/appliedVoltage/process'
            },
            'inducedVoltage': {
                'key': 'inducedVoltage',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/inducedVoltage/process'
            },
            'shortCircuit': {
                'key': 'shortCircuit',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/shortCircuit/process'
            },
            'temperatureRise': {
                'key': 'temperatureRise',
                'dependencies': ['transformerInputs', 'losses'], 
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/temperatureRise/process'
            },
            'dielectricAnalysis': {
                'key': 'dielectricAnalysis',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/modules/dielectricAnalysis/process'
            },
            'standards': {
                'key': 'standards', 'dependencies': [], 'propagates_to': [], 'update_logic_endpoint': None
            },
            'sessions': {
                'key': 'sessions', 'dependencies': [], 'propagates_to': [], 'update_logic_endpoint': None
            },
            'globalInfo': {
                'key': 'globalInfo',
                'dependencies': ['transformerInputs'],
                'propagates_to': [],
                'update_logic_endpoint': 'api/transformer/global-update'
            },
            'usageCounter': {
                'key': 'usageCounter',
                'dependencies': [],
                'propagates_to': [],
                'update_logic_endpoint': None
            },
            'currentSession': {
                'key': 'currentSession',
                'dependencies': [],
                'propagates_to': [],
                'update_logic_endpoint': None
            }
        }
        
        self._init_database()
        self._load_all_stores()

    def enable_auto_propagation(self):
        """Habilita propagação automática"""
        self._auto_propagation_enabled = True
        print("[MCPDataManager] Propagação automática HABILITADA")

    def disable_auto_propagation(self):
        """Desabilita propagação automática"""
        self._auto_propagation_enabled = False
        print("[MCPDataManager] Propagação automática DESABILITADA")
    
    def _init_database(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS data_stores (
                    store_id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    version INTEGER DEFAULT 1
                )
            ''')
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    session_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            ''')
            conn.commit()
    
    def _load_all_stores(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT store_id, data FROM data_stores')
            for store_id, data_json in cursor.fetchall():
                try:
                    self._memory_store[store_id] = json.loads(data_json)
                except json.JSONDecodeError:
                    print(f"Erro ao carregar dados do store '{store_id}'. Inicializando vazio.")
                    self._memory_store[store_id] = {}
            for store_id_def in self.store_definitions:
                if store_id_def not in self._memory_store:
                    self._memory_store[store_id_def] = {} # Inicializa stores vazios
    
    def get_data(self, store_id: str) -> Dict[str, Any]:
        with self._lock:
            if store_id not in self.store_definitions:
                # Se não está definido, não deve existir. Mas para flexibilidade, podemos retornar vazio
                # raise ValueError(f"Store '{store_id}' não existe.")
                print(f"Aviso: Tentativa de obter store não definido '{store_id}'. Retornando vazio.")
                return {}
            return self._memory_store.get(store_id, {}).copy()
    
    def set_data(self, store_id: str, data: Dict[str, Any]) -> bool:
        with self._lock:
            if store_id not in self.store_definitions:
                # Poderia adicionar dinamicamente, mas por ora vamos manter os stores definidos
                print(f"Aviso: Tentativa de definir store não definido '{store_id}'. Ignorando.")
                return False # Ou raise ValueError
            
            self._memory_store[store_id] = data.copy() # Substitui completamente
            self._persist_store(store_id)
            print(f"[MCPDataManager - set_data] Store '{store_id}' definido. Disparando propagação.")
            self._propagate_changes(store_id) 
            return True
    
    def patch_data(self, store_id: str, partial_data: Dict[str, Any]) -> bool:
        with self._lock:
            if store_id not in self.store_definitions:
                print(f"Aviso: Tentativa de aplicar patch em store não definido '{store_id}'. Ignorando.")
                return False # Ou raise ValueError

            current_store_data = self._memory_store.get(store_id, {})
            
            # Lógica de merge inteligente para 'formData'
            if 'formData' in partial_data and isinstance(partial_data['formData'], dict):
                if 'formData' not in current_store_data or not isinstance(current_store_data.get('formData'), dict):
                    current_store_data['formData'] = {} 
                
                current_store_data['formData'].update(partial_data['formData'])
                
                # Remove 'formData' de partial_data para o update geral abaixo, se houver outras chaves
                # Isso evita que current_store_data['formData'] seja sobrescrito se partial_data = {'formData': ..., 'outraChave': ...}
                # No entanto, se partial_data é APENAS {'formData': ...}, o update abaixo não fará nada.
                # É mais seguro fazer uma cópia e remover.
                other_partial_updates = {k: v for k, v in partial_data.items() if k != 'formData'}
                current_store_data.update(other_partial_updates)
            else:
                current_store_data.update(partial_data) # Merge simples se não houver 'formData' em partial_data
            
            self._memory_store[store_id] = current_store_data
            self._persist_store(store_id)
            print(f"[MCPDataManager - patch_data] Store '{store_id}' atualizado. Disparando propagação.")
            self._propagate_changes(store_id)
            return True
    
    def _persist_store(self, store_id: str):
        data_json = json.dumps(self._memory_store.get(store_id, {}))
        local_timestamp = self.get_local_timestamp()
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO data_stores (store_id, data, last_updated, version)
                VALUES (?, ?, ?,
                    COALESCE((SELECT version + 1 FROM data_stores WHERE store_id = ?), 1))
            ''', (store_id, data_json, local_timestamp, store_id))
            conn.commit()

    def _propagate_changes(self, updated_store_id: str):
        # Verifica se a propagação automática está habilitada
        if not self._auto_propagation_enabled:
            print(f"[MCPDataManager] Propagação automática desabilitada para '{updated_store_id}' - pulando")
            return

        print(f"[MCPDataManager] Iniciando propagação a partir de '{updated_store_id}'")

        # Para transformerInputs, só propaga se os dados estão completos o suficiente
        if updated_store_id == 'transformerInputs':
            transformer_data = self._memory_store.get('transformerInputs', {})
            form_data = transformer_data.get('formData', {})

            # Verifica se temos dados mínimos necessários para propagação
            required_fields = ['potencia_mva', 'tensao_at', 'tensao_bt']
            missing_fields = [field for field in required_fields if not form_data.get(field)]

            if missing_fields:
                print(f"[MCPDataManager] Propagação cancelada para '{updated_store_id}' - campos obrigatórios ausentes: {missing_fields}")
                return

        # Encontra todos os stores que dependem DIRETAMENTE do store atualizado
        # e que têm um endpoint de atualização.
        dependent_calls_info = []
        for store_key, store_def in self.store_definitions.items():
            if updated_store_id in store_def.get('dependencies', []) and store_def.get('update_logic_endpoint'):
                dependent_calls_info.append({
                    "dependent_store_id": store_key,
                    "endpoint": store_def['update_logic_endpoint'],
                    "all_dependencies_ids": store_def.get('dependencies', []) # Todas as dependências do store alvo
                })

        if not dependent_calls_info:
            print(f"[MCPDataManager] '{updated_store_id}' não tem dependentes diretos com endpoints para propagar.")
            return

        base_url = "http://localhost:8000" # Assume que o servidor FastAPI está rodando aqui

        for call_info in dependent_calls_info:
            dependent_store_id = call_info["dependent_store_id"]
            update_endpoint_path = call_info["endpoint"]
            all_deps_ids_for_target = call_info["all_dependencies_ids"]
            
            print(f"[MCPDataManager] Store '{dependent_store_id}' (dependente de '{updated_store_id}') precisa ser recalculado. Endpoint: {update_endpoint_path}")
            
            payload_for_dependent_module = {}
            # 1. Adicionar os 'inputs' do próprio módulo dependente ao payload
            dependent_module_current_data = self.get_data(dependent_store_id) # Dados atuais do store dependente
            payload_for_dependent_module['moduleData'] = dependent_module_current_data.get('inputs', {})

            # 2. Adicionar dados de TODAS as suas dependências
            for dep_id in all_deps_ids_for_target:
                dependency_data_content = self.get_data(dep_id) # Pega do cache em memória
                if dep_id == 'transformerInputs':
                    payload_for_dependent_module['basicData'] = dependency_data_content.get('formData', {})
                elif dep_id == 'losses': # Específico para temperatureRise
                    # O service de temperatureRise precisa dos 'results' de 'losses'
                    payload_for_dependent_module['lossesData'] = dependency_data_content.get('results', {})
                else:
                    # Para outras dependências, pode ser necessário enviar 'results' ou o dado completo
                    # Esta parte pode precisar de ajuste fino dependendo do que cada service espera
                    payload_for_dependent_module[dep_id] = dependency_data_content.get('results', dependency_data_content)
            
            full_url = f"{base_url}/{update_endpoint_path.lstrip('/')}"
            print(f"[MCPDataManager] Chamando: POST {full_url}")
            # print(f"[MCPDataManager] Payload para {dependent_store_id}: {json.dumps(payload_for_dependent_module, indent=2)}") # Cuidado com payloads grandes

            try:
                response = requests.post(full_url, json=payload_for_dependent_module, timeout=10)

                if response.status_code == 200:
                    print(f"[MCPDataManager] ✅ Sucesso ao chamar {full_url} para '{dependent_store_id}'")
                else:
                    print(f"[MCPDataManager] ⚠️ Resposta não-OK de {full_url} para '{dependent_store_id}': {response.status_code}")
                    try:
                        error_detail = response.json()
                        print(f"[MCPDataManager] Detalhes do erro: {error_detail}")
                    except:
                        print(f"[MCPDataManager] Resposta não-JSON: {response.text[:200]}")

            except requests.exceptions.Timeout:
                print(f"[MCPDataManager] ⏱️ Timeout ao chamar {full_url} para '{dependent_store_id}' (>10s)")
            except requests.exceptions.ConnectionError:
                print(f"[MCPDataManager] 🔌 Erro de conexão ao chamar {full_url} para '{dependent_store_id}'")
            except requests.exceptions.RequestException as e:
                print(f"[MCPDataManager] 🌐 Erro HTTP ao chamar {full_url} para '{dependent_store_id}': {e}")
            except Exception as e:
                print(f"[MCPDataManager] ❌ Erro inesperado ao processar propagação para '{dependent_store_id}' via {full_url}: {e}")
        
        print(f"[MCPDataManager] Fim da propagação de mudanças para '{updated_store_id}'")

    def get_all_stores(self) -> Dict[str, Dict[str, Any]]:
        with self._lock:
            return {store_id: data.copy() for store_id, data in self._memory_store.items()}

    def clear_store(self, store_id: str) -> bool:
        with self._lock:
            if store_id not in self.store_definitions:
                return False
            self._memory_store[store_id] = {}
            self._persist_store(store_id)
            # Propagar a limpeza? Geralmente não, a menos que seja um reset.
            # self._propagate_changes(store_id) # Descomentar se necessário
            return True

    def clear_all_stores(self) -> bool:
        with self._lock:
            all_cleared = True
            for store_id in self.store_definitions:
                if not self.clear_store(store_id): # clear_store já persiste e pode propagar
                    all_cleared = False
            return all_cleared

    # Métodos de sessão (save_session, load_session, list_sessions) permanecem iguais
    def save_session(self, session_id: str, description: str = "", session_name: str = "") -> bool:
        local_timestamp = self.get_local_timestamp()
        session_data = {
            'stores': self.get_all_stores(),
            'timestamp': local_timestamp,
            'session_name': session_name  # Adiciona o nome da sessão aos dados
        }
        session_json = json.dumps(session_data)
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sessions (session_id, session_data, created_at, description)
                VALUES (?, ?, ?, ?)
            ''', (session_id, session_json, local_timestamp, description))
            conn.commit()
        return True

    def load_session(self, session_id: str) -> bool:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT session_data FROM sessions WHERE session_id = ?', (session_id,))
            result = cursor.fetchone()
            if not result: return False
            try:
                session_data = json.loads(result[0])
                stores_to_load = session_data.get('stores', {})

                # Aplicar migração de dados para compatibilidade com versões antigas
                stores_to_load = self._migrate_legacy_data(stores_to_load)

                with self._lock: # Garantir que o carregamento seja atômico em relação a outras operações
                    self._memory_store.clear() # Limpa o estado atual da memória
                    self._memory_store.update(stores_to_load) # Carrega todos os stores da sessão
                    # Persiste cada store individualmente e dispara propagações
                    for store_id_loaded, data_loaded in stores_to_load.items():
                        if store_id_loaded in self.store_definitions: # Apenas se o store ainda é definido
                            self._persist_store(store_id_loaded) # Persiste
                            # Dispara propagação para cada store principal carregado
                            # TransformerInputs é o mais importante para disparar primeiro
                    if 'transformerInputs' in stores_to_load:
                        print(f"[MCPDataManager - load_session] Disparando propagação para 'transformerInputs' após carregar sessão.")
                        self._propagate_changes('transformerInputs')
                    else: # Se não houver transformerInputs, propaga para outros que possam ter sido carregados
                        for store_id_loaded in stores_to_load:
                             if store_id_loaded in self.store_definitions:
                                print(f"[MCPDataManager - load_session] Disparando propagação para '{store_id_loaded}' após carregar sessão.")
                                self._propagate_changes(store_id_loaded)

                return True
            except json.JSONDecodeError:
                return False
    
    def list_sessions(self) -> list:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Adicionar coluna updated_at se não existir
            try:
                cursor.execute('ALTER TABLE sessions ADD COLUMN updated_at TIMESTAMP')
            except:
                pass  # Coluna já existe

            cursor.execute('SELECT session_id, session_data, created_at, description, updated_at FROM sessions ORDER BY created_at DESC')
            sessions = []
            for row in cursor.fetchall():
                session_id, session_data_json, created_at, description, updated_at = row
                try:
                    session_data = json.loads(session_data_json)
                    session_name = session_data.get('session_name', session_id)
                except json.JSONDecodeError:
                    session_name = session_id

                sessions.append({
                    'session_id': session_id,
                    'session_name': session_name,
                    'created_at': created_at,
                    'description': description,
                    'updated_at': updated_at
                })
            return sessions

    # --- Serialization Methods ---

    def serialize_module_data(self, module_name: str, data: Dict[str, Any], form_data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Serialize module data using standardized models for database storage.

        Args:
            module_name: Name of the module (losses, inducedVoltage, appliedVoltage)
            data: Raw calculation results
            form_data: Original form input data

        Returns:
            Serialized data ready for database storage
        """
        if not TestDataSerializer:
            print(f"[MCPDataManager] TestDataSerializer not available, storing raw data for {module_name}")
            return data

        try:
            if module_name == "losses":
                from ..services.losses_service import serialize_losses_results
                bank_selections = self.get_data("bankSelections") or {}
                serialized = serialize_losses_results(data, form_data or {}, bank_selections)
                return TestDataSerializer.prepare_for_database(serialized)

            elif module_name == "inducedVoltage":
                from ..services.induced_voltage_service import serialize_induced_voltage_results
                serialized = serialize_induced_voltage_results(data, form_data or {})
                return TestDataSerializer.prepare_for_database(serialized)

            elif module_name == "appliedVoltage":
                from ..services.applied_voltage_service import serialize_applied_voltage_results
                serialized = serialize_applied_voltage_results(data, form_data or {})
                return TestDataSerializer.prepare_for_database(serialized)

            else:
                print(f"[MCPDataManager] No serializer available for module {module_name}, storing raw data")
                return data

        except Exception as e:
            print(f"[MCPDataManager] Error serializing {module_name} data: {e}")
            return data

    def validate_module_data(self, module_name: str, data: Dict[str, Any]) -> List[str]:
        """
        Validate module data integrity.

        Args:
            module_name: Name of the module
            data: Data to validate

        Returns:
            List of validation errors (empty if valid)
        """
        try:
            if module_name == "losses":
                return validate_losses_data(data)
            elif module_name == "inducedVoltage":
                return validate_induced_voltage_data(data)
            elif module_name == "appliedVoltage":
                return validate_applied_voltage_data(data)
            else:
                return []  # No validation for unknown modules
        except Exception as e:
            return [f"Validation error: {str(e)}"]

    def create_history_session_data(self, session_id: str, session_name: str = "", description: str = "") -> Optional[HistorySessionData]:
        """
        Create a standardized history session data object.

        Args:
            session_id: Unique session identifier
            session_name: Human-readable session name
            description: Session description

        Returns:
            HistorySessionData object or None if serialization not available
        """
        if not HistorySessionData:
            return None

        try:
            # Get all current store data
            all_stores = self.get_all_stores()

            # Extract module-specific data
            transformer_inputs = all_stores.get("transformerInputs", {})

            # Try to deserialize module data if available
            losses_data = None
            induced_voltage_data = None
            applied_voltage_data = None

            if "losses" in all_stores:
                losses_raw = all_stores["losses"]
                if isinstance(losses_raw, dict) and "data" in losses_raw:
                    try:
                        losses_data = TestDataSerializer.deserialize_from_json(
                            losses_raw["data"], LossesResult
                        )
                    except:
                        pass  # Keep as None if deserialization fails

            if "inducedVoltage" in all_stores:
                induced_raw = all_stores["inducedVoltage"]
                if isinstance(induced_raw, dict) and "data" in induced_raw:
                    try:
                        induced_voltage_data = TestDataSerializer.deserialize_from_json(
                            induced_raw["data"], InducedVoltageResult
                        )
                    except:
                        pass

            if "appliedVoltage" in all_stores:
                applied_raw = all_stores["appliedVoltage"]
                if isinstance(applied_raw, dict) and "data" in applied_raw:
                    try:
                        applied_voltage_data = TestDataSerializer.deserialize_from_json(
                            applied_raw["data"], AppliedVoltageResult
                        )
                    except:
                        pass

            session_data = HistorySessionData(
                session_id=session_id,
                session_name=session_name,
                description=description,
                transformer_inputs=transformer_inputs,
                losses_data=losses_data,
                induced_voltage_data=induced_voltage_data,
                applied_voltage_data=applied_voltage_data,
                # Raw data for other modules
                impulse_data=all_stores.get("impulse"),
                temperature_rise_data=all_stores.get("temperatureRise"),
                short_circuit_data=all_stores.get("shortCircuit"),
                dielectric_analysis_data=all_stores.get("dielectricAnalysis")
            )

            return session_data

        except Exception as e:
            print(f"[MCPDataManager] Error creating history session data: {e}")
            return None