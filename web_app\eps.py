import pandas as pd
import json
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import os
import numpy as np
import plotly.io as pio # Importação necessária para definir o template
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# --- 0. Definição dos Caminhos e Limites ---
output_directory = r'C:\Users\<USER>\Desktop\TTS_0\web_app'
file_path = os.path.join(output_directory, 'EPS.json')
output_path = os.path.join(output_directory, 'relatorio_final_completo.html')

# --- DEFINIÇÃO DOS LIMITES DO LABORATÓRIO ---
EPS_LIMITS = {
    'NO_LOAD':    {'kW': 1296, 'kVA': 1350},
    'LOAD_LOSS':  {'kW': 1296, 'kVA': 1440},
    'HEAT_RUN':   {'kW': 1296, 'kVA': 1440},
    'INDUCED':    {'kW': 1000, 'kVA': 1200}
}

# --- CONFIGURAÇÕES AVANÇADAS DE ANÁLISE ---
CAPACITY_THRESHOLDS = {
    'CRITICAL': 0.95,  # 95% da capacidade
    'WARNING': 0.85,   # 85% da capacidade
    'OPTIMAL': 0.75    # 75% da capacidade
}

SAFETY_MARGINS = {
    'MINIMUM': 0.05,   # 5% margem mínima
    'RECOMMENDED': 0.15, # 15% margem recomendada
    'CONSERVATIVE': 0.25 # 25% margem conservadora
}
CAP_BANK_RAW = {
    '13.8': "23'400", '23.9': "23'400", '27.6': "93’600", '41.4': "70’200",
    '47.8': "93’600", '71.7': "70’200", '95.6': "93’600"
}
CAP_BANK_CAPACITY_KVA = {float(k): float(v.replace("'", "").replace("’", "")) for k, v in CAP_BANK_RAW.items()}
CAP_BANK_VOLTAGES = np.array(list(CAP_BANK_CAPACITY_KVA.keys()))

# --- 1. Carregamento e Limpeza ---
if not os.path.exists(file_path):
    print(f"ERRO: O arquivo não foi encontrado: {file_path}")
    exit()
try:
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        if not content.startswith('['): content = '[' + content.replace(r'}\s*,?\s*{', '},{') + ']'
        data = json.loads(content)
except Exception as e:
    print(f"Ocorreu um erro ao ler ou processar o arquivo JSON: {e}")
    exit()
df = pd.DataFrame(data)
print("Dados unificados (EPS.json) carregados com sucesso.")

# --- 2. Limpeza e Preparação dos Dados ---
def clean_and_convert(val):
    if isinstance(val, str): val = val.replace("’", "").replace("'", "").replace(",", ".").replace("%", "").strip()
    return pd.to_numeric(val, errors='coerce')
df = df.apply(lambda col: col.map(clean_and_convert))
df = df.dropna(how='all').reset_index(drop=True)
if 'Power [kVA]' in df.columns: df['Power [MVA]'] = df['Power [kVA]'] / 1000
df['DUT_Identifier'] = (
    df['Power [MVA]'].fillna(0).astype(int).astype(str) + 'MVA | ' +
    df['Voltage [kV] HV Max'].fillna(0).astype(str) + 'kV | ' +
    df['Phases ( 1 or 3)'].fillna(0).astype(int).astype(str) + 'Ph'
)

# --- 2.5. RECALCULO E VERIFICAÇÃO AVANÇADA DE LIMITES ---
print("\n--- Realizando análise avançada de fallback do EPS e Banco de Capacitores ---")
hr_v_col = 'Heatrun HV Voltage[kV]'; hr_s_col = 'Heatrun HV Power[kVA]'; hr_p_col = 'Heatrun Power[kW]'
ll_v_col = 'Load Loss HV Voltage [kV]'; ll_s_col = 'Load Loss HV Power [kVA]'; ll_p_col = 'Load Loss Power[kW]'; sc_imp_col = 'SC Imp. (%) HV-LV'
nl_p_col = 'No Load Power[kW]'; nl_s100_col = 'No Load S 100%[kVA]'; nl_s110_col = 'No Load S 110%[kVA]'; nl_i100_col = 'No Load Loss Current[%] I0 100%'
ind_v_col = 'Induced Voltage[kV]'; ind_s_col = 'Induced Induced Estimate [kVA]'
hr_q_demanded_col = 'Heatrun Reactive Power [kVAR]'; ll_q_demanded_col = 'Load Loss Reactive Power [kVAR]'

df[hr_q_demanded_col] = np.sqrt((df[hr_s_col]**2 - df[hr_p_col]**2).abs())
df[ll_q_demanded_col] = np.sqrt((df[ll_s_col]**2 - df[ll_p_col]**2).abs())
def get_bank_capacity_at_voltage(v_test):
    if pd.isna(v_test): return np.nan
    idx = (np.abs(CAP_BANK_VOLTAGES - v_test)).argmin()
    return CAP_BANK_CAPACITY_KVA[CAP_BANK_VOLTAGES[idx]] * ((v_test / CAP_BANK_VOLTAGES[idx]) ** 2)

def check_fallback_status(df, p_col, q_demanded_col, v_col, limit_kVA):
    q_available = df[v_col].apply(get_bank_capacity_at_voltage)
    q_deficit = (df[q_demanded_col] - q_available).clip(lower=0)
    s_eps_fallback = np.sqrt(df[p_col]**2 + q_deficit**2)
    conditions = [(q_available >= df[q_demanded_col]), (s_eps_fallback <= limit_kVA), (s_eps_fallback > limit_kVA)]
    choices = ['N/A (Banco OK)', 'OK (EPS Cobre)', 'Excede Limite']
    return pd.Series(np.select(conditions, choices, default='Dados Insuf.'), index=df.index), q_available, s_eps_fallback

def check_status(conds, choices, default='Dados Insuficientes'): return np.select(conds, choices, default=default)

# --- FUNÇÕES AVANÇADAS DE ANÁLISE DE CAPACIDADE ---
def calculate_capacity_utilization(power_demand, power_limit):
    """Calcula a utilização da capacidade em percentual"""
    if isinstance(power_demand, pd.Series):
        # Para Series, usar operações vetorizadas
        result = (power_demand / power_limit) * 100
        return result.where(power_demand.notna() & (power_limit != 0), np.nan)
    else:
        # Para valores escalares
        if pd.isna(power_demand) or pd.isna(power_limit) or power_limit == 0:
            return np.nan
        return (power_demand / power_limit) * 100

def get_capacity_status(utilization_pct):
    """Determina o status baseado na utilização da capacidade"""
    if isinstance(utilization_pct, pd.Series):
        # Para Series, usar operações vetorizadas
        conditions = [
            utilization_pct >= CAPACITY_THRESHOLDS['CRITICAL'] * 100,
            utilization_pct >= CAPACITY_THRESHOLDS['WARNING'] * 100,
            utilization_pct <= CAPACITY_THRESHOLDS['OPTIMAL'] * 100,
            utilization_pct.notna()
        ]
        choices = ['CRÍTICO', 'ALERTA', 'ÓTIMO', 'NORMAL']
        return np.select(conditions, choices, default='Dados Insuficientes')
    else:
        # Para valores escalares
        if pd.isna(utilization_pct):
            return 'Dados Insuficientes'
        elif utilization_pct >= CAPACITY_THRESHOLDS['CRITICAL'] * 100:
            return 'CRÍTICO'
        elif utilization_pct >= CAPACITY_THRESHOLDS['WARNING'] * 100:
            return 'ALERTA'
        elif utilization_pct <= CAPACITY_THRESHOLDS['OPTIMAL'] * 100:
            return 'ÓTIMO'
        else:
            return 'NORMAL'

def calculate_safety_margin(power_demand, power_limit):
    """Calcula a margem de segurança disponível"""
    if isinstance(power_demand, pd.Series):
        # Para Series, usar operações vetorizadas
        result = ((power_limit - power_demand) / power_limit) * 100
        return result.where(power_demand.notna() & (power_limit != 0), np.nan)
    else:
        # Para valores escalares
        if pd.isna(power_demand) or pd.isna(power_limit) or power_limit == 0:
            return np.nan
        return ((power_limit - power_demand) / power_limit) * 100

def calculate_eps_current(df):
    """Calcula a corrente do EPS baseada nos parâmetros do transformador"""
    # Constantes do SUT
    SUT_BT_VOLTAGE = 480.0
    SUT_AT_MIN_VOLTAGE = 14000.0  # Tap mínimo disponível

    # Calcular corrente nominal LV
    sqrt_3 = 1.732050807568877

    # Fator baseado no número de fases
    factor = df['Phases ( 1 or 3)'].apply(lambda x: sqrt_3 if x == 3 else 1)

    # Corrente nominal LV (A)
    current_lv_nominal = (df['Power [MVA]'] * 1000) / (df['Voltage [kV] LV Max'] * factor)

    # Corrente de excitação (A) - percentual da corrente nominal
    current_excitation = current_lv_nominal * (df['No Load Loss Current[%] I0 100%'] / 100)

    # Corrente refletida ao EPS (considerando relação de transformação do SUT)
    # Usando tap de 14kV como referência
    sut_ratio = SUT_AT_MIN_VOLTAGE / SUT_BT_VOLTAGE  # 14000/480 = 29.17
    current_eps = current_excitation * sut_ratio

    return current_eps

def analyze_eps_capacity_advanced(df):
    """Análise avançada da capacidade do EPS"""
    # Calcular corrente do EPS
    df['EPS_Current_A'] = calculate_eps_current(df)

    # Análise por tipo de ensaio
    test_types = {
        'NO_LOAD': {
            'power_col': 'No Load Power[kW]',
            'apparent_100_col': 'No Load S 100%[kVA]',
            'apparent_110_col': 'No Load S 110%[kVA]'
        },
        'LOAD_LOSS': {'power_col': 'Load Loss Power[kW]', 'apparent_col': 'Load Loss HV Power [kVA]'},
        'HEAT_RUN': {'power_col': 'Heatrun Power[kW]', 'apparent_col': 'Heatrun HV Power[kVA]'},
        'INDUCED': {'power_col': 'No Load Power[kW]', 'apparent_col': 'Induced Induced Estimate [kVA]'}
    }

    for test_name, cols in test_types.items():
        if test_name == 'NO_LOAD':
            # Análise especial para ensaio em vazio
            if all(col in df.columns for col in [cols['power_col'], cols['apparent_100_col'], cols['apparent_110_col']]):
                # Verificar qual valor supera os limites
                power_exceeds = df[cols['power_col']] > EPS_LIMITS[test_name]['kW']
                s100_exceeds = df[cols['apparent_100_col']] > EPS_LIMITS[test_name]['kVA']
                s110_exceeds = df[cols['apparent_110_col']] > EPS_LIMITS[test_name]['kVA']
                current_exceeds = df['EPS_Current_A'] > 2000.0  # Limite de corrente do EPS

                # Status baseado em qualquer violação
                df[f'{test_name}_Power_Status'] = power_exceeds.apply(lambda x: 'EXCEDE' if x else 'OK')
                df[f'{test_name}_S100_Status'] = s100_exceeds.apply(lambda x: 'EXCEDE' if x else 'OK')
                df[f'{test_name}_S110_Status'] = s110_exceeds.apply(lambda x: 'EXCEDE' if x else 'OK')
                df[f'{test_name}_Current_Status'] = current_exceeds.apply(lambda x: 'EXCEDE' if x else 'OK')

                # Status geral do ensaio
                overall_exceeds = power_exceeds | s100_exceeds | s110_exceeds | current_exceeds
                df[f'{test_name}_Overall_Status'] = overall_exceeds.apply(lambda x: 'EXCEDE LIMITE' if x else 'OK')

                # Utilização percentual
                df[f'{test_name}_Power_Utilization_%'] = calculate_capacity_utilization(
                    df[cols['power_col']], EPS_LIMITS[test_name]['kW']
                )
                df[f'{test_name}_S100_Utilization_%'] = calculate_capacity_utilization(
                    df[cols['apparent_100_col']], EPS_LIMITS[test_name]['kVA']
                )
                df[f'{test_name}_S110_Utilization_%'] = calculate_capacity_utilization(
                    df[cols['apparent_110_col']], EPS_LIMITS[test_name]['kVA']
                )
                df[f'{test_name}_Current_Utilization_%'] = calculate_capacity_utilization(
                    df['EPS_Current_A'], 2000.0
                )
        else:
            # Análise padrão para outros ensaios
            if cols['power_col'] in df.columns and cols['apparent_col'] in df.columns:
                # Utilização de potência ativa
                df[f'{test_name}_Power_Utilization_%'] = calculate_capacity_utilization(
                    df[cols['power_col']], EPS_LIMITS[test_name]['kW']
                )

                # Utilização de potência aparente
                df[f'{test_name}_Apparent_Utilization_%'] = calculate_capacity_utilization(
                    df[cols['apparent_col']], EPS_LIMITS[test_name]['kVA']
                )

                # Status da capacidade
                df[f'{test_name}_Capacity_Status'] = df[f'{test_name}_Apparent_Utilization_%'].apply(get_capacity_status)

                # Margem de segurança
                df[f'{test_name}_Safety_Margin_%'] = calculate_safety_margin(
                    df[cols['apparent_col']], EPS_LIMITS[test_name]['kVA']
                )

    return df

def generate_capacity_summary_report(df):
    """Gera relatório resumido da capacidade do EPS"""
    report = {
        'total_transformers': len(df),
        'critical_count': 0,
        'warning_count': 0,
        'optimal_count': 0,
        'test_analysis': {},
        'recommendations': []
    }

    test_types = ['NO_LOAD', 'LOAD_LOSS', 'HEAT_RUN', 'INDUCED']

    for test_type in test_types:
        status_col = f'{test_type}_Capacity_Status'
        util_col = f'{test_type}_Apparent_Utilization_%'
        margin_col = f'{test_type}_Safety_Margin_%'

        if status_col in df.columns:
            status_counts = df[status_col].value_counts()
            avg_utilization = df[util_col].mean() if util_col in df.columns else 0
            min_margin = df[margin_col].min() if margin_col in df.columns else 0

            report['test_analysis'][test_type] = {
                'critical': status_counts.get('CRÍTICO', 0),
                'warning': status_counts.get('ALERTA', 0),
                'optimal': status_counts.get('ÓTIMO', 0),
                'normal': status_counts.get('NORMAL', 0),
                'avg_utilization': avg_utilization,
                'min_safety_margin': min_margin
            }

            # Contadores globais
            report['critical_count'] += status_counts.get('CRÍTICO', 0)
            report['warning_count'] += status_counts.get('ALERTA', 0)
            report['optimal_count'] += status_counts.get('ÓTIMO', 0)

    # Gerar recomendações
    if report['critical_count'] > 0:
        report['recommendations'].append(f"⚠️ ATENÇÃO: {report['critical_count']} transformadores em estado CRÍTICO")

    if report['warning_count'] > 0:
        report['recommendations'].append(f"🔶 ALERTA: {report['warning_count']} transformadores em estado de ALERTA")

    if report['optimal_count'] > report['total_transformers'] * 0.8:
        report['recommendations'].append("✅ Boa distribuição de carga - maioria dos transformadores em estado ótimo")

    return report

def create_capacity_dashboard_metrics(df):
    """Cria métricas para dashboard de capacidade"""
    metrics = {}

    # Métricas gerais
    total_transformers = len(df)
    metrics['total_transformers'] = total_transformers

    # Análise por potência
    if 'Power [MVA]' in df.columns:
        power_ranges = {
            'Pequeno (< 25 MVA)': df[df['Power [MVA]'] < 25],
            'Médio (25-100 MVA)': df[(df['Power [MVA]'] >= 25) & (df['Power [MVA]'] < 100)],
            'Grande (≥ 100 MVA)': df[df['Power [MVA]'] >= 100]
        }

        metrics['power_distribution'] = {}
        for range_name, range_df in power_ranges.items():
            if not range_df.empty:
                critical_count = 0
                for test_type in ['NO_LOAD', 'LOAD_LOSS', 'HEAT_RUN', 'INDUCED']:
                    status_col = f'{test_type}_Capacity_Status'
                    if status_col in range_df.columns:
                        critical_count += (range_df[status_col] == 'CRÍTICO').sum()

                metrics['power_distribution'][range_name] = {
                    'count': len(range_df),
                    'critical_issues': critical_count,
                    'percentage': (len(range_df) / total_transformers) * 100
                }

    # Utilização média por ensaio
    metrics['test_utilization'] = {}
    for test_type in ['NO_LOAD', 'LOAD_LOSS', 'HEAT_RUN', 'INDUCED']:
        util_col = f'{test_type}_Apparent_Utilization_%'
        if util_col in df.columns:
            avg_util = df[util_col].mean()
            max_util = df[util_col].max()
            metrics['test_utilization'][test_type] = {
                'average': avg_util if not pd.isna(avg_util) else 0,
                'maximum': max_util if not pd.isna(max_util) else 0,
                'status': get_capacity_status(avg_util) if not pd.isna(avg_util) else 'N/A'
            }

    return metrics

def export_to_excel_advanced(df, filename, include_charts=True):
    """Exporta dados para Excel com formatação avançada"""
    try:
        import openpyxl
        from openpyxl.styles import PatternFill, Font, Alignment
        from openpyxl.chart import BarChart, Reference

        # Criar workbook
        wb = openpyxl.Workbook()

        # Aba principal com dados
        ws_main = wb.active
        ws_main.title = "Dados Principais"

        # Escrever dados
        for r_idx, row in enumerate(df.itertuples(index=False), 1):
            for c_idx, value in enumerate(row, 1):
                cell = ws_main.cell(row=r_idx, column=c_idx, value=value)

                # Formatação condicional
                if isinstance(value, str):
                    if value == 'CRÍTICO':
                        cell.fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")
                        cell.font = Font(color="FFFFFF", bold=True)
                    elif value == 'ALERTA':
                        cell.fill = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid")
                        cell.font = Font(bold=True)
                    elif value == 'ÓTIMO':
                        cell.fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")
                        cell.font = Font(bold=True)

        # Aba de resumo
        ws_summary = wb.create_sheet("Resumo Executivo")
        capacity_report = generate_capacity_summary_report(df)

        summary_data = [
            ["Métrica", "Valor"],
            ["Total de Transformadores", capacity_report['total_transformers']],
            ["Estado Crítico", capacity_report['critical_count']],
            ["Estado de Alerta", capacity_report['warning_count']],
            ["Estado Ótimo", capacity_report['optimal_count']]
        ]

        for row_data in summary_data:
            ws_summary.append(row_data)

        # Aba de análise por ensaio
        ws_tests = wb.create_sheet("Análise por Ensaio")
        test_analysis = capacity_report['test_analysis']

        test_headers = ["Ensaio", "Crítico", "Alerta", "Ótimo", "Normal", "Utilização Média (%)", "Margem Mínima (%)"]
        ws_tests.append(test_headers)

        for test_name, data in test_analysis.items():
            row_data = [
                test_name.replace('_', ' ').title(),
                data['critical'],
                data['warning'],
                data['optimal'],
                data['normal'],
                round(data['avg_utilization'], 2),
                round(data['min_safety_margin'], 2)
            ]
            ws_tests.append(row_data)

        # Salvar arquivo
        wb.save(filename)
        return True

    except ImportError:
        print("Biblioteca openpyxl não encontrada. Usando exportação CSV simples.")
        df.to_csv(filename.replace('.xlsx', '.csv'), index=False)
        return False
    except Exception as e:
        print(f"Erro na exportação: {e}")
        return False

def generate_pdf_report(df, filename):
    """Gera relatório PDF com gráficos e tabelas"""
    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        doc = SimpleDocTemplate(filename, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Título
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center
        )
        story.append(Paragraph("Relatório de Análise de Capacidade EPS", title_style))
        story.append(Spacer(1, 20))

        # Resumo executivo
        capacity_report = generate_capacity_summary_report(df)

        summary_data = [
            ['Métrica', 'Valor'],
            ['Total de Transformadores', str(capacity_report['total_transformers'])],
            ['Estado Crítico', str(capacity_report['critical_count'])],
            ['Estado de Alerta', str(capacity_report['warning_count'])],
            ['Estado Ótimo', str(capacity_report['optimal_count'])]
        ]

        summary_table = Table(summary_data)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(Paragraph("Resumo Executivo", styles['Heading2']))
        story.append(summary_table)
        story.append(Spacer(1, 20))

        # Recomendações
        if capacity_report['recommendations']:
            story.append(Paragraph("Recomendações", styles['Heading2']))
            for rec in capacity_report['recommendations']:
                story.append(Paragraph(f"• {rec}", styles['Normal']))
            story.append(Spacer(1, 20))

        # Construir PDF
        doc.build(story)
        return True

    except ImportError:
        print("Biblioteca reportlab não encontrada. PDF não gerado.")
        return False
    except Exception as e:
        print(f"Erro na geração do PDF: {e}")
        return False

df['LL EPS Fallback Status'], df['LL_Q_Available_kvar'], df['LL_S_EPS_Fallback'] = check_fallback_status(df, ll_p_col, ll_q_demanded_col, ll_v_col, EPS_LIMITS['LOAD_LOSS']['kVA'])
df['HR EPS Fallback Status'], df['HR_Q_Available_kvar'], df['HR_S_EPS_Fallback'] = check_fallback_status(df, hr_p_col, hr_q_demanded_col, hr_v_col, EPS_LIMITS['HEAT_RUN']['kVA'])
df['NL_Status'] = check_status([(df[nl_p_col] > EPS_LIMITS['NO_LOAD']['kW']) | (df[nl_s100_col] > EPS_LIMITS['NO_LOAD']['kVA']), df[nl_p_col].notna()], ['Excede Limite', 'OK'])
df['Ind_Status'] = check_status([(df[nl_p_col] > EPS_LIMITS['INDUCED']['kW']) | (df[ind_s_col] > EPS_LIMITS['INDUCED']['kVA']), df[ind_s_col].notna()], ['Excede Limite', 'OK'])

# --- APLICAR ANÁLISE AVANÇADA DE CAPACIDADE ---
df = analyze_eps_capacity_advanced(df)

fail_conditions = ((df['NL_Status'] == 'Excede Limite') | (df['LL EPS Fallback Status'] == 'Excede Limite') | (df['Ind_Status'] == 'Excede Limite') | (df['HR EPS Fallback Status'] == 'Excede Limite'))
df['Overall_Status'] = np.where(fail_conditions, 'FAIL', 'OK')

# --- 3. GERAÇÃO DA TABELA GERAL RESUMIDA ---
col_groups = {
    "Identificação": ['DUT_Identifier', 'Overall_Status'],
    "Dados Nominais": ['Power [MVA]', 'Frequency [Hz]', 'Phases ( 1 or 3)', 'Voltage [kV] HV Max', 'Voltage [kV] LV Max'],
    "Status dos Ensaios": ['NO_LOAD_Overall_Status', 'LL EPS Fallback Status', 'Ind_Status', 'HR EPS Fallback Status']
}
all_ordered_cols = [col for group in col_groups.values() for col in group]
df_general_table = df[all_ordered_cols].copy()
multi_index_tuples = [(group, col) for group, cols in col_groups.items() for col in cols]
df_general_table.columns = pd.MultiIndex.from_tuples(multi_index_tuples)
formatters = {
    ('Dados Nominais', 'Power [MVA]'): '{:.1f}',
    ('Dados Nominais', 'Frequency [Hz]'): '{:.1f}',
    ('Dados Nominais', 'Voltage [kV] HV Max'): '{:.1f}',
    ('Dados Nominais', 'Voltage [kV] LV Max'): '{:.1f}'
}

# --- 4. GERAÇÃO DAS ANÁLISES MODULARES COM GRÁFICOS VISÍVEIS E CLAROS ---
# *** LINHA CORRIGIDA: DEFININDO O TEMPLATE PADRÃO PARA TODOS OS GRÁFICOS ***
pio.templates.default = "plotly_white"

def create_vertical_summary_table(df_source, columns, sort_by_col):
    id_col = 'DUT_Identifier'; cols_with_id = columns[:]; cols_with_id.insert(0, id_col)
    summary_df = df_source.dropna(subset=[c for c in cols_with_id if c != id_col])[cols_with_id].copy()
    if sort_by_col in summary_df.columns: summary_df = summary_df.sort_values(by=sort_by_col, ascending=False)

    # Padronizar para 1 casa decimal
    numeric_cols = summary_df.select_dtypes(include=[np.number]).columns
    summary_df[numeric_cols] = summary_df[numeric_cols].round(1)

    return summary_df

def create_interactive_datatable_html(df_data, table_id, title):
    """Cria HTML para tabela interativa com filtros e ordenação"""
    if df_data.empty:
        return f"<div class='alert alert-warning'>Nenhum dado disponível para {title}</div>"

    # Padronizar dados numéricos para 1 casa decimal
    numeric_cols = df_data.select_dtypes(include=[np.number]).columns
    df_display = df_data.copy()
    df_display[numeric_cols] = df_display[numeric_cols].round(1)

    # Converter para HTML com thead e tfoot para filtros
    table_html = f"""
    <table id="{table_id}" class="display table table-striped table-hover interactive-table" style="width:100%">
        <thead>
            <tr>
                {''.join([f'<th>{col}</th>' for col in df_display.columns])}
            </tr>
        </thead>
        <tfoot>
            <tr>
                {''.join([f'<th>{col}</th>' for col in df_display.columns])}
            </tr>
        </tfoot>
        <tbody>
            {''.join([f'<tr>{"".join([f"<td>{val}</td>" for val in row])}</tr>' for row in df_display.values])}
        </tbody>
    </table>
    """

    # Adicionar controles e JavaScript
    full_html = f"""
    <div class="table-container" id="container_{table_id}">
        <div class="table-header">
            <h5 class="table-title">{title}</h5>
            <div class="table-controls">
                <button class="btn btn-sm btn-outline-primary" onclick="exportTableData('{table_id}', 'excel')">
                    📊 Excel
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="exportTableData('{table_id}', 'csv')">
                    📄 CSV
                </button>
            </div>
        </div>
        <div class="table-wrapper">
            {table_html}
        </div>
    </div>

    <script>
    $(document).ready(function() {{
        $('#{table_id}').DataTable({{
            responsive: true,
            scrollX: true,
            scrollY: '400px',
            scrollCollapse: true,
            paging: true,
            pageLength: 10,
            lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"]],
            searching: true,
            ordering: true,
            info: true,
            stateSave: true,
            dom: 'Blfrtip',
            buttons: [
                {{
                    extend: 'colvis',
                    text: '👁️ Colunas',
                    className: 'btn btn-sm btn-outline-info'
                }}
            ],
            language: {{
                search: "Buscar:",
                lengthMenu: "Mostrar _MENU_ registros",
                info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                infoEmpty: "Mostrando 0 a 0 de 0 registros",
                infoFiltered: "(filtrado de _MAX_ registros totais)",
                paginate: {{
                    first: "Primeiro",
                    last: "Último",
                    next: "Próximo",
                    previous: "Anterior"
                }},
                emptyTable: "Nenhum dado disponível na tabela",
                zeroRecords: "Nenhum registro encontrado"
            }},
            columnDefs: [
                {{
                    targets: '_all',
                    className: 'text-center'
                }}
            ],
            initComplete: function() {{
                // Adicionar filtros por coluna no rodapé
                this.api().columns().every(function() {{
                    var column = this;
                    var title = column.header().textContent;

                    if (title !== 'DUT_Identifier') {{
                        var select = $('<select class="form-control form-control-sm"><option value="">Filtrar ' + title + '</option></select>')
                            .appendTo($(column.footer()).empty())
                            .on('change', function() {{
                                var val = $.fn.dataTable.util.escapeRegex($(this).val());
                                column.search(val ? '^' + val + '$' : '', true, false).draw();
                            }});

                        column.data().unique().sort().each(function(d, j) {{
                            if (d && d !== '-') {{
                                select.append('<option value="' + d + '">' + d + '</option>');
                            }}
                        }});
                    }}
                }});

                // Aplicar estilos condicionais
                applyTableStyling('{table_id}');
            }}
        }});
    }});

    function applyTableStyling(tableId) {{
        const table = document.getElementById(tableId);
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {{
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {{
                const text = cell.textContent.trim();

                // Aplicar cores baseadas no status
                if (text === 'EXCEDE' || text === 'EXCEDE LIMITE') {{
                    cell.style.backgroundColor = '#dc3545';
                    cell.style.color = 'white';
                    cell.style.fontWeight = 'bold';
                }} else if (text === 'OK') {{
                    cell.style.backgroundColor = '#28a745';
                    cell.style.color = 'white';
                    cell.style.fontWeight = 'bold';
                }}
            }});
        }});
    }}
    </script>
    """

    return full_html

def create_interactive_table_json(df_data, table_id, title):
    """Cria dados JSON para tabela interativa"""
    if df_data.empty:
        return {"error": f"Nenhum dado disponível para {title}"}

    # Preparar dados
    data = df_data.fillna('').to_dict('records')
    columns = [{"title": col, "data": col} for col in df_data.columns]

    return {
        "table_id": table_id,
        "title": title,
        "data": data,
        "columns": columns,
        "total_records": len(data)
    }

def generate_datatable_html(table_config):
    """Gera HTML para DataTable interativa"""
    table_id = table_config["table_id"]
    title = table_config["title"]

    return f"""
    <div class="floating-table-container" id="container_{table_id}">
        <div class="table-header">
            <h4 class="table-title">{title}</h4>
            <div class="table-controls">
                <button class="btn btn-sm btn-primary" onclick="exportTable('{table_id}', 'excel')">
                    📊 Excel
                </button>
                <button class="btn btn-sm btn-secondary" onclick="exportTable('{table_id}', 'csv')">
                    📄 CSV
                </button>
                <button class="btn btn-sm btn-info" onclick="toggleFullscreen('{table_id}')">
                    🔍 Expandir
                </button>
            </div>
        </div>
        <table id="{table_id}" class="display responsive nowrap floating-table" style="width:100%">
        </table>
    </div>
    """

def create_interactive_datatable(df_data, table_id, title, columns_config=None):
    """
    Cria uma tabela interativa com DataTables

    Args:
        df_data: DataFrame com os dados
        table_id: ID único para a tabela
        title: Título da tabela
        columns_config: Configuração específica das colunas
    """
    if df_data.empty:
        return f"<div class='alert alert-warning'>Nenhum dado disponível para {title}</div>"

    # Configuração padrão das colunas
    if columns_config is None:
        columns_config = []
        for i, col in enumerate(df_data.columns):
            config = {"title": col, "data": i}

            # Configurações específicas por tipo de coluna
            if any(keyword in col.lower() for keyword in ['power', 'kw', 'kva', 'kvar']):
                config["type"] = "num"
                config["render"] = "$.fn.dataTable.render.number(',', '.', 0, '', ' kW/kVA')"
            elif any(keyword in col.lower() for keyword in ['voltage', 'kv']):
                config["type"] = "num"
                config["render"] = "$.fn.dataTable.render.number(',', '.', 2, '', ' kV')"
            elif any(keyword in col.lower() for keyword in ['%', 'percent', 'utilization']):
                config["type"] = "num"
                config["render"] = "$.fn.dataTable.render.number(',', '.', 1, '', '%')"
            elif any(keyword in col.lower() for keyword in ['status']):
                config["className"] = "status-column"

            columns_config.append(config)

    # Converter DataFrame para formato JSON
    data_json = df_data.to_json(orient='records', date_format='iso')

    # HTML da tabela
    table_html = f"""
    <div class="table-container" id="container_{table_id}">
        <div class="table-header">
            <h4 class="table-title">{title}</h4>
            <div class="table-controls">
                <button class="btn btn-sm btn-outline-primary" onclick="exportTableData('{table_id}', 'excel')">
                    <i class="fas fa-file-excel"></i> Excel
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="exportTableData('{table_id}', 'csv')">
                    <i class="fas fa-file-csv"></i> CSV
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="toggleTableFullscreen('{table_id}')">
                    <i class="fas fa-expand"></i> Tela Cheia
                </button>
            </div>
        </div>
        <div class="table-wrapper">
            <table id="{table_id}" class="display responsive nowrap interactive-table" style="width:100%">
                <thead></thead>
                <tbody></tbody>
            </table>
        </div>
    </div>

    <script>
    $(document).ready(function() {{
        var tableData = {data_json};
        var columnsConfig = {json.dumps(columns_config)};

        var table = $('#{table_id}').DataTable({{
            data: tableData,
            columns: columnsConfig,
            responsive: true,
            scrollX: true,
            scrollY: '400px',
            scrollCollapse: true,
            paging: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Todos"]],
            searching: true,
            ordering: true,
            info: true,
            stateSave: true,
            dom: 'Bfrtip',
            buttons: [
                {{
                    extend: 'colvis',
                    text: '<i class="fas fa-columns"></i> Colunas',
                    className: 'btn btn-sm btn-outline-secondary'
                }},
                {{
                    extend: 'searchBuilder',
                    text: '<i class="fas fa-filter"></i> Filtros Avançados',
                    className: 'btn btn-sm btn-outline-primary'
                }}
            ],
            language: {{
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/pt-BR.json'
            }},
            initComplete: function() {{
                // Adicionar filtros por coluna
                this.api().columns().every(function() {{
                    var column = this;
                    var columnHeader = $(column.header());

                    if (!columnHeader.hasClass('no-filter')) {{
                        var select = $('<select class="form-control form-control-sm"><option value="">Todos</option></select>')
                            .appendTo($(column.footer()).empty())
                            .on('change', function() {{
                                var val = $.fn.dataTable.util.escapeRegex($(this).val());
                                column.search(val ? '^' + val + '$' : '', true, false).draw();
                            }});

                        column.data().unique().sort().each(function(d, j) {{
                            if (d) {{
                                select.append('<option value="' + d + '">' + d + '</option>');
                            }}
                        }});
                    }}
                }});

                // Aplicar estilos condicionais
                applyConditionalStyling('{table_id}');
            }},
            createdRow: function(row, data, dataIndex) {{
                // Aplicar classes CSS baseadas nos dados
                applyRowStyling(row, data, '{table_id}');
            }}
        }});

        // Salvar referência da tabela para uso posterior
        window.dataTables = window.dataTables || {{}};
        window.dataTables['{table_id}'] = table;
    }});
    </script>
    """

    return table_html

def get_status_colors(status_series, ok_val='OK'): return np.where(status_series == 'Excede Limite', 'crimson', np.where(status_series == ok_val, 'mediumseagreen', 'lightslategrey')).tolist()

def get_capacity_colors(status_series):
    """Retorna cores baseadas no status de capacidade"""
    color_map = {
        'CRÍTICO': '#dc3545',    # Vermelho
        'ALERTA': '#fd7e14',     # Laranja
        'NORMAL': '#20c997',     # Verde-azulado
        'ÓTIMO': '#28a745',      # Verde
        'Dados Insuficientes': '#6c757d'  # Cinza
    }
    return [color_map.get(status, '#6c757d') for status in status_series]

def create_capacity_gauge_chart(utilization_pct, title):
    """Cria um gráfico de gauge para mostrar utilização da capacidade"""
    if pd.isna(utilization_pct):
        utilization_pct = 0

    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = utilization_pct,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': title, 'font': {'size': 16}},
        delta = {'reference': CAPACITY_THRESHOLDS['OPTIMAL'] * 100, 'increasing': {'color': "red"}},
        gauge = {
            'axis': {'range': [None, 120], 'tickwidth': 1, 'tickcolor': "darkblue"},
            'bar': {'color': "darkblue"},
            'bgcolor': "white",
            'borderwidth': 2,
            'bordercolor': "gray",
            'steps': [
                {'range': [0, CAPACITY_THRESHOLDS['OPTIMAL'] * 100], 'color': '#d4edda'},
                {'range': [CAPACITY_THRESHOLDS['OPTIMAL'] * 100, CAPACITY_THRESHOLDS['WARNING'] * 100], 'color': '#fff3cd'},
                {'range': [CAPACITY_THRESHOLDS['WARNING'] * 100, CAPACITY_THRESHOLDS['CRITICAL'] * 100], 'color': '#f8d7da'},
                {'range': [CAPACITY_THRESHOLDS['CRITICAL'] * 100, 120], 'color': '#f5c6cb'}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 100
            }
        }
    ))

    fig.update_layout(
        height=300,
        margin=dict(l=20, r=20, t=40, b=20),
        font={'color': "darkblue", 'family': "Arial"}
    )

    return fig

def create_enhanced_risk_chart(df, p_col, q_demanded_col, q_available_col, s_fallback_col, limit_kVA, title):
    """Versão aprimorada do gráfico de risco com mais informações"""
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Análise de Potência Reativa', 'Utilização da Capacidade do EPS'),
        specs=[[{"secondary_y": True}], [{"secondary_y": False}]],
        vertical_spacing=0.15
    )

    # Gráfico superior - Análise de potência reativa
    q_covered_by_bank = np.minimum(df[q_demanded_col], df[q_available_col])
    q_deficit = (df[q_demanded_col] - df[q_available_col]).clip(lower=0)

    fig.add_trace(go.Bar(
        name='kVAR Coberto pelo Banco',
        x=df['DUT_Identifier'],
        y=q_covered_by_bank,
        marker_color='mediumseagreen',
        hovertemplate='<b>%{x}</b><br>Banco: %{y:.0f} kVAR<extra></extra>'
    ), row=1, col=1, secondary_y=False)

    fig.add_trace(go.Bar(
        name='kVAR Déficit (EPS)',
        x=df['DUT_Identifier'],
        y=q_deficit,
        marker_color='darkorange',
        hovertemplate='<b>%{x}</b><br>EPS: %{y:.0f} kVAR<extra></extra>'
    ), row=1, col=1, secondary_y=False)

    fig.add_trace(go.Scatter(
        name='Potência Ativa EPS (kW)',
        x=df['DUT_Identifier'],
        y=df[p_col],
        mode='lines+markers',
        line=dict(color='blue'),
        hovertemplate='<b>%{x}</b><br>P: %{y:.0f} kW<extra></extra>'
    ), row=1, col=1, secondary_y=True)

    # Gráfico inferior - Utilização da capacidade
    utilization_pct = (df[s_fallback_col] / limit_kVA) * 100
    colors = ['#28a745' if x < 75 else '#ffc107' if x < 85 else '#dc3545' if x < 95 else '#6f42c1' for x in utilization_pct]

    fig.add_trace(go.Bar(
        name='Utilização da Capacidade (%)',
        x=df['DUT_Identifier'],
        y=utilization_pct,
        marker_color=colors,
        hovertemplate='<b>%{x}</b><br>Utilização: %{y:.1f}%<extra></extra>'
    ), row=2, col=1)

    # Linhas de referência
    fig.add_hline(y=limit_kVA, line_dash="solid", line_color="red",
                  annotation_text=f"Limite EPS {limit_kVA} kVA", row=1, col=1)
    fig.add_hline(y=75, line_dash="dot", line_color="green",
                  annotation_text="Ótimo (75%)", row=2, col=1)
    fig.add_hline(y=85, line_dash="dash", line_color="orange",
                  annotation_text="Alerta (85%)", row=2, col=1)
    fig.add_hline(y=95, line_dash="solid", line_color="red",
                  annotation_text="Crítico (95%)", row=2, col=1)

    fig.update_layout(
        title_text=title,
        title_font_size=20,
        height=600,
        barmode='stack',
        margin=dict(t=80, b=10),
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    fig.update_yaxes(title_text="Potência Reativa (kVAR)", row=1, col=1, secondary_y=False)
    fig.update_yaxes(title_text="Potência EPS (kW / kVA)", row=1, col=1, secondary_y=True, showgrid=False)
    fig.update_yaxes(title_text="Utilização (%)", row=2, col=1)
    fig.update_xaxes(title_text="Transformador", row=2, col=1)

    return fig

def create_capacity_overview_chart(df):
    """Cria gráfico de visão geral da capacidade do EPS"""
    # Preparar dados para o gráfico
    test_types = ['NO_LOAD', 'LOAD_LOSS', 'HEAT_RUN', 'INDUCED']
    utilization_data = []

    for test_type in test_types:
        util_col = f'{test_type}_Apparent_Utilization_%'
        if util_col in df.columns:
            avg_util = df[util_col].mean()
            max_util = df[util_col].max()
            utilization_data.append({
                'Test_Type': test_type.replace('_', ' ').title(),
                'Average_Utilization': avg_util if not pd.isna(avg_util) else 0,
                'Max_Utilization': max_util if not pd.isna(max_util) else 0
            })

    if not utilization_data:
        return go.Figure()

    util_df = pd.DataFrame(utilization_data)

    fig = go.Figure()

    # Barras de utilização média
    fig.add_trace(go.Bar(
        name='Utilização Média (%)',
        x=util_df['Test_Type'],
        y=util_df['Average_Utilization'],
        marker_color='#17a2b8',
        opacity=0.7
    ))

    # Barras de utilização máxima
    fig.add_trace(go.Bar(
        name='Utilização Máxima (%)',
        x=util_df['Test_Type'],
        y=util_df['Max_Utilization'],
        marker_color='#dc3545',
        opacity=0.8
    ))

    # Linhas de referência
    fig.add_hline(y=CAPACITY_THRESHOLDS['OPTIMAL'] * 100,
                  line_dash="dot", line_color="green",
                  annotation_text="Ótimo (75%)")
    fig.add_hline(y=CAPACITY_THRESHOLDS['WARNING'] * 100,
                  line_dash="dash", line_color="orange",
                  annotation_text="Alerta (85%)")
    fig.add_hline(y=CAPACITY_THRESHOLDS['CRITICAL'] * 100,
                  line_dash="solid", line_color="red",
                  annotation_text="Crítico (95%)")

    fig.update_layout(
        title="<b>Visão Geral da Utilização da Capacidade do EPS</b>",
        title_font_size=18,
        xaxis_title="Tipo de Ensaio",
        yaxis_title="Utilização da Capacidade (%)",
        barmode='group',
        height=400,
        margin=dict(t=60, b=10),
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    return fig

# Módulo 1: Ensaio em Vazio - Análise completa
nl_cols_mod = [nl_p_col, nl_s100_col, 'No Load S 110%[kVA]', nl_i100_col, 'EPS_Current_A', 'NO_LOAD_Overall_Status']
df_nl = df.dropna(subset=[nl_p_col, nl_s100_col, nl_i100_col])

# Criar tabela interativa para ensaio em vazio
nl_table_data = df_nl[['DUT_Identifier'] + nl_cols_mod].copy()
nl_table_data = nl_table_data.round(1)  # Padronizar para 1 casa decimal

# Gráfico aprimorado para ensaio em vazio
fig_nl = make_subplots(
    rows=2, cols=1,
    subplot_titles=('Análise de Potência e Limites EPS', 'Corrente de Excitação e Corrente EPS'),
    specs=[[{"secondary_y": False}], [{"secondary_y": True}]],
    vertical_spacing=0.15
)

if not df_nl.empty:
    # Gráfico superior - Potências vs Limites
    fig_nl.add_trace(go.Bar(
        name='Potência Ativa (kW)',
        x=df_nl['DUT_Identifier'],
        y=df_nl[nl_p_col],
        marker_color='#4E79A7',
        hovertemplate='<b>%{x}</b><br>P: %{y:.1f} kW<extra></extra>'
    ), row=1, col=1)

    fig_nl.add_trace(go.Bar(
        name='S 100% (kVA)',
        x=df_nl['DUT_Identifier'],
        y=df_nl[nl_s100_col],
        marker_color='#A0CBE8',
        hovertemplate='<b>%{x}</b><br>S100: %{y:.1f} kVA<extra></extra>'
    ), row=1, col=1)

    fig_nl.add_trace(go.Bar(
        name='S 110% (kVA)',
        x=df_nl['DUT_Identifier'],
        y=df_nl['No Load S 110%[kVA]'],
        marker_color='#F28E2B',
        hovertemplate='<b>%{x}</b><br>S110: %{y:.1f} kVA<extra></extra>'
    ), row=1, col=1)

    # Gráfico inferior - Correntes
    fig_nl.add_trace(go.Scatter(
        name='Corrente Exc. (%)',
        x=df_nl['DUT_Identifier'],
        y=df_nl[nl_i100_col],
        mode='lines+markers',
        line=dict(color='#E15759', dash='dot'),
        hovertemplate='<b>%{x}</b><br>I exc: %{y:.1f}%<extra></extra>'
    ), row=2, col=1, secondary_y=False)

    fig_nl.add_trace(go.Scatter(
        name='Corrente EPS (A)',
        x=df_nl['DUT_Identifier'],
        y=df_nl['EPS_Current_A'],
        mode='lines+markers',
        line=dict(color='#76B7B2'),
        hovertemplate='<b>%{x}</b><br>I EPS: %{y:.1f} A<extra></extra>'
    ), row=2, col=1, secondary_y=True)

# Linhas de referência dos limites
fig_nl.add_hline(y=EPS_LIMITS['NO_LOAD']['kW'], line_dash="solid", line_color="red",
                 annotation_text=f"Limite P: {EPS_LIMITS['NO_LOAD']['kW']} kW", row=1, col=1)
fig_nl.add_hline(y=EPS_LIMITS['NO_LOAD']['kVA'], line_dash="dash", line_color="orange",
                 annotation_text=f"Limite S: {EPS_LIMITS['NO_LOAD']['kVA']} kVA", row=1, col=1)
fig_nl.add_hline(y=2000, line_dash="solid", line_color="red",
                 annotation_text="Limite I EPS: 2000 A", row=2, col=1, secondary_y=True)

fig_nl.update_layout(
    title_text="<b>Análise Completa do Ensaio em Vazio</b>",
    title_font_size=20,
    height=600,
    margin=dict(t=80, b=10),
    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
)

fig_nl.update_yaxes(title_text="Potência (kW / kVA)", row=1, col=1)
fig_nl.update_yaxes(title_text="Corrente Exc. (%)", row=2, col=1, secondary_y=False)
fig_nl.update_yaxes(title_text="Corrente EPS (A)", row=2, col=1, secondary_y=True, showgrid=False)
fig_nl.update_xaxes(title_text="Transformador", row=2, col=1)

# Criar tabela de resumo para o módulo
nl_summary_styler = create_vertical_summary_table(nl_table_data, nl_cols_mod, nl_s100_col)

def create_risk_chart(df, p_col, q_demanded_col, q_available_col, s_fallback_col, limit_kVA, title):
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    q_covered_by_bank = np.minimum(df[q_demanded_col], df[q_available_col])
    q_deficit = (df[q_demanded_col] - df[q_available_col]).clip(lower=0)
    fig.add_trace(go.Bar(name='kVAR Coberto pelo Banco', x=df['DUT_Identifier'], y=q_covered_by_bank, marker_color='mediumseagreen'), secondary_y=False)
    fig.add_trace(go.Bar(name='kVAR Déficit (EPS)', x=df['DUT_Identifier'], y=q_deficit, marker_color='darkorange'), secondary_y=False)
    fig.add_trace(go.Scatter(name='Potência Ativa EPS (kW)', x=df['DUT_Identifier'], y=df[p_col], mode='lines+markers', line=dict(color='blue')), secondary_y=True)
    fig.add_trace(go.Scatter(name='Demanda kVA Total no EPS', x=df['DUT_Identifier'], y=df[s_fallback_col], mode='lines+markers', line=dict(color='red', dash='dot')), secondary_y=True)
    fig.add_hline(y=limit_kVA, line_dash="solid", line_color="red", annotation_text=f"Limite EPS {limit_kVA} kVA", secondary_y=True)
    fig.update_layout(barmode='stack', title_text=title, title_font_size=20, xaxis_title="Transformador", margin=dict(t=60, b=10), legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1))
    fig.update_yaxes(title_text="Potência Reativa (kVAR)", secondary_y=False); fig.update_yaxes(title_text="Potência EPS (kW / kVA)", secondary_y=True, showgrid=False)
    return fig

# Módulo 2: Ensaio em Carga - Análise de potência e impedância
# Verificar se Load Loss Power[kW] supera o limite
df['LL_Power_Exceeds'] = df[ll_p_col] > EPS_LIMITS['LOAD_LOSS']['kW']
df['LL_Power_Status'] = df['LL_Power_Exceeds'].apply(lambda x: 'EXCEDE LIMITE' if x else 'OK')

# Análise da relação Load Loss Power[kW] x SC Imp. (%) HV-LV
df['LL_Power_per_Impedance'] = df[ll_p_col] / df[sc_imp_col]

ll_cols_mod = [ll_p_col, ll_s_col, sc_imp_col, 'LL_Power_Status', 'LL_Power_per_Impedance', 'LL EPS Fallback Status']
df_ll = df.dropna(subset=[ll_p_col, ll_s_col, sc_imp_col])

# Criar tabela para ensaio em carga
ll_table_data = df_ll[['DUT_Identifier'] + ll_cols_mod].copy()
ll_table_data = ll_table_data.round(1)

# Gráfico aprimorado para ensaio em carga
fig_ll = make_subplots(
    rows=2, cols=1,
    subplot_titles=('Potência vs Limite EPS', 'Relação Potência/Impedância'),
    specs=[[{"secondary_y": False}], [{"secondary_y": True}]],
    vertical_spacing=0.15
)

if not df_ll.empty:
    # Gráfico superior - Potência vs Limite
    colors_power = ['#dc3545' if exceeds else '#28a745' for exceeds in df_ll['LL_Power_Exceeds']]

    fig_ll.add_trace(go.Bar(
        name='Load Loss Power (kW)',
        x=df_ll['DUT_Identifier'],
        y=df_ll[ll_p_col],
        marker_color=colors_power,
        hovertemplate='<b>%{x}</b><br>Potência: %{y:.1f} kW<extra></extra>'
    ), row=1, col=1)

    # Gráfico inferior - Relação Potência/Impedância
    fig_ll.add_trace(go.Scatter(
        name='Potência/Impedância',
        x=df_ll['DUT_Identifier'],
        y=df_ll['LL_Power_per_Impedance'],
        mode='lines+markers',
        line=dict(color='#17a2b8'),
        hovertemplate='<b>%{x}</b><br>P/Z: %{y:.1f} kW/%<extra></extra>'
    ), row=2, col=1, secondary_y=False)

    fig_ll.add_trace(go.Scatter(
        name='Impedância (%)',
        x=df_ll['DUT_Identifier'],
        y=df_ll[sc_imp_col],
        mode='lines+markers',
        line=dict(color='#fd7e14', dash='dot'),
        hovertemplate='<b>%{x}</b><br>Impedância: %{y:.1f}%<extra></extra>'
    ), row=2, col=1, secondary_y=True)

# Linha de referência do limite
fig_ll.add_hline(y=EPS_LIMITS['LOAD_LOSS']['kW'], line_dash="solid", line_color="red",
                 annotation_text=f"Limite: {EPS_LIMITS['LOAD_LOSS']['kW']} kW", row=1, col=1)

fig_ll.update_layout(
    title_text="<b>Análise do Ensaio em Carga</b>",
    title_font_size=20,
    height=600,
    margin=dict(t=80, b=10),
    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
)

fig_ll.update_yaxes(title_text="Potência (kW)", row=1, col=1)
fig_ll.update_yaxes(title_text="Potência/Impedância", row=2, col=1, secondary_y=False)
fig_ll.update_yaxes(title_text="Impedância (%)", row=2, col=1, secondary_y=True, showgrid=False)
fig_ll.update_xaxes(title_text="Transformador", row=2, col=1)

# Módulo 3: Análise de Tensão Induzida
# Verificar se Induced Induced Estimate [kVA] supera o limite
df['IND_Power_Exceeds'] = df[ind_s_col] > EPS_LIMITS['INDUCED']['kVA']
df['IND_Power_Status'] = df['IND_Power_Exceeds'].apply(lambda x: 'EXCEDE LIMITE' if x else 'OK')

ind_cols_mod = [ind_v_col, ind_s_col, 'IND_Power_Status', 'Ind_Status']
df_ind = df.dropna(subset=[ind_v_col, ind_s_col])

# Criar tabela para tensão induzida
ind_table_data = df_ind[['DUT_Identifier'] + ind_cols_mod].copy()
ind_table_data = ind_table_data.round(1)

# Gráfico para tensão induzida
fig_ind = go.Figure()
if not df_ind.empty:
    colors_ind = ['#dc3545' if exceeds else '#28a745' for exceeds in df_ind['IND_Power_Exceeds']]

    fig_ind.add_trace(go.Bar(
        name='Potência Induzida (kVA)',
        x=df_ind['DUT_Identifier'],
        y=df_ind[ind_s_col],
        marker_color=colors_ind,
        hovertemplate='<b>%{x}</b><br>Potência: %{y:.1f} kVA<extra></extra>'
    ))

    fig_ind.add_hline(y=EPS_LIMITS['INDUCED']['kVA'], line_dash="solid", line_color="red",
                      annotation_text=f"Limite EPS: {EPS_LIMITS['INDUCED']['kVA']} kVA")

fig_ind.update_layout(
    title_text="<b>Análise de Tensão Induzida vs. Limites</b>",
    title_font_size=20,
    xaxis_title="Transformador",
    yaxis_title="Potência Estimada (kVA)",
    margin=dict(t=60, b=10)
)

# Módulo 4: Análise de Aquecimento (Heatrun)
# Verificar se Heatrun Power[kW] supera o limite
df['HR_Power_Exceeds'] = df[hr_p_col] > EPS_LIMITS['HEAT_RUN']['kW']
df['HR_Power_Status'] = df['HR_Power_Exceeds'].apply(lambda x: 'EXCEDE LIMITE' if x else 'OK')

# Análise da disponibilidade do banco capacitivo
df['HR_Bank_Sufficient'] = df['HR_Q_Available_kvar'] >= df[hr_q_demanded_col]
df['HR_Bank_Status'] = df['HR_Bank_Sufficient'].apply(lambda x: 'SUFICIENTE' if x else 'INSUFICIENTE')

hr_cols_mod = [hr_v_col, hr_p_col, hr_s_col, hr_q_demanded_col, 'HR_Q_Available_kvar', 'HR_Power_Status', 'HR_Bank_Status', 'HR EPS Fallback Status']
df_hr = df.dropna(subset=[hr_p_col, hr_s_col, 'HR_Q_Available_kvar'])

# Criar tabela para aquecimento
hr_table_data = df_hr[['DUT_Identifier'] + hr_cols_mod].copy()
hr_table_data = hr_table_data.round(1)

# Gráfico para aquecimento
fig_hr = make_subplots(
    rows=2, cols=1,
    subplot_titles=('Potência Ativa vs Limite', 'Disponibilidade do Banco Capacitivo'),
    specs=[[{"secondary_y": False}], [{"secondary_y": False}]],
    vertical_spacing=0.15
)

if not df_hr.empty:
    # Gráfico superior - Potência vs Limite
    colors_hr = ['#dc3545' if exceeds else '#28a745' for exceeds in df_hr['HR_Power_Exceeds']]

    fig_hr.add_trace(go.Bar(
        name='Heatrun Power (kW)',
        x=df_hr['DUT_Identifier'],
        y=df_hr[hr_p_col],
        marker_color=colors_hr,
        hovertemplate='<b>%{x}</b><br>Potência: %{y:.1f} kW<extra></extra>'
    ), row=1, col=1)

    # Gráfico inferior - Banco Capacitivo
    fig_hr.add_trace(go.Bar(
        name='Q Demandado (kVAR)',
        x=df_hr['DUT_Identifier'],
        y=df_hr[hr_q_demanded_col],
        marker_color='#fd7e14',
        hovertemplate='<b>%{x}</b><br>Q Demandado: %{y:.1f} kVAR<extra></extra>'
    ), row=2, col=1)

    fig_hr.add_trace(go.Bar(
        name='Q Disponível (kVAR)',
        x=df_hr['DUT_Identifier'],
        y=df_hr['HR_Q_Available_kvar'],
        marker_color='#17a2b8',
        hovertemplate='<b>%{x}</b><br>Q Disponível: %{y:.1f} kVAR<extra></extra>'
    ), row=2, col=1)

# Linhas de referência
fig_hr.add_hline(y=EPS_LIMITS['HEAT_RUN']['kW'], line_dash="solid", line_color="red",
                 annotation_text=f"Limite: {EPS_LIMITS['HEAT_RUN']['kW']} kW", row=1, col=1)

fig_hr.update_layout(
    title_text="<b>Análise de Aquecimento (Heatrun)</b>",
    title_font_size=20,
    height=600,
    margin=dict(t=80, b=10),
    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
)

fig_hr.update_yaxes(title_text="Potência (kW)", row=1, col=1)
fig_hr.update_yaxes(title_text="Potência Reativa (kVAR)", row=2, col=1)
fig_hr.update_xaxes(title_text="Transformador", row=2, col=1)

# --- GRÁFICOS AVANÇADOS DE CAPACIDADE ---
fig_capacity_overview = create_capacity_overview_chart(df)

# Gráficos de gauge para cada tipo de ensaio
gauge_charts = {}
test_types_for_gauge = {
    'NO_LOAD': 'Ensaio em Vazio',
    'LOAD_LOSS': 'Ensaio em Carga',
    'HEAT_RUN': 'Aquecimento',
    'INDUCED': 'Tensão Induzida'
}

for test_type, display_name in test_types_for_gauge.items():
    util_col = f'{test_type}_Apparent_Utilization_%'
    if util_col in df.columns:
        avg_utilization = df[util_col].mean()
        if not pd.isna(avg_utilization):
            gauge_charts[test_type] = create_capacity_gauge_chart(
                avg_utilization,
                f"Capacidade Média - {display_name}"
            )

# --- 5. GERAÇÃO DO RELATÓRIO HTML ---
print(f"\n--- Salvando relatório completo em '{output_path}' ---")
def style_rows(row):
    if row[('Identificação', 'Overall_Status')] == 'FAIL': return ['background-color: #ffdddd'] * len(row)
    return [''] * len(row)
summary_df = pd.DataFrame({'Ensaio': ['Em Vazio', 'Tensão Induzida', 'Carga (Fallback)', 'Aquecimento (Fallback)'],
    'OK': [df['NL_Status'].value_counts().get('OK', 0), df['Ind_Status'].value_counts().get('OK', 0), df['LL EPS Fallback Status'].value_counts().get('OK (EPS Cobre)', 0), df['HR EPS Fallback Status'].value_counts().get('OK (EPS Cobre)', 0)],
    'Excede Limite': [df['NL_Status'].value_counts().get('Excede Limite', 0), df['Ind_Status'].value_counts().get('Excede Limite', 0), df['LL EPS Fallback Status'].value_counts().get('Excede Limite', 0), df['HR EPS Fallback Status'].value_counts().get('Excede Limite', 0)],
    'N/A (Banco OK)': [0, 0, df['LL EPS Fallback Status'].value_counts().get('N/A (Banco OK)', 0), df['HR EPS Fallback Status'].value_counts().get('N/A (Banco OK)', 0)]
})
with open(output_path, 'w', encoding='utf-8') as f:
    f.write("""<html><head><title>Dashboard EPS - Análise de Capacidade Avançada</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/dataTables.responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body{font-family:'Segoe UI',Arial,sans-serif;margin:0;padding:20px;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:#333;min-height:100vh;}
        .dashboard-container{background:white;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.2);padding:30px;margin-bottom:30px;}
        .dashboard-header{text-align:center;margin-bottom:40px;padding:20px;background:linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);color:white;border-radius:10px;}
        .dashboard-title{font-size:2.5rem;font-weight:bold;margin-bottom:10px;text-shadow:2px 2px 4px rgba(0,0,0,0.3);}
        .dashboard-subtitle{font-size:1.2rem;opacity:0.9;}
        .metrics-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:30px;}
        .metric-card{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;padding:20px;border-radius:10px;text-align:center;box-shadow:0 5px 15px rgba(0,0,0,0.1);}
        .metric-value{font-size:2rem;font-weight:bold;margin-bottom:5px;}
        .metric-label{font-size:0.9rem;opacity:0.9;}
        .module{padding:25px;margin-bottom:25px;background:white;box-shadow:0 5px 20px rgba(0,0,0,0.1);border-radius:12px;border-left:5px solid #667eea;}
        .module-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px;padding-bottom:15px;border-bottom:2px solid #f0f0f0;}
        .module-title{color:#1e3c72;font-size:1.5rem;font-weight:bold;margin:0;}
        .module-controls{display:flex;gap:10px;}
        .floating-table-container{background:white;border-radius:10px;box-shadow:0 3px 10px rgba(0,0,0,0.1);margin-bottom:20px;}
        .table-header{background:#f8f9fa;padding:15px;border-radius:10px 10px 0 0;display:flex;justify-content:space-between;align-items:center;}
        .table-title{margin:0;color:#1e3c72;font-weight:bold;}
        .table-controls{display:flex;gap:10px;}
        .floating-table{width:100%!important;}
        .status-critical{background-color:#dc3545!important;color:white!important;}
        .status-warning{background-color:#ffc107!important;color:#212529!important;}
        .status-optimal{background-color:#28a745!important;color:white!important;}
        .status-normal{background-color:#17a2b8!important;color:white!important;}
        .btn{border-radius:6px;font-weight:500;transition:all 0.3s ease;}
        .btn:hover{transform:translateY(-2px);box-shadow:0 4px 8px rgba(0,0,0,0.2);}
        .chart-container{background:white;border-radius:10px;padding:20px;box-shadow:0 3px 10px rgba(0,0,0,0.1);}
        .side-by-side-module{display:grid;grid-template-columns:1fr 2fr;gap:25px;align-items:start;}

        /* Estilos melhorados para tabelas */
        .table-container-general{max-height:80vh;overflow:auto;background:#f8f9fa;border-radius:8px;border:1px solid #dee2e6;}
        table{border-collapse:collapse;width:100%;font-size:0.75em;background:#f8f9fa;}
        th,td{border:1px solid #dee2e6;padding:6px 8px;text-align:left;vertical-align:middle;background:#f8f9fa;}
        th{background:#e9ecef;color:#495057;font-weight:600;text-align:center;font-size:0.7em;position:sticky;top:0;z-index:10;}
        th.level0{background:#6c757d;color:white;font-size:0.7em;font-weight:bold;position:sticky;top:0;z-index:3;}
        th.level1{background:#adb5bd;color:#212529;font-size:0.65em;font-weight:600;position:sticky;top:37px;z-index:2;}
        td{background:white;font-size:0.7em;}
        tr:nth-child(even) td{background:#f8f9fa;}
        tr:hover td{background:#e3f2fd;}

        /* Tabelas de detalhes nos módulos */
        .table-wrapper table{background:white;border:1px solid #dee2e6;}
        .table-wrapper th{background:#6c757d;color:white;font-size:0.7em;padding:8px;}
        .table-wrapper td{background:white;font-size:0.7em;padding:6px;}
        .table-wrapper tr:nth-child(even) td{background:#f8f9fa;}
        .table-wrapper tr:hover td{background:#e3f2fd;}

        @media (max-width: 768px) {
            .side-by-side-module{grid-template-columns:1fr;}
            .metrics-grid{grid-template-columns:1fr;}
            table{font-size:0.65em;}
            th{font-size:0.6em;}
            td{font-size:0.65em;}
        }
    </style></head><body>""")
    # Gerar métricas do dashboard
    capacity_metrics = create_capacity_dashboard_metrics(df)
    capacity_report = generate_capacity_summary_report(df)

    # Header do Dashboard
    f.write(f"""
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">🔋 Dashboard EPS</h1>
            <p class="dashboard-subtitle">Análise Avançada de Capacidade do Laboratório</p>
            <small>Gerado em: {datetime.now().strftime('%d/%m/%Y às %H:%M')}</small>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{capacity_metrics['total_transformers']}</div>
                <div class="metric-label">Total de Transformadores</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{capacity_report['critical_count']}</div>
                <div class="metric-label">Estado Crítico</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{capacity_report['warning_count']}</div>
                <div class="metric-label">Estado de Alerta</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{capacity_report['optimal_count']}</div>
                <div class="metric-label">Estado Ótimo</div>
            </div>
        </div>
    </div>
    """)
    f.write("<div class='module'><h2>Módulo 0: Sumário Executivo</h2>" + summary_df.to_html(classes='table', index=False, na_rep='-') + "</div>")
    f.write("<div class='module'><h2>Tabela Geral de Dados Consolidados</h2><p>Visão geral com cabeçalhos flutuantes. <span style='background-color:#ffdddd; padding: 3px;'>Linhas destacadas</span> indicam falha.</p><div class='table-container-general'>")
    styled_df = df_general_table.style.format(formatter=formatters, na_rep='-').apply(style_rows, axis=1)
    f.write(styled_df.to_html(classes='table', na_rep='-', index=False, border=0))
    f.write("</div></div>")
    def write_side_by_side_module(title, table_data, table_id, *figs):
        if not table_data.empty:
            f.write(f"<div class='module'><h2>{title}</h2><div class='side-by-side-module'>")
            f.write("<div class='table-wrapper'>")
            f.write(create_interactive_datatable_html(table_data, table_id, f"Detalhes - {title}"))
            f.write("</div>")
            f.write("<div class='graph-wrapper'>")
            for fig in figs:
                f.write(fig.to_html(full_html=False, include_plotlyjs=False))
            f.write("</div></div></div>")
    write_side_by_side_module("Módulo 1: Análise de Ensaio em Vazio", nl_table_data, "nl_table", fig_nl)
    write_side_by_side_module("Módulo 2: Análise de Ensaio em Carga", ll_table_data, "ll_table", fig_ll)
    write_side_by_side_module("Módulo 3: Análise de Tensão Induzida", ind_table_data, "ind_table", fig_ind)
    write_side_by_side_module("Módulo 4: Análise de Aquecimento (Heatrun)", hr_table_data, "hr_table", fig_hr)
    # Adicionar JavaScript para otimizações e interatividade
    f.write("""
    <script>
    // Otimizações de Performance
    document.addEventListener('DOMContentLoaded', function() {
        // Lazy loading para gráficos
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const chartDiv = entry.target;
                    if (chartDiv.dataset.chartData) {
                        // Carregar gráfico quando visível
                        loadChart(chartDiv);
                        chartObserver.unobserve(chartDiv);
                    }
                }
            });
        }, observerOptions);

        // Observar todos os containers de gráficos
        document.querySelectorAll('.chart-container').forEach(chart => {
            chartObserver.observe(chart);
        });

        // Cache de dados das tabelas
        window.tableCache = new Map();

        // Otimização de scroll para tabelas grandes
        document.querySelectorAll('.floating-table').forEach(table => {
            if (table.rows.length > 100) {
                enableVirtualScrolling(table);
            }
        });
    });

    // Função para carregamento lazy de gráficos
    function loadChart(chartDiv) {
        const chartData = JSON.parse(chartDiv.dataset.chartData);
        Plotly.newPlot(chartDiv, chartData.data, chartData.layout, {responsive: true});
    }

    // Virtual scrolling para tabelas grandes
    function enableVirtualScrolling(table) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const rowHeight = 40; // altura estimada da linha
        const containerHeight = 400; // altura do container
        const visibleRows = Math.ceil(containerHeight / rowHeight);

        let startIndex = 0;
        let endIndex = visibleRows;

        function updateVisibleRows() {
            rows.forEach((row, index) => {
                if (index >= startIndex && index < endIndex) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        table.addEventListener('scroll', () => {
            const scrollTop = table.scrollTop;
            startIndex = Math.floor(scrollTop / rowHeight);
            endIndex = startIndex + visibleRows;
            updateVisibleRows();
        });

        updateVisibleRows();
    }

    // Funções de exportação
    function exportTable(tableId, format) {
        const table = document.getElementById(tableId);
        if (!table) return;

        if (format === 'excel') {
            exportToExcel(table, `eps_analysis_${tableId}`);
        } else if (format === 'csv') {
            exportToCSV(table, `eps_analysis_${tableId}`);
        }
    }

    function exportTableData(tableId, format) {
        const table = document.getElementById(tableId);
        if (!table) return;

        if (format === 'excel') {
            exportToExcel(table, `eps_analysis_${tableId}`);
        } else if (format === 'csv') {
            exportToCSV(table, `eps_analysis_${tableId}`);
        }
    }

    function exportToCSV(table, filename) {
        const rows = Array.from(table.querySelectorAll('tr'));
        const csv = rows.map(row => {
            const cells = Array.from(row.querySelectorAll('td, th'));
            return cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
        }).join('\\n');

        downloadFile(csv, `${filename}.csv`, 'text/csv');
    }

    function exportToExcel(table, filename) {
        // Implementação simplificada - em produção usar biblioteca como SheetJS
        const csv = exportToCSV(table, filename);
        downloadFile(csv, `${filename}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    function downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // Toggle fullscreen para tabelas
    function toggleFullscreen(tableId) {
        const container = document.getElementById(`container_${tableId}`);
        if (!container) return;

        if (!document.fullscreenElement) {
            container.requestFullscreen().catch(err => {
                console.log(`Erro ao entrar em tela cheia: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Aplicar estilos condicionais baseados nos dados
    function applyConditionalStyling(tableId) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                const text = cell.textContent.trim();

                // Aplicar cores baseadas no status
                if (text === 'CRÍTICO') {
                    cell.classList.add('status-critical');
                } else if (text === 'ALERTA') {
                    cell.classList.add('status-warning');
                } else if (text === 'ÓTIMO') {
                    cell.classList.add('status-optimal');
                } else if (text === 'NORMAL') {
                    cell.classList.add('status-normal');
                }

                // Destacar valores altos de utilização
                if (text.includes('%') && !isNaN(parseFloat(text))) {
                    const value = parseFloat(text);
                    if (value > 95) {
                        cell.style.backgroundColor = '#dc3545';
                        cell.style.color = 'white';
                    } else if (value > 85) {
                        cell.style.backgroundColor = '#ffc107';
                        cell.style.color = '#212529';
                    }
                }
            });
        });
    }

    function applyRowStyling(row, data, tableId) {
        // Aplicar estilos específicos por linha baseados nos dados
        if (data.Overall_Status === 'FAIL') {
            row.style.backgroundColor = '#ffebee';
        }
    }

    // Debounce para otimizar eventos de scroll e resize
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Otimizar redimensionamento de gráficos
    window.addEventListener('resize', debounce(() => {
        document.querySelectorAll('.plotly-graph-div').forEach(div => {
            Plotly.Plots.resize(div);
        });
    }, 250));
    </script>
    </body></html>""")
print(f"\nRelatório final com gráficos avançados e corrigidos gerado com sucesso em: {output_path}")