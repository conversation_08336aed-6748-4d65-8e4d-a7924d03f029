<!-- public/pages/history.html -->
<div class="container-fluid ps-2 pe-0"> <!-- MODIFICADO: Adicionado ps-2 pe-0 para alinhar com a sidebar -->
    <!-- Div onde o painel de informações do transformador será injetado -->
    <div id="transformer-info-history-page" class="mb-2"></div>
    <!-- Divs ocultas para compatibilidade global -->

    <!-- <PERSON><PERSON><PERSON><PERSON> Página -->
    <div class="row g-2 pt-2">
        <div class="col-md-12">            <h4 class="page-title d-inline align-middle">
                <i class="page-title-icon fas fa-history me-2"></i>
                Histórico de Sessões
            </h4>
        </div>
    </div>

    <!-- Seção de Gerenciamento de Sessões (Salvar e Buscar) -->
    <div class="card mb-3">        <div class="card-header">
            <h6 class="card-header-title m-0">
                <i class="fas fa-tasks me-2"></i> Gerenciar Sessões
            </h6>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-7 col-lg-8 mb-2 mb-md-0">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="history-search-input" placeholder="Filtrar por nome ou notas...">
                        <button type="button" class="btn btn-primary ms-2" id="history-search-button">Buscar</button>
                    </div>
                </div>
                <div class="col-md-5 col-lg-4">
                    <button type="button" class="btn btn-success w-100" id="history-open-save-modal-button">
                        <i class="fas fa-save me-2"></i> Salvar Sessão Atual
                    </button>
                </div>
            </div>
        </div>
    </div>    <!-- Estatísticas -->
    <div class="history-stats-card">
        <div class="card-body p-0">
            <div class="row g-0">
                <div class="col-md-4 history-stats-item">
                    <i class="history-stats-icon fas fa-database"></i>
                    <div>
                        <h5 id="history-stats-total-sessions" class="history-stats-number">--</h5>
                        <p class="history-stats-label">Sessões Salvas</p>
                    </div>
                </div>
                <!-- Adicione mais estatísticas se desejar aqui -->
            </div>
        </div>
    </div>

    <!-- Tabela de Sessões Armazenadas -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="card-header-title m-0">
                <i class="fas fa-list-alt me-2"></i> Sessões Armazenadas
            </h6>
            <button type="button" class="btn btn-outline-success btn-sm" id="downloadAllDataBtn" disabled>
                <i class="fas fa-file-excel me-1"></i>Baixar Todos os Dados
            </button>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped table-bordered mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col" class="text-center" style="width: 18%;">Data Criação</th>
                            <th scope="col" class="text-center" style="width: 25%;">Nome da Sessão</th>
                            <th scope="col" class="text-center" style="width: 20%;">Notas</th>
                            <th scope="col" class="text-center" style="width: 17%;">Última Modificação</th>
                            <th scope="col" class="text-center" style="width: 20%;">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="history-table-body-content">
                        <!-- Conteúdo da tabela preenchido por JavaScript -->
                        <tr>
                            <td colspan="5" class="text-muted text-center py-5">Nenhuma sessão encontrada.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="history-action-message" class="history-action-message mt-3 px-3 pb-3"></div>
        </div>
    </div>

    <!-- Modal para Salvar Sessão -->
    <div class="modal fade" id="history-save-session-modal" tabindex="-1" aria-labelledby="historySaveSessionModalLabel">
        <div class="modal-dialog modal-dialog-centered">            <div class="modal-content modal-content-custom">
                <div class="modal-header modal-header-custom">
                    <h5 class="modal-title" id="historySaveSessionModalLabel">Salvar Sessão Atual</h5>
                    <button type="button" class="btn-close btn-close-inverted" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <label for="history-session-name-input" class="form-label">Nome do Projeto ou OS:</label>
                    <input type="text" class="form-control mb-3" id="history-session-name-input" placeholder="Identificador único">
                    <label for="history-session-notes-input" class="form-label">Notas (Opcional):</label>
                    <textarea class="form-control textarea-large" id="history-session-notes-input" placeholder="Detalhes..."></textarea>
                    <div id="history-save-modal-error" class="text-danger small mt-2"></div>
                </div>
                <div class="modal-footer modal-footer-custom">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="history-save-modal-cancel-button">Cancelar</button>
                    <button type="button" class="btn btn-success" id="history-save-modal-confirm-button">
                        <i class="fas fa-save me-1"></i> Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Sessão -->
    <div class="modal fade" id="history-edit-session-modal" tabindex="-1" aria-labelledby="historyEditSessionModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-content-custom">
                <div class="modal-header modal-header-custom">
                    <h5 class="modal-title" id="historyEditSessionModalLabel">Editar Sessão</h5>
                    <button type="button" class="btn-close btn-close-inverted" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="history-edit-session-name-input" class="form-label">Nome da Sessão:</label>
                        <input type="text" class="form-control" id="history-edit-session-name-input" placeholder="Nome da sessão">
                    </div>
                    <div class="mb-3">
                        <label for="history-edit-session-notes-input" class="form-label">Notas:</label>
                        <textarea class="form-control" id="history-edit-session-notes-input" rows="3" placeholder="Notas da sessão"></textarea>
                    </div>
                    <div id="history-edit-modal-error" class="text-danger small"></div>
                </div>
                <div class="modal-footer modal-footer-custom">
                    <button type="button" class="btn btn-secondary" id="history-edit-modal-cancel-button" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="history-edit-modal-confirm-button">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Confirmar Exclusão -->
    <div class="modal fade" id="history-delete-session-modal" tabindex="-1" aria-labelledby="historyDeleteSessionModalLabel">
        <div class="modal-dialog modal-dialog-centered">            <div class="modal-content modal-content-custom">
                <div class="modal-header modal-header-custom">
                    <h5 class="modal-title" id="historyDeleteSessionModalLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close btn-close-inverted" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir esta sessão?</p>
                    <p class="fw-bold text-danger">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer modal-footer-custom">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="history-delete-modal-cancel-button">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="history-delete-modal-confirm-button">
                        <i class="fas fa-trash-alt me-1"></i> Excluir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Download de Dados Completos -->
    <div class="modal fade" id="downloadAllDataModal" tabindex="-1" aria-labelledby="downloadAllDataModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-content-custom">
                <div class="modal-header modal-header-custom">
                    <h5 class="modal-title" id="downloadAllDataModalLabel">
                        <i class="fas fa-lock me-2"></i>Download de Dados Completos
                    </h5>
                    <button type="button" class="btn-close btn-close-inverted" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Acesso Restrito:</strong> Esta funcionalidade requer autorização especial.
                    </div>
                    <div class="mb-3">
                        <label for="downloadPasswordInput" class="form-label">Senha de Autorização:</label>
                        <input type="password" class="form-control" id="downloadPasswordInput" placeholder="Digite a senha">
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            O arquivo Excel conterá todos os dados históricos separados em planilhas para transformadores trifásicos e monofásicos.
                        </small>
                    </div>
                    <div id="downloadPasswordError" class="text-danger small"></div>
                </div>
                <div class="modal-footer modal-footer-custom">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" id="confirmDownloadBtn">
                        <i class="fas fa-download me-1"></i>Baixar Dados
                        <span class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true" id="downloadSpinner"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>