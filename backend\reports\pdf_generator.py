# PDF report generation logic
import io
from datetime import datetime
from typing import Dict, Any, List, Optional
import pathlib

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, white, black
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, HRFlowable
from reportlab.platypus import Image as ReportLabImage
from reportlab.lib.enums import TA_CENTER, TA_LEFT

# --- Configurações de Estilo e Cores ---
TTS_PRIMARY = HexColor('#26427A')
TTS_SECONDARY = HexColor('#6c757d')
TTS_SUCCESS = HexColor('#28a745')
TTS_DANGER = HexColor('#dc3545')
TTS_LIGHT_GRAY = HexColor('#f8f9fa')
TTS_WHITE = white

class TTSReportTemplate:
    """Template centralizado para estilos do relatório."""
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        self.styles.add(ParagraphStyle(name='TTSTitle', parent=self.styles['Title'], fontSize=16, spaceAfter=15, textColor=TTS_PRIMARY, alignment=TA_CENTER, fontName='Helvetica-Bold'))
        self.styles.add(ParagraphStyle(name='TTSSectionTitle', parent=self.styles['h1'], fontSize=12, spaceAfter=8, spaceBefore=15, textColor=TTS_PRIMARY, fontName='Helvetica-Bold', borderWidth=1, borderColor=TTS_PRIMARY, borderPadding=5, backColor=TTS_LIGHT_GRAY))
        self.styles.add(ParagraphStyle(name='TTSTapTitle', parent=self.styles['h2'], fontSize=11, spaceAfter=10, spaceBefore=15, textColor=black, fontName='Helvetica-Bold', alignment=TA_LEFT, backColor=HexColor('#e9ecef'), borderPadding=6, leftIndent=0))
        self.styles.add(ParagraphStyle(name='TTSScenarioTitle', parent=self.styles['h3'], fontSize=10, spaceAfter=6, spaceBefore=10, textColor=TTS_PRIMARY, fontName='Helvetica-Bold'))
        self.styles.add(ParagraphStyle(name='TTSNormal', parent=self.styles['Normal'], fontSize=9, fontName='Helvetica', leading=12))
        self.styles.add(ParagraphStyle(name='TTSWhiteText', parent=self.styles['Normal'], textColor=TTS_WHITE, fontName='Helvetica-Bold', alignment=TA_CENTER))
        self.styles.add(ParagraphStyle(name='TTSTechnical', parent=self.styles['Normal'], fontSize=8, fontName='Helvetica', alignment=TA_LEFT))
        self.styles.add(ParagraphStyle(name='TTSAlertText', parent=self.styles['Normal'], fontSize=9, fontName='Helvetica', bulletIndent=10, leftIndent=20))

# --- Funções Utilitárias ---
def format_value(value, unit="", decimals=2):
    if value is None or value == "" or str(value).lower() in ['n/a', 'na', '-', 'none']: return "-"
    try:
        num_value = float(str(value).replace(',', '.'))
        formatted_val = f"{num_value:,.{decimals}f}".replace(",", "X").replace(".", ",").replace("X", ".")
        return f"{formatted_val} {unit}".strip()
    except (ValueError, TypeError):
        return str(value) if str(value).strip() else "-"

def _get_unified_data_source(module_data_dict: Dict[str, Any]) -> Dict[str, Any]:
    if not isinstance(module_data_dict, dict): return {}
    root_keys_data = {k: v for k, v in module_data_dict.items() if k not in ['results', 'formData', 'inputs', 'basicData', 'resultsNoLoad', 'resultsLoad']}
    results_data = module_data_dict.get('results', {}) or {}
    form_data = module_data_dict.get('formData', {}) or {}
    inputs_data = module_data_dict.get('inputs', {}) or {}
    basic_data = module_data_dict.get('basicData', {}) or {}

    # INCLUIR dados aninhados de perdas em vazio
    results_no_load = module_data_dict.get('resultsNoLoad', {}) or {}
    if results_no_load and isinstance(results_no_load, dict):
        # Incluir parametros_gerais_comparativo diretamente
        comparativo = results_no_load.get('parametros_gerais_comparativo', {}) or {}
        results_data.update(comparativo)

    # INCLUIR dados aninhados de perdas em carga
    results_load = module_data_dict.get('resultsLoad', {}) or {}
    if results_load and isinstance(results_load, dict):
        results_data.update(results_load)

    return {**root_keys_data, **results_data, **form_data, **inputs_data, **basic_data}

def format_connection_type(connection):
    if not connection: return "-"
    connection_map = {'estrela_sem_neutro': 'Y', 'estrela_com_neutro': 'Yn', 'triangulo': 'D', 'zigzag_sem_neutro': 'Z', 'zigzag_com_neutro': 'Zn', 'delta': 'D', 'wye': 'Y', 'star': 'Y'}
    return connection_map.get(str(connection).lower(), connection)
    
# --- Funções de Criação de Layout ---
def create_header_footer(canvas, doc):
    canvas.saveState()
    canvas.setFont('Helvetica', 8)
    canvas.setFillColor(TTS_SECONDARY)
    canvas.drawRightString(doc.width + doc.leftMargin, 0.5 * inch, f"Página {canvas.getPageNumber()}")
    canvas.setStrokeColor(TTS_SECONDARY)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 0.7 * inch, doc.width + doc.leftMargin, 0.7 * inch)
    canvas.restoreState()

def create_two_column_fields_layout(fields_list, styles):
    if not fields_list: return []
    mid_point = (len(fields_list) + 1) // 2
    left_fields, right_fields = fields_list[:mid_point], fields_list[mid_point:]
    table_data = []
    max_rows = max(len(left_fields), len(right_fields))
    for i in range(max_rows):
        left_cell = f"<b>{left_fields[i][0]}:</b> {left_fields[i][1]}" if i < len(left_fields) else ""
        right_cell = f"<b>{right_fields[i][0]}:</b> {right_fields[i][1]}" if i < len(right_fields) else ""
        table_data.append([Paragraph(left_cell, styles['TTSNormal']), Paragraph(right_cell, styles['TTSNormal'])])
    table = Table(table_data, colWidths=[3.5 * inch, 3.5 * inch])
    table.setStyle(TableStyle([('VALIGN', (0, 0), (-1, -1), 'TOP'), ('LEFTPADDING', (0, 0), (-1, -1), 0), ('RIGHTPADDING', (0, 0), (-1, -1), 12)]))
    return [table, Spacer(1, 12)]

def create_info_table(fields: List[tuple], styles) -> Table:
    table_data = []
    for label, value in fields:
        value_cell = value if isinstance(value, Paragraph) else Paragraph(str(value), styles['TTSNormal'])
        table_data.append([Paragraph(f"<b>{label}:</b>", styles['TTSNormal']), value_cell])
    table = Table(table_data, colWidths=[2.0 * inch, 4.5 * inch])
    table.setStyle(TableStyle([
        ('VALIGN', (0, 0), (-1, -1), 'TOP'), ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6), ('TOPPADDING', (0, 0), (-1, -1), 4),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 4), ('GRID', (0, 0), (-1, -1), 0.5, TTS_SECONDARY),
        ('BACKGROUND', (0, 0), (0, -1), TTS_LIGHT_GRAY),
    ]))
    return table

def create_side_by_side_comparison(left_fields, right_fields, left_title, right_title, styles):
    max_rows = max(len(left_fields), len(right_fields))
    table_data = [[Paragraph(f"<b>{left_title}</b>", styles['TTSNormal']), Paragraph(f"<b>{right_title}</b>", styles['TTSNormal'])]]
    
    def split_fields(fields):
        mid = (len(fields) + 1) // 2
        return fields[:mid], fields[mid:]

    left_col1, left_col2 = split_fields(left_fields)
    right_col1, right_col2 = split_fields(right_fields)
    
    max_sub_rows = max(len(left_col1), len(left_col2), len(right_col1), len(right_col2))

    for i in range(max_sub_rows):
        l1_cell = f"<b>{left_col1[i][0]}:</b> {left_col1[i][1]}" if i < len(left_col1) else ""
        l2_cell = f"<b>{left_col2[i][0]}:</b> {left_col2[i][1]}" if i < len(left_col2) else ""
        r1_cell = f"<b>{right_col1[i][0]}:</b> {right_col1[i][1]}" if i < len(right_col1) else ""
        r2_cell = f"<b>{right_col2[i][0]}:</b> {right_col2[i][1]}" if i < len(right_col2) else ""
        
        left_inner_table = Table([[Paragraph(l1_cell, styles['TTSNormal']), Paragraph(l2_cell, styles['TTSNormal'])]], colWidths=[1.75*inch, 1.75*inch])
        left_inner_table.setStyle(TableStyle([('LEFTPADDING', (0,0), (-1,-1), 0)]))
        
        right_inner_table = Table([[Paragraph(r1_cell, styles['TTSNormal']), Paragraph(r2_cell, styles['TTSNormal'])]], colWidths=[1.75*inch, 1.75*inch])
        right_inner_table.setStyle(TableStyle([('LEFTPADDING', (0,0), (-1,-1), 0)]))

        table_data.append([left_inner_table, right_inner_table])

    outer_table = Table(table_data, colWidths=[3.5 * inch, 3.5 * inch], repeatRows=1)
    outer_table.setStyle(TableStyle([('VALIGN', (0,0), (-1,-1), 'TOP'), ('BACKGROUND', (0,0), (-1,0), TTS_LIGHT_GRAY), ('LINEBELOW', (0,0), (-1,0), 1, TTS_PRIMARY), ('GRID', (0,0), (-1,-1), 0.5, TTS_SECONDARY)]))
    return outer_table

# --- Funções de Formatação de Dados ---
def format_transformer_basic_data(data: Dict[str, Any], story, styles):
    if not data: return
    combined_data = _get_unified_data_source(data)
    story.append(Paragraph("Dados Gerais", styles['TTSScenarioTitle']))
    general_fields = [('Potência', format_value(combined_data.get('potencia_mva'), 'MVA')), ('Frequência', format_value(combined_data.get('frequencia'), 'Hz')), ('Tipo', combined_data.get('tipo_transformador', 'N/A')), ('Grupo de Ligação', combined_data.get('grupo_ligacao', 'N/A')), ('Líquido Isolante', combined_data.get('liquido_isolante', 'N/A')), ('Norma', combined_data.get('norma_iso', 'N/A'))]
    story.extend(create_two_column_fields_layout(general_fields, styles))
    
    story.append(Paragraph("Temperaturas", styles['TTSScenarioTitle']))
    temp_fields = [('Elevação Óleo Topo', format_value(combined_data.get('elevacao_oleo_topo'), '°C')), ('Elevação Enrolamento', format_value(combined_data.get('elevacao_enrol'), '°C'))]
    story.extend(create_two_column_fields_layout(temp_fields, styles))
    
    story.append(Paragraph("Pesos", styles['TTSScenarioTitle']))
    weight_fields = [('Peso Parte Ativa', format_value(combined_data.get('peso_parte_ativa'), 'kg')), ('Peso Tanque', format_value(combined_data.get('peso_tanque_acessorios'), 'kg')), ('Peso Óleo', format_value(combined_data.get('peso_oleo'), 'kg')), ('Peso Total', format_value(combined_data.get('peso_total'), 'kg'))]
    story.extend(create_two_column_fields_layout(weight_fields, styles))

    # Parâmetros dos Enrolamentos
    story.append(Paragraph("Parâmetros dos Enrolamentos", styles['TTSScenarioTitle']))
    at_data = [['Tensão', format_value(combined_data.get('tensao_at'), 'kV')], ['Corrente Nominal', format_value(combined_data.get('corrente_nominal_at'), 'A')], ['Classe de Tensão', format_value(combined_data.get('classe_tensao_at'), 'kV')], ['Conexão', format_connection_type(combined_data.get('conexao_at'))], ['NBI', format_value(combined_data.get('nbi_at'), 'kV')], ['SIL', format_value(combined_data.get('sil_at'), 'kV')]]
    if combined_data.get('tensao_bucha_neutro_at'):
        at_data.extend([['Tensão Neutro', format_value(combined_data.get('tensao_bucha_neutro_at'), 'kV')], ['NBI Neutro', format_value(combined_data.get('nbi_neutro_at'), 'kV')]])
    bt_data = [['Tensão', format_value(combined_data.get('tensao_bt'), 'kV')], ['Corrente Nominal', format_value(combined_data.get('corrente_nominal_bt'), 'A')], ['Classe de Tensão', format_value(combined_data.get('classe_tensao_bt'), 'kV')], ['Conexão', format_connection_type(combined_data.get('conexao_bt'))], ['NBI', format_value(combined_data.get('nbi_bt'), 'kV')], ['SIL', format_value(combined_data.get('sil_bt'), 'kV')]]
    ter_data = [['Corrente Nominal', format_value(combined_data.get('corrente_nominal_terciario'), 'A')]]
    max_rows = max(len(at_data), len(bt_data), len(ter_data))
    headers = [Paragraph("<b>AT</b>", styles['TTSNormal']), Paragraph("<b>BT</b>", styles['TTSNormal']), Paragraph("<b>Terciário</b>", styles['TTSNormal'])]
    table_data = [headers]
    for i in range(max_rows):
        row = []
        for fields in [at_data, bt_data, ter_data]:
            row.append(Paragraph(f"<b>{fields[i][0]}:</b> {fields[i][1]}", styles['TTSNormal']) if i < len(fields) else "")
        table_data.append(row)
    table = Table(table_data, colWidths=[2.33*inch]*3, repeatRows=1)
    table.setStyle(TableStyle([('VALIGN', (0,0), (-1,-1), 'TOP'), ('GRID', (0,0), (-1,-1), 0.5, TTS_SECONDARY), ('BACKGROUND', (0,0), (-1,0), TTS_LIGHT_GRAY)]))
    story.append(table)

def format_losses(data: Dict[str, Any], story, styles, doc):
    """Função orquestradora para perdas em vazio e em carga."""
    story.append(Paragraph("PERDAS EM VAZIO", styles['TTSScenarioTitle']))
    no_load_data = _get_unified_data_source(data)
    entrada_fields = [('Perdas em Vazio (kW)', format_value(no_load_data.get('perdas-vazio-kw'))), ('Peso do Núcleo (Ton)', format_value(no_load_data.get('peso-projeto-Ton'))), ('Corrente Excitação (%)', format_value(no_load_data.get('corrente-excitacao'), '%')), ('Indução Núcleo (T)', format_value(no_load_data.get('inducao-nucleo'), 'T')), ('Corrente Exc. 1.1pu (%)', format_value(no_load_data.get('corrente-excitacao-1-1'), '%')), ('Tipo de Aço', no_load_data.get('tipo-aco', '-'))]
    filtered_entrada = [f for f in entrada_fields if f[1] != '-']
    if filtered_entrada:
        story.append(Paragraph("Parâmetros de Entrada (Vazio)", styles['TTSScenarioTitle']))
        story.extend(create_two_column_fields_layout(filtered_entrada, styles))
    
    # DADOS COMPARATIVOS CORRETOS - usando os nomes corretos dos campos
    projeto_fields = [
        ('Tensão Nominal BT', format_value(no_load_data.get('tensao_nominal_bt_kv'), 'kV')),
        ('Corrente Nominal BT', format_value(no_load_data.get('corrente_nominal_bt_a'), 'A')),
        ('Frequência', format_value(no_load_data.get('frequencia_hz'), 'Hz')),
        ('Potência Mag.', format_value(no_load_data.get('potencia_mag_projeto'), 'kVAr')),
        ('Fator de Perdas Mag.', format_value(no_load_data.get('fator_perdas_mag_projeto'), 'VAR/kg')),
        ('Fator de Perdas', format_value(no_load_data.get('fator_perdas_projeto'), 'W/kg')),
        ('Peso do Núcleo', format_value(no_load_data.get('peso_nucleo_projeto'), 'Ton')),
        ('Corrente de Excitação', format_value(no_load_data.get('corrente_excitacao_projeto'), '%'))
    ]
    aco_fields = [
        ('Tensão Nominal BT', format_value(no_load_data.get('tensao_nominal_bt_kv'), 'kV')),
        ('Corrente Nominal BT', format_value(no_load_data.get('corrente_nominal_bt_a'), 'A')),
        ('Frequência', format_value(no_load_data.get('frequencia_hz'), 'Hz')),
        ('Potência Mag.', format_value(no_load_data.get('potencia_mag_aco'), 'kVAr')),
        ('Fator de Perdas Mag.', format_value(no_load_data.get('fator_perdas_mag_aco'), 'VAR/kg')),
        ('Fator de Perdas', format_value(no_load_data.get('fator_perdas_aco'), 'W/kg')),
        ('Peso do Núcleo', format_value(no_load_data.get('peso_nucleo_aco'), 'Ton')),
        ('Corrente de Excitação', format_value(no_load_data.get('corrente_excitacao_aco'), '%'))
    ]
    story.append(create_side_by_side_comparison(projeto_fields, aco_fields, "Comparativo - Projeto", "Comparativo - Aço H110-27", styles))

    story.append(PageBreak())
    format_load_losses(data, story, styles, doc)

def format_load_losses(data: Dict[str, Any], story, styles, doc):
    load_results = _get_unified_data_source(data)
    if not load_results or not load_results.get('cenarios_detalhados_por_tap'):
        story.append(Paragraph("Nenhum dado de perdas em carga disponível.", styles['TTSNormal']))
        return

    story.append(Paragraph("PERDAS EM CARGA", styles['TTSScenarioTitle']))
    
    limites = load_results.get('limites_info', {})
    if limites:
        limites_fields = [('Limite Corrente EPS (+)', format_value(limites.get('eps_current_limit_positive_a'), 'A')), ('Limite Corrente EPS (-)', format_value(limites.get('eps_current_limit_negative_a'), 'A')), ('Limite Potência DUT', format_value(limites.get('dut_power_limit_kw'), 'kW'))]
        table_data = [[Paragraph("<b>Limites do Sistema para Ensaio</b>", styles['TTSWhiteText'])]] + limites_fields
        table = Table(table_data, colWidths=[2.5*inch, 4.5*inch])
        table.setStyle(TableStyle([('VALIGN', (0,0), (-1,-1), 'TOP'), ('BACKGROUND', (0,0), (-1,0), TTS_PRIMARY), ('SPAN', (0,0), (-1,0)), ('GRID', (0,0), (-1,-1), 0.5, TTS_SECONDARY)]))
        story.append(table)
        story.append(Spacer(1, 15))

    nominais = load_results.get('condicoes_nominais', {})
    if nominais:
        story.append(Paragraph("Condições Nominais dos TAPs", styles['TTSScenarioTitle']))
        temp_ref = nominais.get("temperatura_referencia", 75)
        nominal_fields = [('Tensão', format_value(nominais.get('tensao_at_kv_nominal'), 'kV')), ('Corrente', format_value(nominais.get('corrente_at_a_nominal'), 'A')), ('Vcc', f"{format_value(nominais.get('vcc_percent_nominal'), '%', 2)}"), (f'Perdas Carga ({temp_ref}°C)', format_value(nominais.get('perdas_totais_kw_nominal_tref'), 'kW')), ('Perdas Carga (25°C)', format_value(nominais.get('perdas_frio_25c_kw_nominal'), 'kW'))]
        menor_fields = [('Tensão', format_value(nominais.get('tensao_at_kv_menor'), 'kV')), ('Corrente', format_value(nominais.get('corrente_at_a_menor'), 'A')), ('Vcc', f"{format_value(nominais.get('vcc_percent_menor'), '%', 2)}"), (f'Perdas Carga ({temp_ref}°C)', format_value(nominais.get('perdas_totais_kw_menor_tref'), 'kW')), ('Perdas Carga (25°C)', format_value(nominais.get('perdas_frio_25c_kw_menor'), 'kW'))]
        maior_fields = [('Tensão', format_value(nominais.get('tensao_at_kv_maior'), 'kV')), ('Corrente', format_value(nominais.get('corrente_at_a_maior'), 'A')), ('Vcc', f"{format_value(nominais.get('vcc_percent_maior'), '%', 2)}"), (f'Perdas Carga ({temp_ref}°C)', format_value(nominais.get('perdas_totais_kw_maior_tref'), 'kW')), ('Perdas Carga (25°C)', format_value(nominais.get('perdas_frio_25c_kw_maior'), 'kW'))]
        headers = [Paragraph("<b>Tap Nominal</b>", styles['TTSNormal']), Paragraph("<b>Tap Menor</b>", styles['TTSNormal']), Paragraph("<b>Tap Maior</b>", styles['TTSNormal'])]
        table_data = [headers]
        max_r = max(len(nominal_fields), len(menor_fields), len(maior_fields))
        for i in range(max_r):
            row = []
            for fields in [nominal_fields, menor_fields, maior_fields]:
                row.append(Paragraph(f"<b>{fields[i][0]}:</b> {fields[i][1]}", styles['TTSNormal']) if i < len(fields) else "")
            table_data.append(row)
        table = Table(table_data, colWidths=[2.33 * inch] * 3, repeatRows=1)
        table.setStyle(TableStyle([('VALIGN', (0,0), (-1,-1), 'TOP'), ('BACKGROUND', (0,0), (-1,0), TTS_LIGHT_GRAY), ('LINEBELOW', (0,0), (-1,0), 1, TTS_PRIMARY), ('GRID', (0,0), (-1,-1), 0.5, TTS_SECONDARY)]))
        story.append(table)
    
    cenarios_por_tap = load_results.get('cenarios_detalhados_por_tap', [])
    story.append(PageBreak())
    story.append(Paragraph("CENÁRIOS DETALHADOS DE PERDAS EM CARGA", styles['TTSScenarioTitle']))
    grouped_scenarios = {}
    for tap_data in cenarios_por_tap:
        tap_name = tap_data.get('nome_tap', 'Desconhecido')
        if tap_name not in grouped_scenarios: grouped_scenarios[tap_name] = []
        grouped_scenarios[tap_name].extend(tap_data.get('cenarios_do_tap', []))
    
    tap_order = ['Nominal', 'Menor', 'Maior']
    for tap_name in tap_order:
        if tap_name in grouped_scenarios:
            scenarios = grouped_scenarios[tap_name]
            story.append(Paragraph(f"TAP: {tap_name.upper()}", styles['TTSTapTitle']))
            for cenario in scenarios:
                # USAR LAYOUT DE 6 COLUNAS para cenários detalhados
                scenario_title = f"Cenário: {cenario.get('nome_cenario_teste', 'N/A')}"
                cenario_6col_data = convert_scenario_to_6_columns(cenario, tap_name)
                if len(cenario_6col_data) >= 6:
                    col1, col2, col3, col4, col5, col6 = cenario_6col_data
                    scenario_elements = create_six_column_scenario_layout(col1, col2, col3, col4, col5, col6, scenario_title, styles, doc)
                    story.extend(scenario_elements)
                else:
                    # Fallback para formato antigo se conversão falhar
                    format_single_scenario_by_topics(cenario, story, styles)
            if tap_name != tap_order[-1]: story.append(Spacer(1, 20))

def format_single_scenario_by_topics(cenario: Dict[str, Any], story, styles):
    story.append(Paragraph(f"Cenário: {cenario.get('nome_cenario_teste', 'N/A')}", styles['TTSScenarioTitle']))
    tp = cenario.get('test_params_cenario', {})
    test_fields = [('Tensão de Teste', format_value(tp.get('tensao_kv'), 'kV')), ('Corrente de Teste', format_value(tp.get('corrente_a'), 'A')), ('Potência Ativa (P)', format_value(tp.get('pativa_kw'), 'kW')), ('Potência Reativa (Q)', format_value(tp.get('q_teste_mvar'), 'MVAr')), ('Potência Aparente (S)', format_value(tp.get('pteste_mva'), 'MVA')), ('Reativo Necessário', format_value(cenario.get('cap_required_mvar'), 'MVAr'))]
    story.append(create_info_table(test_fields, styles))
    story.append(Spacer(1, 10))
    cap_bank_sf = cenario.get('cap_bank_sf', {})
    sf_bank = next((c for c in cap_bank_sf.get('available_configurations', []) if c.get('is_default')), {})
    capacitor_fields = [('Tensão do Banco', format_value(cap_bank_sf.get('tensao_disp_kv'), 'kV')), ('Q Nominal Fornecido', format_value(sf_bank.get('q_provided_mvar'), 'MVAr')), ('Q Efetivo Fornecido', format_value(sf_bank.get('q_efetiva_banco_mvar'), 'MVAr')), ('Configuração CS', Paragraph(sf_bank.get('cs_config', '-'), styles['TTSNormal'])), ('Configuração Q', sf_bank.get('q_config', '-'))]
    story.append(Paragraph("Análise do Banco de Capacitores", styles['TTSScenarioTitle']))
    story.append(create_info_table(capacitor_fields, styles))
    story.append(Spacer(1, 10))
    sut_analysis = cenario.get('sut_eps_analysis', [])
    ideal_tap = next((s for s in sut_analysis if s.get('is_ideal_tap')), sut_analysis[0] if sut_analysis else {})
    status_color = TTS_DANGER if ideal_tap.get('is_over_positive_sf') else black
    status_text = "EXCEDE LIMITE" if ideal_tap.get('is_over_positive_sf') else "OK"
    status_global_color = TTS_DANGER if '>' in cenario.get('status_global', '') else black
    sut_eps_fields = [('Tap Ideal SUT', format_value(ideal_tap.get('sut_tap_kv'), 'kV')), ('Corrente EPS (calc)', format_value(ideal_tap.get('corrente_eps_sf_a'), 'A')), ('% do Limite EPS', format_value(ideal_tap.get('percent_limite_sf'), '%')), ('Status Sobrecorrente', Paragraph(f"<font color='{status_color.hexval()}'>{status_text}</font>", styles['TTSNormal'])), ('Fonte SUT', Paragraph(ideal_tap.get('sut_display_msg', '-'), styles['TTSNormal'])), ('Status Global do Cenário', Paragraph(f"<b><font color='{status_global_color.hexval()}'>{cenario.get('status_global', 'OK')}</font></b>", styles['TTSNormal']))]
    story.append(Paragraph("Análise SUT/EPS e Status", styles['TTSScenarioTitle']))
    story.append(create_info_table(sut_eps_fields, styles))
    story.append(Spacer(1, 15))
    story.append(HRFlowable(width='100%', thickness=0.5, color=TTS_SECONDARY))
    story.append(Spacer(1, 15))

def convert_scenario_to_6_columns(cenario: Dict[str, Any], tap_name: str) -> List:
    """Converte dados de cenário para formato de 6 colunas (como TAPs) - TODOS os dados originais"""

    # Organizar dados em 6 grupos (colunas)
    col1_data = []  # Dados do Teste
    col2_data = []  # Potências
    col3_data = []  # Banco de Capacitores
    col4_data = []  # SUT/EPS Análise
    col5_data = []  # Correntes e Limites
    col6_data = []  # Configurações e Status

    # === DADOS ORIGINAIS DA TABELA ===
    tp = cenario.get('test_params_cenario', {})

    # COLUNA 1: Dados do Teste
    col1_data.extend([
        ('Tensão de Teste', format_value(tp.get('tensao_kv'), 'kV')),
        ('Corrente de Teste', format_value(tp.get('corrente_a'), 'A')),
        ('Potência Ativa (P)', format_value(tp.get('pativa_kw'), 'kW')),
        ('Potência Reativa (Q)', format_value(tp.get('q_teste_mvar'), 'MVAr')),
        ('Potência Aparente (S)', format_value(tp.get('pteste_mva'), 'MVA'))
    ])

    # COLUNA 2: Potências
    col2_data.extend([
        ('Reativo Necessário', format_value(cenario.get('cap_required_mvar'), 'MVAr')),
        ('Q Nominal Fornecido', format_value(cenario.get('cap_bank_sf', {}).get('available_configurations', [{}])[0].get('q_provided_mvar') if cenario.get('cap_bank_sf', {}).get('available_configurations') else None, 'MVAr')),
        ('Q Efetivo Fornecido', format_value(cenario.get('cap_bank_sf', {}).get('available_configurations', [{}])[0].get('q_efetiva_banco_mvar') if cenario.get('cap_bank_sf', {}).get('available_configurations') else None, 'MVAr'))
    ])

    # COLUNA 3: Banco de Capacitores
    cap_bank_sf = cenario.get('cap_bank_sf', {})
    sf_bank = next((c for c in cap_bank_sf.get('available_configurations', []) if c.get('is_default')), {})
    col3_data.extend([
        ('Tensão do Banco', format_value(cap_bank_sf.get('tensao_disp_kv'), 'kV')),
        ('Configuração CS', sf_bank.get('cs_config', '-')),
        ('Configuração Q', sf_bank.get('q_config', '-'))
    ])

    # COLUNA 4: SUT/EPS Análise
    sut_analysis = cenario.get('sut_eps_analysis', [])
    ideal_tap = next((s for s in sut_analysis if s.get('is_ideal_tap')), sut_analysis[0] if sut_analysis else {})
    col4_data.extend([
        ('Tap Ideal SUT', format_value(ideal_tap.get('sut_tap_kv'), 'kV')),
        ('Corrente EPS (calc)', format_value(ideal_tap.get('corrente_eps_sf_a'), 'A')),
        ('% do Limite EPS', format_value(ideal_tap.get('percent_limite_sf'), '%'))
    ])

    # COLUNA 5: Correntes e Limites
    status_text = "EXCEDE LIMITE" if ideal_tap.get('is_over_positive_sf') else "OK"
    col5_data.extend([
        ('Status Sobrecorrente', status_text),
        ('Fonte SUT', ideal_tap.get('sut_display_msg', '-'))
    ])

    # COLUNA 6: Configurações e Status
    col6_data.extend([
        ('Status Global do Cenário', cenario.get('status_global', 'OK'))
    ])

    return [col1_data, col2_data, col3_data, col4_data, col5_data, col6_data]

def create_six_column_scenario_layout(col1_data, col2_data, col3_data, col4_data, col5_data, col6_data, scenario_title, styles, doc):
    """Cria layout de 6 colunas para cenários detalhados"""
    elements = []

    # Título do cenário
    elements.append(Paragraph(scenario_title, styles['TTSScenarioTitle']))
    elements.append(Spacer(1, 10))

    # Cabeçalhos das 6 colunas
    headers = [
        Paragraph("<b>Dados do Teste</b>", styles['TTSNormal']),
        Paragraph("<b>Potências</b>", styles['TTSNormal']),
        Paragraph("<b>Banco Capacitores</b>", styles['TTSNormal']),
        Paragraph("<b>SUT/EPS Análise</b>", styles['TTSNormal']),
        Paragraph("<b>Correntes & Limites</b>", styles['TTSNormal']),
        Paragraph("<b>Config. & Status</b>", styles['TTSNormal'])
    ]

    # Preparar dados das colunas
    all_columns = [col1_data, col2_data, col3_data, col4_data, col5_data, col6_data]
    max_rows = max(len(col) for col in all_columns) if all_columns else 0

    # Criar dados da tabela
    table_data = [headers]

    for i in range(max_rows):
        row = []
        for col_data in all_columns:
            if i < len(col_data):
                field_name, field_value = col_data[i]
                # Quebrar linhas longas
                if len(str(field_value)) > 30:
                    field_value = str(field_value).replace(', ', ',<br/>')
                cell_content = Paragraph(f"<b>{field_name}:</b><br/>{field_value}", styles['TTSNormal'])
            else:
                cell_content = ""
            row.append(cell_content)
        table_data.append(row)

    # Criar tabela com 6 colunas
    col_width = doc.width / 6
    table = Table(table_data, colWidths=[col_width] * 6, repeatRows=1)
    table.setStyle(TableStyle([
        ('VALIGN', (0,0), (-1,-1), 'TOP'),
        ('BACKGROUND', (0,0), (-1,0), TTS_LIGHT_GRAY),
        ('LINEBELOW', (0,0), (-1,0), 1, TTS_PRIMARY),
        ('GRID', (0,0), (-1,-1), 0.5, TTS_SECONDARY),
        ('FONTSIZE', (0,0), (-1,-1), 8),
        ('LEFTPADDING', (0,0), (-1,-1), 4),
        ('RIGHTPADDING', (0,0), (-1,-1), 4),
        ('TOPPADDING', (0,0), (-1,-1), 4),
        ('BOTTOMPADDING', (0,0), (-1,-1), 4)
    ]))

    elements.append(table)
    elements.append(Spacer(1, 15))

    return elements

def format_applied_induced_data(data: Dict, story, styles, test_type: str):
    combined_data = _get_unified_data_source(data)
    fields, title = [], ""
    if test_type == 'applied':
        title = "Ensaio de Tensão Aplicada"
        fields = [
            ('Tensão Ensaio AT', format_value(combined_data.get('tensao_ensaio_at'), 'kV')),
            ('Tensão Ensaio BT', format_value(combined_data.get('tensao_ensaio_bt'), 'kV')),
            ('Duração do Ensaio', format_value(combined_data.get('duracao_ensaio'), 's')),
            ('Tipo de Ensaio', combined_data.get('tipo_ensaio', '-')),
            ('Status do Ensaio', combined_data.get('status_ensaio', '-')),
            ('Corrente Fuga AT', format_value(combined_data.get('corrente_fuga_at'), 'mA')),
            ('Corrente Fuga BT', format_value(combined_data.get('corrente_fuga_bt'), 'mA')),
            ('Isolação AT-BT', combined_data.get('isolacao_at_bt', '-')),
            ('Isolação AT-Terra', combined_data.get('isolacao_at_terra', '-')),
            ('Isolação BT-Terra', combined_data.get('isolacao_bt_terra', '-'))
        ]
    elif test_type == 'induced':
        title = "Ensaio de Tensão Induzida"
        fields = [
            ('Frequência de Ensaio', format_value(combined_data.get('frequencia_ensaio'), 'Hz')),
            ('Duração do Ensaio', format_value(combined_data.get('duracao_ensaio'), 's')),
            ('Tipo de Aço', combined_data.get('tipo_aco', '-')),
            ('Capacitância', format_value(combined_data.get('capacitancia'), 'nF')),
            ('Status do Ensaio', combined_data.get('status_ensaio', '-')),
            ('Tensão Induzida Calculada', format_value(combined_data.get('tensao_induzida_calculada'), 'kV')),
            ('Corrente Excitação Ensaio', format_value(combined_data.get('corrente_excitacao_ensaio'), '%')),
            ('Potência Ativa Ensaio', format_value(combined_data.get('potencia_ativa_ensaio'), 'kW')),
            ('Potência Reativa Ensaio', format_value(combined_data.get('potencia_reativa_ensaio'), 'kVAr')),
            ('Indução Máxima', format_value(combined_data.get('inducao_maxima'), 'T')),
            ('Temperatura Máxima', format_value(combined_data.get('temperatura_maxima'), '°C'))
        ]

    valid_fields = [f for f in fields if f[1] != '-']
    if valid_fields:
        story.append(Paragraph(title, styles['TTSScenarioTitle']))
        story.extend(create_two_column_fields_layout(valid_fields, styles))

def generate_general_alerts(modules_data: Dict[str, Any]) -> List[str]:
    alerts = []
    load_results = _get_unified_data_source(modules_data.get('losses', {}))
    cenarios = load_results.get('cenarios_detalhados_por_tap', [])
    for tap_data in cenarios:
        if isinstance(tap_data, dict):
            tap_name = tap_data.get('nome_tap', 'N/A')
            for cenario in tap_data.get('cenarios_do_tap', []):
                if isinstance(cenario, dict):
                    status = cenario.get('status_global', '')
                    if status and '>' in status:
                        alerts.append(f"Cenário '{cenario.get('nome_cenario_teste')}' no tap '{tap_name}' excedeu um limite: {status}")
    return alerts

def format_status_geral(modules_data, story, styles):
    story.append(Paragraph("STATUS GERAL - ALERTAS E PONTOS DE ATENÇÃO", styles['TTSSectionTitle']))
    alerts = generate_general_alerts(modules_data)
    if not alerts:
        story.append(Paragraph("Nenhum ponto crítico detectado nos cenários de perdas em carga.", styles['TTSNormal']))
    else:
        for alert in alerts:
            story.append(Paragraph(f"• {alert}", styles['TTSAlertText']))

def check_module_has_data(module_data: Optional[Dict[str, Any]]) -> bool:
    if not module_data or not isinstance(module_data, dict): return False
    meta_keys = {'lastUpdated', 'timestamp', '_id', 'userId'}
    data_keys = [k for k in module_data.keys() if k not in meta_keys]
    return any(module_data[k] for k in data_keys)

def generate_pdf_report(modules_data: Dict[str, Any], selected_modules: List[str], report_title: str = "Relatório de Simulação de Testes de Transformadores") -> io.BytesIO:
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=0.75*inch, leftMargin=0.75*inch, topMargin=0.75*inch, bottomMargin=1.0*inch)
    template = TTSReportTemplate()
    styles = template.styles
    story = []

    # --- Cabeçalho e Título ---
    logo_path = pathlib.Path(__file__).parent.parent.parent / "public" / "assets" / "DataLogo.jpg"
    try: logo_img = ReportLabImage(str(logo_path), width=1.2*inch, height=0.6*inch, hAlign='RIGHT')
    except: logo_img = Paragraph("DATA", styles['TTSTitle'])
    company_info = Paragraph("<b>TTS - SIMULADOR DE TESTES DE TRANSFORMADORES</b><br/><font size=8>Sistema de Simulação e Análise de Ensaios</font>", styles['TTSNormal'])
    header_table = Table([[company_info, logo_img]], colWidths=[doc.width - 1.3*inch, 1.2*inch], style=[('VALIGN', (0,0), (-1,-1), 'MIDDLE')])
    story.extend([header_table, HRFlowable(width="100%", thickness=2, color=TTS_PRIMARY), Spacer(1, 15), Paragraph(report_title.upper(), styles['TTSTitle'])])
    
    # --- Informações do Relatório ---
    info_fields = [('Data de Geração', datetime.now().strftime("%d/%m/%Y às %H:%M:%S")), ('Versão do Sistema', 'TTS v1.0'), ('Tipo de Relatório', 'Simulação de Ensaios de Transformadores')]
    story.extend(create_two_column_fields_layout(info_fields, styles))
    
    # --- Sumário ---
    section_map = {'transformerInputs': '1. DADOS BÁSICOS DO TRANSFORMADOR', 'losses': '2. ENSAIO DE PERDAS', 'appliedVoltage': '3. ENSAIO DE TENSÃO APLICADA', 'inducedVoltage': '4. ENSAIO DE TENSÃO INDUZIDA'}
    modules_with_data = [m for m in selected_modules if check_module_has_data(modules_data.get(m))]
    if modules_with_data:
        story.append(Paragraph("SUMÁRIO DO RELATÓRIO", styles['TTSScenarioTitle']))
        for module_name in modules_with_data:
            story.append(Paragraph(section_map.get(module_name, module_name.upper()), styles['TTSNormal']))
        story.append(PageBreak())

    # --- Conteúdo dos Módulos ---
    for i, module_name in enumerate(modules_with_data):
        module_content = modules_data.get(module_name, {})
        story.append(Paragraph(section_map.get(module_name, module_name.upper()), styles['TTSSectionTitle']))

        if module_name == 'transformerInputs':
            format_transformer_basic_data(module_content, story, styles)
        elif module_name == 'losses':
            format_losses(module_content, story, styles, doc)
        elif module_name == 'appliedVoltage':
            format_applied_induced_data(module_content, story, styles, 'applied')
        elif module_name == 'inducedVoltage':
            format_applied_induced_data(module_content, story, styles, 'induced')

        if i < len(modules_with_data) - 1 and not (isinstance(story[-1], PageBreak) if story else False):
            story.append(PageBreak())

    # --- Status Geral no Final ---
    if not (isinstance(story[-1], PageBreak) if story else False): story.append(PageBreak())
    format_status_geral(modules_data, story, styles)

    # --- Rodapé da Última Página ---
    story.append(Spacer(1, 30))
    story.append(HRFlowable(width="100%", thickness=0.5, color=TTS_SECONDARY))
    story.append(Paragraph("<i>Relatório gerado automaticamente pelo Sistema TTS - Simulador de Testes de Transformadores</i>", styles['TTSTechnical']))

    doc.build(story, onFirstPage=create_header_footer, onLaterPages=create_header_footer)
    buffer.seek(0)
    return buffer