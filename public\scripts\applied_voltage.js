// public/scripts/applied_voltage.js

console.log('🔥 [applied_voltage] SCRIPT CARREGADO!');
console.log('[applied_voltage] Script carregado - versão 2024-01-25 15:30');

import { loadAndPopulateTransformerInfo, transformerDataStore, updateGlobalInfoPanel } from './common_module.js';
import { waitForApiSystem, collectFormData, fillFormWithData } from './api_persistence.js';

// Função para navegar para transformer inputs sem reload
function navigateToTransformerInputs() {
    console.log('[applied_voltage] Navegando para transformer_inputs sem reload');
    if (window.loadModulePage) {
        window.loadModulePage('transformer_inputs');
    } else {
        // Fallback se a função global não estiver disponível
        window.location.hash = '#transformer_inputs';
    }
}

// Função auxiliar para obter valor de input
function getInputValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : null;
}

// Função auxiliar para definir texto em um elemento
function setElementText(id, text) {
    const el = document.getElementById(id);
    if (el) el.textContent = text;
}

// Função para carregar dados do store 'appliedVoltage' e preencher o formulário
async function loadAppliedVoltageDataAndPopulateForm() {
    try {
        console.log('[applied_voltage] Carregando dados do store "appliedVoltage" e preenchendo formulário...');
        await waitForApiSystem(); // Garante que o sistema de persistência esteja pronto

        const store = window.apiDataSystem.getStore('appliedVoltage');
        const data = await store.getData();

        if (data && data.formData) {
            const formElement = document.getElementById('applied-voltage-form');
            if (formElement) {
                fillFormWithData(formElement, data.formData);
                console.log('[applied_voltage] Formulário de tensão aplicada preenchido com dados do store:', data.formData);
            } else {
                console.warn('[applied_voltage] Formulário "applied-voltage-form" não encontrado para preenchimento.');
            }
        } else {
            console.log('[applied_voltage] Nenhum dado de tensão aplicada encontrado no store.');
        }

        // Carregar e exibir resultados salvos se existirem
        if (data && data.calculationResults) {
            console.log('[applied_voltage] Carregando resultados salvos:', data.calculationResults);
            displayCalculationResults(data.calculationResults);
        }
    } catch (error) {
        console.error('[applied_voltage] Erro ao carregar e preencher dados de tensão aplicada:', error);
    }
}

// Função para preencher as tensões de ensaio com base nos dados do transformador
async function fillTestVoltages() {
    try {
        console.log('[applied_voltage] fillTestVoltages: Iniciando...');

        // Aguarda o sistema de API estar disponível
        await waitForApiSystem();
        if (!window.apiDataSystem) {
            console.error('[applied_voltage] fillTestVoltages: apiDataSystem não disponível');
            return;
        }

        const store = window.apiDataSystem.getStore('transformerInputs');
        const transformerData = await store.getData();
        console.log('[applied_voltage] fillTestVoltages: Dados COMPLETOS do transformador:', JSON.stringify(transformerData, null, 2));

        // Verifica múltiplas estruturas de dados possíveis
        let currentData = {};

        // Estrutura 1: dados diretos no nível raiz
        if (transformerData && typeof transformerData === 'object') {
            currentData = { ...transformerData };
        }

        // Estrutura 2: dados em formData
        if (transformerData?.formData && typeof transformerData.formData === 'object') {
            currentData = { ...currentData, ...transformerData.formData };
        }

        // Estrutura 3: dados em inputs (possível estrutura do backend)
        if (transformerData?.inputs && typeof transformerData.inputs === 'object') {
            currentData = { ...currentData, ...transformerData.inputs };
        }

        // Estrutura 4: dados em data (outra possível estrutura)
        if (transformerData?.data && typeof transformerData.data === 'object') {
            currentData = { ...currentData, ...transformerData.data };
        }

        console.log('[applied_voltage] fillTestVoltages: currentData FINAL combinado:', JSON.stringify(currentData, null, 2));

        // Busca as tensões de ensaio em diferentes possíveis localizações
        const tensaoAt = currentData.teste_tensao_aplicada_at ||
                        currentData['teste_tensao_aplicada_at'] ||
                        currentData.tensao_ensaio_at ||
                        currentData.tensao_aplicada_at;

        const tensaoBt = currentData.teste_tensao_aplicada_bt ||
                        currentData['teste_tensao_aplicada_bt'] ||
                        currentData.tensao_ensaio_bt ||
                        currentData.tensao_aplicada_bt;

        const tensaoTer = currentData.teste_tensao_aplicada_terciario ||
                         currentData['teste_tensao_aplicada_terciario'] ||
                         currentData.tensao_ensaio_terciario ||
                         currentData.tensao_aplicada_terciario;

        const freq = currentData.frequencia ||
                    currentData['frequencia'] ||
                    null; // sem valor padrão

        console.log('[applied_voltage] fillTestVoltages: Valores FINAIS extraídos:', {
            tensaoAt, tensaoBt, tensaoTer, freq
        });

        // Verifica se encontrou pelo menos uma tensão válida
        const hasValidVoltage = (tensaoAt && !isNaN(parseFloat(tensaoAt))) ||
                               (tensaoBt && !isNaN(parseFloat(tensaoBt))) ||
                               (tensaoTer && !isNaN(parseFloat(tensaoTer)));

        console.log('[applied_voltage] fillTestVoltages: Tem tensão válida?', hasValidVoltage);

        // Preenche os displays
        setElementText('tensao-at-display', (tensaoAt && !isNaN(parseFloat(tensaoAt)) ? `${tensaoAt} kV` : '-'));
        setElementText('tensao-bt-display', (tensaoBt && !isNaN(parseFloat(tensaoBt)) ? `${tensaoBt} kV` : '-'));
        setElementText('tensao-terciario-display', (tensaoTer && !isNaN(parseFloat(tensaoTer)) ? `${tensaoTer} kV` : '-'));
        setElementText('frequencia-display', (freq && !isNaN(parseFloat(freq)) ? `${freq} Hz` : '-'));

        // Preenche o campo oculto tipo_transformador
        const tipoTrafoHidden = document.getElementById('tipo_transformador');
        if (tipoTrafoHidden) {
            tipoTrafoHidden.value = currentData.tipo_transformador || 'Trifásico';
        }

        // Log final dos elementos preenchidos
        console.log('[applied_voltage] fillTestVoltages: Elementos preenchidos:', {
            'tensao-at-display': document.getElementById('tensao-at-display')?.textContent,
            'tensao-bt-display': document.getElementById('tensao-bt-display')?.textContent,
            'tensao-terciario-display': document.getElementById('tensao-terciario-display')?.textContent,
            'frequencia-display': document.getElementById('frequencia-display')?.textContent
        });

        console.log('[applied_voltage] fillTestVoltages: Concluído com sucesso');
    } catch (error) {
        console.error('[applied_voltage] fillTestVoltages: Erro:', error);
    }
}

// Lógica para o botão "Calcular"
async function setupCalcButton() {
    const calcBtn = document.getElementById('calc-applied-voltage-btn');
    if (calcBtn) {
        calcBtn.addEventListener('click', async function() {
            console.log('🚀 [applied_voltage] BOTÃO CALCULAR CLICADO!');
            console.log('[applied_voltage] Botão Calcular Tensão Aplicada clicado!');

            // Obter valores de capacitância dos inputs
            const capAt = parseFloat(getInputValue('cap-at'));
            const capBt = parseFloat(getInputValue('cap-bt'));
            const capTer = parseFloat(getInputValue('cap-ter'));

            // Obter valores de tensão e frequência dos displays
            const freqText = document.getElementById('frequencia-display')?.textContent || '-';
            const tensaoAtText = document.getElementById('tensao-at-display')?.textContent || '-';
            const tensaoBtText = document.getElementById('tensao-bt-display')?.textContent || '-';
            const tensaoTerText = document.getElementById('tensao-terciario-display')?.textContent || '-';

            console.log('[applied_voltage] Textos obtidos dos displays:', {
                freqText, tensaoAtText, tensaoBtText, tensaoTerText
            });

            // Parse dos valores numéricos
            const freq = parseFloat(freqText.split(' ')[0]);
            const tensaoAt = tensaoAtText !== '-' ? parseFloat(tensaoAtText.split(' ')[0]) : NaN;
            const tensaoBt = tensaoBtText !== '-' ? parseFloat(tensaoBtText.split(' ')[0]) : NaN;
            const tensaoTer = tensaoTerText !== '-' ? parseFloat(tensaoTerText.split(' ')[0]) : NaN;

            console.log('[applied_voltage] Valores parseados:', {
                capAt, capBt, capTer, freq, tensaoAt, tensaoBt, tensaoTer
            });

            // Verificar se temos valores válidos para calcular
            if (isNaN(freq)) {
                console.error('[applied_voltage] Frequência inválida:', freq);
                document.getElementById('applied-voltage-error-message').textContent = 'Erro: Frequência não encontrada. Verifique se os dados básicos do transformador foram preenchidos e salvos.';
                return;
            }

            if (isNaN(tensaoAt) && isNaN(tensaoBt) && isNaN(tensaoTer)) {
                console.error('[applied_voltage] Nenhuma tensão de ensaio válida encontrada');
                document.getElementById('applied-voltage-error-message').textContent = 'Erro: Nenhuma tensão de ensaio encontrada. Vá para "Dados Básicos" e preencha as tensões de ensaio aplicada (AT/BT/Terciário), depois clique em "Salvar e Calcular".';
                return;
            }

            // Limpar mensagem de erro se chegou até aqui
            document.getElementById('applied-voltage-error-message').textContent = '';

            // Preparar dados para enviar ao backend
            const calculationData = {
                cap_at: capAt,
                cap_bt: capBt,
                cap_ter: capTer,
                tensao_at: tensaoAt,
                tensao_bt: tensaoBt,
                tensao_terciario: tensaoTer,
                frequencia: freq,
                tipo_transformador: document.getElementById('tipo_transformador')?.value || 'Trifásico'
            };

            console.log('[applied_voltage] Enviando dados para cálculo no backend:', calculationData);

            try {
                // Preparar dados no formato esperado pelo backend
                const requestData = {
                    basicData: {}, // Dados básicos serão obtidos automaticamente pelo backend
                    moduleData: calculationData
                };

                console.log('[applied_voltage] Dados preparados para envio:', requestData);

                // Enviar dados para o backend para cálculo usando o endpoint correto
                const response = await fetch('/api/transformer/modules/appliedVoltage/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
                }

                const results = await response.json();
                console.log('[applied_voltage] Resultados recebidos do backend:', results);
                console.log('[applied_voltage] Estrutura da resposta:', {
                    success: results.success,
                    hasResults: !!results.results,
                    resultsType: typeof results.results,
                    resultsKeys: results.results ? Object.keys(results.results) : 'N/A'
                });
                console.log('[applied_voltage] JSON completo da resposta:', JSON.stringify(results, null, 2));

                // Exibir resultados usando os dados do backend
                console.log('🔍 [applied_voltage] Verificando condições para exibir resultados...');
                console.log('🔍 [applied_voltage] results.success:', results.success);
                console.log('🔍 [applied_voltage] results.results:', !!results.results);
                console.log('🔍 [applied_voltage] Condição completa:', results.success && results.results);

                if (results.success && results.results) {
                    console.log('✅ [applied_voltage] Chamando displayCalculationResults...');
                    console.log('[applied_voltage] Dados dos resultados:', JSON.stringify(results.results, null, 2));
                    displayCalculationResults(results.results);
                } else {
                    console.error('❌ [applied_voltage] Erro na estrutura da resposta:', results);
                    console.error('[applied_voltage] Resposta completa:', JSON.stringify(results, null, 2));
                    throw new Error(results.message || 'Erro desconhecido no cálculo');
                }

            } catch (error) {
                console.error('[applied_voltage] Erro ao calcular no backend:', error);
                document.getElementById('applied-voltage-error-message').textContent = `Erro no cálculo: ${error.message}`;
            }
        });
    }
}

// Função para configurar o botão de debug (se existir)
function setupDebugButton() {
    const debugBtn = document.getElementById('debug-applied-voltage-btn');
    if (debugBtn) {
        debugBtn.addEventListener('click', function() {
            console.log('[applied_voltage] Botão Debug clicado!');
            console.log('[applied_voltage] Estado atual dos dados:', {
                formData: window.collectFormData ? window.collectFormData(document.getElementById('applied-voltage-form')) : 'collectFormData não disponível',
                apiSystem: !!window.apiDataSystem,
                store: window.apiDataSystem ? 'disponível' : 'não disponível'
            });
        });
        console.log('[applied_voltage] Botão de debug configurado');
    } else {
        console.log('[applied_voltage] Botão de debug não encontrado (opcional)');
    }
}

// Função para exibir os resultados do cálculo do backend
function displayCalculationResults(results) {
    console.log('🎯 [applied_voltage] displayCalculationResults: FUNÇÃO CHAMADA!');
    console.log('[applied_voltage] displayCalculationResults: Exibindo resultados:', results);
    console.log('[applied_voltage] displayCalculationResults: Tipo de results:', typeof results);
    console.log('[applied_voltage] displayCalculationResults: Keys de results:', Object.keys(results));

    // Verificar se os elementos existem na página
    const resultsElement = document.getElementById('applied-voltage-results');
    const recommendationElement = document.getElementById('resonant-system-recommendation');
    const errorElement = document.getElementById('applied-voltage-error-message');

    console.log('[applied_voltage] displayCalculationResults: Elementos encontrados:', {
        resultsElement: !!resultsElement,
        recommendationElement: !!recommendationElement,
        errorElement: !!errorElement
    });

    if (!resultsElement) {
        console.error('[applied_voltage] displayCalculationResults: Elemento applied-voltage-results não encontrado!');
        return;
    }

    // Criar layout de resultados com melhor alinhamento
    let resultsHtml = `
        <div class="row g-2">
            <div class="col-12">
                <h6 class="text-center mb-3" style="color: var(--primary-color); font-weight: bold;">Resultados Calculados</h6>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-sm table-bordered" style="font-size: 0.8rem; margin-bottom: 0;">
                <thead style="background-color: var(--background-card-header);">
                    <tr>
                        <th class="text-center" style="padding: 0.4rem; color: var(--text-light);">Lado</th>
                        <th class="text-center" style="padding: 0.4rem; color: var(--text-light);">Tensão Ensaio<br>(kV)</th>
                        <th class="text-center" style="padding: 0.4rem; color: var(--text-light);">Cap. Ensaio<br>(pF)</th>
                        <th class="text-center" style="padding: 0.4rem; color: var(--text-light);">Corrente<br>(mA)</th>
                        <th class="text-center" style="padding: 0.4rem; color: var(--text-light);">Pot. Reativa<br>(MVAr)</th>
                    </tr>
                </thead>
                <tbody>`;

    let recommendationText = '';
    let hasResults = false;
    let hasRecommendations = false;

    // Processar resultados AT
    if (results.tensao_teste_at && results.tensao_teste_at > 0) {
        console.log('[applied_voltage] displayCalculationResults: Processando AT:', {
            tensao: results.tensao_teste_at,
            capacitancia: results.capacitancia_at,
            corrente: results.corrente_teste_at,
            potencia: results.potencia_reativa_at
        });

        const currentMa = (results.corrente_teste_at * 1000).toFixed(2);
        const powerMvar = (results.potencia_reativa_at / 1000000).toFixed(3);
        const recommendation = results.analise_viabilidade_ressonante?.at || 'Não analisado';

        // Determina cor da linha baseada na viabilidade
        const rowClass = recommendation.includes('Viável') ? 'table-success' :
                        recommendation.includes('Não viável') ? 'table-warning' : '';

        resultsHtml += `
            <tr class="${rowClass}">
                <td class="text-center fw-bold" style="padding: 0.4rem;">AT</td>
                <td class="text-center" style="padding: 0.4rem;">${results.tensao_teste_at.toFixed(1)}</td>
                <td class="text-center" style="padding: 0.4rem;">${results.capacitancia_at.toFixed(0)}</td>
                <td class="text-center" style="padding: 0.4rem;">${currentMa}</td>
                <td class="text-center" style="padding: 0.4rem;">${powerMvar}</td>
            </tr>`;

        // Adiciona recomendação colorida para AT
        const atColor = recommendation.includes('Viável') ?
            (recommendation.includes('ideal') ? 'success' : 'warning') : 'danger';
        recommendationText += `
            <div class="alert alert-${atColor} p-2 mb-1" style="font-size: 0.7rem;">
                <strong>AT (Alta Tensão):</strong> ${recommendation}
            </div>`;
        hasRecommendations = true;
        hasResults = true;
    }

    // Processar resultados BT
    if (results.tensao_teste_bt && results.tensao_teste_bt > 0) {
        console.log('[applied_voltage] displayCalculationResults: Processando BT:', {
            tensao: results.tensao_teste_bt,
            capacitancia: results.capacitancia_bt,
            corrente: results.corrente_teste_bt,
            potencia: results.potencia_reativa_bt
        });

        const currentMa = (results.corrente_teste_bt * 1000).toFixed(2);
        const powerMvar = (results.potencia_reativa_bt / 1000000).toFixed(3);
        const recommendation = results.analise_viabilidade_ressonante?.bt || 'Não analisado';

        // Determina cor da linha baseada na viabilidade
        const rowClass = recommendation.includes('Viável') ? 'table-success' :
                        recommendation.includes('Não viável') ? 'table-warning' : '';

        resultsHtml += `
            <tr class="${rowClass}">
                <td class="text-center fw-bold" style="padding: 0.4rem;">BT</td>
                <td class="text-center" style="padding: 0.4rem;">${results.tensao_teste_bt.toFixed(1)}</td>
                <td class="text-center" style="padding: 0.4rem;">${results.capacitancia_bt.toFixed(0)}</td>
                <td class="text-center" style="padding: 0.4rem;">${currentMa}</td>
                <td class="text-center" style="padding: 0.4rem;">${powerMvar}</td>
            </tr>`;

        // Adiciona recomendação colorida para BT
        const btColor = recommendation.includes('Viável') ?
            (recommendation.includes('ideal') ? 'success' : 'warning') : 'danger';
        recommendationText += `
            <div class="alert alert-${btColor} p-2 mb-1" style="font-size: 0.7rem;">
                <strong>BT (Baixa Tensão):</strong> ${recommendation}
            </div>`;
        hasRecommendations = true;
        hasResults = true;
    }

    // Processar resultados Terciário
    if (results.tensao_teste_terciario && results.tensao_teste_terciario > 0) {
        console.log('[applied_voltage] displayCalculationResults: Processando Terciário:', {
            tensao: results.tensao_teste_terciario,
            capacitancia: results.capacitancia_terciario,
            corrente: results.corrente_teste_terciario,
            potencia: results.potencia_reativa_terciario
        });

        const currentMa = (results.corrente_teste_terciario * 1000).toFixed(2);
        const powerMvar = (results.potencia_reativa_terciario / 1000000).toFixed(3);
        const recommendation = results.analise_viabilidade_ressonante?.terciario || 'Não analisado';

        // Determina cor da linha baseada na viabilidade
        const rowClass = recommendation.includes('Viável') ? 'table-success' :
                        recommendation.includes('Não viável') ? 'table-warning' : '';

        resultsHtml += `
            <tr class="${rowClass}">
                <td class="text-center fw-bold" style="padding: 0.4rem;">Terciário</td>
                <td class="text-center" style="padding: 0.4rem;">${results.tensao_teste_terciario.toFixed(1)}</td>
                <td class="text-center" style="padding: 0.4rem;">${results.capacitancia_terciario.toFixed(0)}</td>
                <td class="text-center" style="padding: 0.4rem;">${currentMa}</td>
                <td class="text-center" style="padding: 0.4rem;">${powerMvar}</td>
            </tr>`;

        // Adiciona recomendação colorida para Terciário
        const tercColor = recommendation.includes('Viável') ?
            (recommendation.includes('ideal') ? 'success' : 'warning') : 'danger';
        recommendationText += `
            <div class="alert alert-${tercColor} p-2 mb-1" style="font-size: 0.7rem;">
                <strong>Terciário (Terciário):</strong> ${recommendation}
            </div>`;
        hasRecommendations = true;
        hasResults = true;
    }

    resultsHtml += `
                </tbody>
            </table>
        </div>`;
    // Se não há resultados, mostrar mensagem
    if (!hasResults) {
        resultsHtml = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-info-circle me-2"></i>
                Nenhum resultado calculado. Verifique se as tensões de ensaio foram definidas nos dados básicos.
            </div>`;
        recommendationText = '';
    } else if (!hasRecommendations) {
        recommendationText = `
            <div class="alert alert-info p-2 mb-0" style="font-size: 0.7rem;">
                <i class="fas fa-lightbulb me-1"></i>
                <strong>Recomendação:</strong> Todos os ensaios podem ser realizados com fontes convencionais.
            </div>`;
    }

    console.log('[applied_voltage] displayCalculationResults: HTML final:', resultsHtml);
    console.log('[applied_voltage] displayCalculationResults: Recomendações:', recommendationText);

    // Exibir resultados na interface
    resultsElement.innerHTML = resultsHtml;

    // Exibir recomendações com formatação HTML
    if (recommendationElement) {
        recommendationElement.innerHTML = recommendationText;
    }

    if (errorElement) {
        errorElement.textContent = '';
    }

    // Salvar resultados no store
    saveCalculationResults(results);
}

// Função para salvar os resultados do cálculo
async function saveCalculationResults(results) {
    try {
        if (window.apiDataSystem) {
            const appliedVoltageStore = window.apiDataSystem.getStore('appliedVoltage');
            const currentStoreData = await appliedVoltageStore.getData() || {};
            const newFormData = collectFormData(document.getElementById('applied-voltage-form'));

            // Preservar dados existentes e adicionar novos resultados
            const updatedStoreData = {
                calculationResults: results,
                formData: { ...(currentStoreData.formData || {}), ...newFormData },
                timestamp: new Date().toISOString()
            };

            await appliedVoltageStore.updateData(updatedStoreData);
            console.log('[applied_voltage] Resultados salvos no store:', updatedStoreData);
        }
    } catch (error) {
        console.error('[applied_voltage] Erro ao salvar resultados:', error);
    }
}

// Função para resetar resultados de cálculos de tensão aplicada
function resetAppliedVoltageResults() {
    console.log('[applied_voltage] resetAppliedVoltageResults: Resetando resultados de cálculos');

    try {
        // Limpar área de resultados
        const resultsContainer = document.getElementById('applied-voltage-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="text-center text-muted py-3"><i class="fas fa-info-circle me-2"></i>Aguardando cálculo...</div>';
        }

        // Limpar recomendação do sistema
        const recommendationContainer = document.getElementById('resonant-system-recommendation');
        if (recommendationContainer) {
            recommendationContainer.innerHTML = 'Aguardando cálculo...';
        }

        // Limpar mensagem de erro
        const errorElement = document.getElementById('applied-voltage-error-message');
        if (errorElement) {
            errorElement.innerHTML = '';
        }

        console.log('[applied_voltage] Resultados de tensão aplicada resetados');

    } catch (error) {
        console.error('[applied_voltage] Erro ao resetar resultados:', error);
    }
}


// Função para verificar se há dados válidos no transformer inputs
async function checkTransformerInputsData() {
    try {
        console.log('[applied_voltage] checkTransformerInputsData: Verificando dados do transformer inputs...');

        await waitForApiSystem();
        const store = window.apiDataSystem.getStore('transformerInputs');
        const data = await store.getData();

        console.log('[applied_voltage] checkTransformerInputsData: Dados RAW do store:', JSON.stringify(data, null, 2));

        if (!data || Object.keys(data).length === 0) {
            console.warn('[applied_voltage] checkTransformerInputsData: Store vazio ou inexistente');
            return false;
        }

        // Verifica se há dados de tensão de ensaio em qualquer estrutura
        const hasTestVoltages = (
            data.teste_tensao_aplicada_at ||
            data.teste_tensao_aplicada_bt ||
            data.teste_tensao_aplicada_terciario ||
            data.formData?.teste_tensao_aplicada_at ||
            data.formData?.teste_tensao_aplicada_bt ||
            data.formData?.teste_tensao_aplicada_terciario ||
            data.inputs?.teste_tensao_aplicada_at ||
            data.inputs?.teste_tensao_aplicada_bt ||
            data.inputs?.teste_tensao_aplicada_terciario
        );

        console.log('[applied_voltage] checkTransformerInputsData: Tem tensões de ensaio?', hasTestVoltages);

        return hasTestVoltages;
    } catch (error) {
        console.error('[applied_voltage] checkTransformerInputsData: Erro:', error);
        return false;
    }
}

// Função de inicialização do módulo Tensão Aplicada
async function initAppliedVoltage() {
    console.log('[applied_voltage] Módulo Tensão Aplicada carregado e pronto para interatividade.');

    try {
        // Aguarda o sistema de API estar disponível
        await waitForApiSystem();

        // A validação de dados agora é feita pelo painel de informações do transformador

        // ID do placeholder para o painel de informações do transformador
        const transformerInfoPlaceholderId = 'transformer-info-applied_voltage-page';

        // Verificar se o elemento existe antes de tentar carregar o painel
        const placeholderElement = document.getElementById(transformerInfoPlaceholderId);
        if (placeholderElement) {
            await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId); // Carrega o painel no topo
        } else {
            console.warn(`[applied_voltage] Elemento ${transformerInfoPlaceholderId} não encontrado - módulo pode não estar ativo`);
        }

        // Configurar persistência de dados usando API do backend
        if (window.setupApiFormPersistence) {
            await window.setupApiFormPersistence('applied-voltage-form', 'appliedVoltage');
            console.log('[applied_voltage] Persistência configurada com sucesso');
        } else {
            console.warn('[applied_voltage] Sistema de persistência não disponível');
        }

        // Carregar e preencher os dados do próprio módulo de tensão aplicada
        await loadAppliedVoltageDataAndPopulateForm();

        // Preenche as tensões de ensaio ao carregar o módulo
        await fillTestVoltages();

        // Configura o botão de cálculo
        setupCalcButton();

        // Configura o botão de debug
        setupDebugButton();

        // Adicionar listener para o evento transformerDataUpdated
        document.addEventListener('transformerDataUpdated', async (event) => {
            console.log('[applied_voltage] Evento transformerDataUpdated recebido:', event.detail);

            // REMOVIDO resetAppliedVoltageResults() - já é feito pelo sistema de ocultação em common_module.js
            // resetAppliedVoltageResults();

            // Recarrega os dados do próprio módulo para garantir consistência (incluindo resultados salvos)
            await loadAppliedVoltageDataAndPopulateForm();

            // Atualiza as tensões de ensaio que dependem dos dados básicos
            await fillTestVoltages();

            // Recarregar o painel de informações do transformador
            const transformerInfoPlaceholderId = 'transformer-info-applied_voltage-page';

            // Verificar se o elemento existe antes de tentar carregar o painel
            const placeholderElement = document.getElementById(transformerInfoPlaceholderId);
            if (placeholderElement) {
                await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);
            } else {
                console.warn(`[applied_voltage] Elemento ${transformerInfoPlaceholderId} não encontrado - módulo pode não estar ativo`);
            }

            // A validação de dados agora é feita pelo painel de informações do transformador

            console.log('[applied_voltage] Dados do transformador atualizados - resultados resetados');
        });

        console.log('[applied_voltage] Inicialização concluída com sucesso');
    } catch (error) {
        console.error('[applied_voltage] Erro durante a inicialização:', error);
    }
}

// SPA routing: executa quando o módulo applied_voltage é carregado
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'applied_voltage') {
        console.log('[applied_voltage] SPA routing init');
        initAppliedVoltage();
    }
});

// Fallback para carregamento direto da página (se não for SPA)
document.addEventListener('DOMContentLoaded', () => {
    // Verifica se o elemento principal do módulo está presente para evitar execução em outras páginas
    if (document.getElementById('applied-voltage-form')) {
        console.log('[applied_voltage] DOMContentLoaded init (fallback)');
        initAppliedVoltage();
    }
});