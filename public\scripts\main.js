// public/scripts/main.js - Versão Atualizada para Roteamento SPA

import { waitForApiSystem } from './api_persistence.js';

// Variáveis globais para o contador de uso
let currentUsage = 0;
const MAX_USAGE = 1000;

// Nova função para inicializar o contador de uso do store
async function initUsageCounter() {
    console.log('[UsageCounter] Inicializando contador de uso...');
    try {
        await waitForApiSystem();
        if (window.apiDataSystem) {
            const usageStore = window.apiDataSystem.getStore('usageCounter');
            const data = await usageStore.getData();
            if (data && typeof data.count === 'number') {
                currentUsage = data.count;
                console.log(`[UsageCounter] Contador de uso carregado do store: ${currentUsage}`);
            } else {
                currentUsage = 0; // Inicializa se não houver dados
                console.log('[UsageCounter] Nenhum dado de contador de uso encontrado, inicializando com 0.');
            }
            updateUsageDisplay();
        } else {
            console.warn('[UsageCounter] apiDataSystem não disponível, contador de uso não será persistido.');
            currentUsage = 0;
            updateUsageDisplay();
        }
    } catch (error) {
        console.error('[UsageCounter] Erro ao inicializar contador de uso:', error);
        currentUsage = 0;
        updateUsageDisplay();
    }
}

// Função para atualizar a exibição do contador de uso
function updateUsageDisplay() {
    const usageValueElement = document.getElementById('usage-value');
    const usageBarElement = document.getElementById('usage-bar');
    const limitAlertDiv = document.getElementById('limit-alert-div');

    if (usageValueElement) usageValueElement.textContent = currentUsage;
    if (usageBarElement) {
        const percentage = (currentUsage / MAX_USAGE) * 100;
        usageBarElement.style.width = `${Math.min(percentage, 100)}%`;
    }
    if (limitAlertDiv) {
        if (currentUsage >= MAX_USAGE) {
            limitAlertDiv.classList.remove('d-none');
        } else {
            limitAlertDiv.classList.add('d-none');
        }
    }
}

// Função simples para incrementar contador apenas quando o usuário entra no programa
async function incrementUsageAndSave() {
    currentUsage++;
    updateUsageDisplay();
    console.log(`[UsageCounter] Incrementando uso para: ${currentUsage} (entrada no programa)`);

    if (window.apiDataSystem) {
        try {
            const usageStore = window.apiDataSystem.getStore('usageCounter');
            await usageStore.updateData({
                count: currentUsage,
                lastAccess: new Date().toISOString()
            });
            console.log(`[UsageCounter] Contador de uso salvo no store: ${currentUsage}`);
        } catch (error) {
            console.error('[UsageCounter] Erro ao salvar contador de uso:', error);
        }
    }
}

document.addEventListener('DOMContentLoaded', async () => { // Tornar o listener DOMContentLoaded assíncrono
    // Chamar a nova função de inicialização do contador de uso
    await initUsageCounter(); // Garante que o contador seja carregado antes de qualquer uso

    // --- Existing Functionality (Preserved and Integrated) ---
    // Theme Toggle
    const themeToggle = document.getElementById('theme-toggle');
    const themeToggleIcon = document.getElementById('theme-toggle-icon');
    const themeToggleText = document.getElementById('theme-toggle-text');
    const body = document.body;
    
    // Function to apply the selected theme
    function applyTheme(theme) {
        body.removeAttribute('data-bs-theme'); // Remove old one first
        body.setAttribute('data-bs-theme', theme);

        if (themeToggleIcon) {
            themeToggleIcon.classList.remove('fa-sun', 'fa-moon'); // Remove both icons
        }

        if (theme === 'dark') {
            if (themeToggleIcon) themeToggleIcon.classList.add('fa-moon'); // Add moon for dark theme
            if (themeToggleText) themeToggleText.textContent = 'Tema Escuro';
        } else { // light theme
            if (themeToggleIcon) themeToggleIcon.classList.add('fa-sun'); // Add sun for light theme
            if (themeToggleText) themeToggleText.textContent = 'Tema Claro';
        }
        localStorage.setItem('theme', theme);
        console.log(`[Theme] Tema aplicado: ${theme}, data-bs-theme:`, body.getAttribute('data-bs-theme'));

        // Dispatch a custom event when the theme changes
        const event = new CustomEvent('themeChanged', { detail: { theme: theme } });
        document.dispatchEvent(event);
    }

    // Initialize theme based on localStorage or default to dark
    const storedTheme = localStorage.getItem('theme');
    const initialTheme = storedTheme || 'dark'; // Default to dark theme
    applyTheme(initialTheme); // Apply the initial theme

    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            let currentTheme = body.getAttribute('data-bs-theme');
            let newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            applyTheme(newTheme);
        });
    }


    
    // Usage Counter Simulation - elementos DOM serão acessados pelas funções globais

    // --- SPA Routing Logic ---
    const mainContentArea = document.getElementById('main-content-area');
    // CORREÇÃO: O seletor agora aponta para o ID do div que contém os links de navegação.
    const navLinks = document.querySelectorAll('#navbarContent .nav-link[data-module]'); 
    
    const homeLink = document.getElementById('home-lO Nink');
    let currentModuleScriptTag = null;
    let currentLoadedModule = null;
    const moduleCache = new Map();

    // Função para limpar cache de módulos específicos
    window.clearModuleCache = function(moduleName) {
        if (moduleName) {
            moduleCache.delete(moduleName);
            console.log(`[clearModuleCache] Cache do módulo ${moduleName} limpo`);
        } else {
            moduleCache.clear();
            console.log(`[clearModuleCache] Cache de todos os módulos limpo`);
        }
    };

    function setActiveNavLink(moduleName) {
        navLinks.forEach(link => {
            if (link.dataset.module === moduleName) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    async function loadModulePage(moduleName, pushToHistory = true) {
        console.log(`🔄 [FLUXO] ===== NAVEGANDO PARA MÓDULO: ${moduleName} =====`);

        if (!moduleName) {
            console.warn('loadModulePage chamada sem moduleName. Usando transformer_inputs como padrão.');
            moduleName = 'transformer_inputs';
        }

        // Evita recarregar o mesmo módulo apenas se não for navegação do histórico
        if (currentLoadedModule === moduleName && pushToHistory) {
            console.log(`[main.js] Módulo ${moduleName} já está carregado. Ignorando.`);
            return;
        }

        // Limpa cache automaticamente se for um recarregamento (não pushToHistory)
        if (!pushToHistory && moduleCache.has(moduleName)) {
            moduleCache.delete(moduleName);
            console.log(`[main.js] Cache do módulo ${moduleName} limpo automaticamente para recarregamento`);
        }        // Caminhos para os arquivos HTML e JS
        const htmlFilePath = `pages/${moduleName}.html`;

        // Mostra um indicador de carregamento
        if (mainContentArea) {
            mainContentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="ms-3 mb-0">Carregando módulo ${moduleName}...</p>
                </div>`;
        }

        try { // Outer try for HTML fetch
            const response = await fetch(htmlFilePath);
            if (!response.ok) {
                throw new Error(`Erro HTTP ${response.status} ao buscar ${htmlFilePath}`);
            }
            const htmlContent = await response.text();
            console.log(`[main.js] HTML carregado com sucesso para o módulo: ${moduleName}`);
            if (mainContentArea) mainContentArea.innerHTML = htmlContent;

            // Remove previous script tag if it exists (for fallback approach)
            if (currentModuleScriptTag) {
                 // If we used scriptTag.remove() previously, we'd do it here.
                 // Since we didn't, just clear the reference.
                 currentModuleScriptTag = null;
            }

            try { // Inner try for script import
                // Verifica cache primeiro
                let module;
                if (moduleCache.has(moduleName)) {
                    module = moduleCache.get(moduleName);
                    console.log(`[main.js] Módulo ${moduleName}.js carregado do cache.`, module);
                } else {
                    // SEMPRE adiciona timestamp para evitar cache do browser
                    const scriptFilePath = `/scripts/${moduleName}.js`; // Define scriptFilePath
                    const scriptFilePathWithCache = `${scriptFilePath}?t=${Date.now()}`;
                    console.log(`[main.js] Tentando carregar módulo de: ${scriptFilePathWithCache}`);
                    // Adiciona log antes de tentar o import dinâmico
                    console.log(`[main.js] Caminho do script para import dinâmico: ${window.location.origin}${scriptFilePathWithCache}`);
                    module = await import(scriptFilePathWithCache);
                    moduleCache.set(moduleName, module);
                    console.log(`[main.js] Módulo ${moduleName}.js carregado dinamicamente com timestamp.`, module);
                }

                currentLoadedModule = moduleName;
                // Dispatch event after successful module load/import
                document.dispatchEvent(new CustomEvent('moduleContentLoaded', { detail: { moduleName } }));
                console.log(`[main.js] Evento moduleContentLoaded disparado para ${moduleName}`);

                // Navegação entre módulos não conta como uso significativo

                // Update UI and history after successful load
                setActiveNavLink(moduleName);
                if (pushToHistory) {
                    history.pushState({ module: moduleName }, `${moduleName.replace('_', ' ')} - Simulador`, `#${moduleName}`);
                } else {
                    document.title = `${moduleName.replace('_', ' ')} - Simulador`;
                }            } catch (scriptLoadError) { // Catch for inner try (script loading)
                console.error(`[main.js] Erro ao carregar o script do módulo ${moduleName} em scripts/${moduleName}.js:`, scriptLoadError);
                // Remove from cache in case of error to force reload next time
                if (moduleCache.has(moduleName)) {
                    moduleCache.delete(moduleName);
                    console.log(`[main.js] Módulo ${moduleName} removido do cache devido ao erro`);
                }

                // If it's a network error or similar, try script tag fallback
                if (scriptLoadError instanceof TypeError && scriptLoadError.message.includes('Failed to fetch dynamically imported module')) {
                     console.log(`[main.js] Tentando abordagem alternativa (script tag) para carregar o módulo ${moduleName}`);
                     try {
                         const scriptTag = document.createElement('script');
                         scriptTag.src = `/scripts/${moduleName}.js?t=${Date.now()}`;
                         scriptTag.type = 'module';
                         document.head.appendChild(scriptTag);
                         currentModuleScriptTag = scriptTag; // Store the tag reference
                         console.log(`[main.js] Script tag criada como fallback para ${moduleName}`);
                         currentLoadedModule = moduleName; // Update loaded module even on fallback attempt

                         // Wait for script to load and execute (approximation)
                         scriptTag.onload = () => {
                             document.dispatchEvent(new CustomEvent('moduleContentLoaded', { detail: { moduleName } }));
                             console.log(`[main.js] Evento disparado via fallback (onload) para ${moduleName}`);
                             // Update UI and history after successful fallback load
                             setActiveNavLink(moduleName);
                             if (pushToHistory) {

                                 history.pushState({ module: moduleName }, `${moduleName.replace('_', ' ')} - Simulador`, `#${moduleName}`);
                             } else {
                                 document.title = `${moduleName.replace('_', ' ')} - Simulador`;
                             }
                         };                         scriptTag.onerror = (e) => {
                             console.error(`[main.js] Erro ao carregar script tag fallback para ${moduleName} em ${scriptTag.src}:`, e);
                             // Dispatch error event or handle failure
                             // Maybe show an error message in mainContentArea?
                             if (mainContentArea) {
                                 mainContentArea.innerHTML = `<div class="alert alert-danger m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--danger-color);">
                                                                     <i class="fas fa-exclamation-circle me-2"></i> Erro ao carregar o script do módulo: ${moduleName} via fallback. Verifique o console.
                                                                 </div>`;
                             }
                             setActiveNavLink(null); // Clear active link on failure
                         };

                     } catch (scriptTagError) {
                         console.error(`[main.js] Erro ao usar fallback para ${moduleName}:`, scriptTagError);
                          if (mainContentArea) {
                             mainContentArea.innerHTML = `<div class="alert alert-danger m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--danger-color);">
                                                             <i class="fas fa-exclamation-circle me-2"></i> Erro crítico ao tentar carregar o módulo: ${moduleName}. Verifique o console.
                                                         </div>`;
                         }
                         setActiveNavLink(null); // Clear active link on failure
                     }
                } 
                else {
                    // Handle other script loading errors (SyntaxError, ReferenceError, etc.)
                     if (mainContentArea) {
                         mainContentArea.innerHTML = `<div class="alert alert-danger m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--danger-color);">
                                                         <i class="fas fa-exclamation-circle me-2"></i> Erro ao executar o script do módulo: ${moduleName}. Verifique o console.
                                                     </div>`;
                     }
                     setActiveNavLink(null); // Clear active link on failure
                }
            } // End of inner try/catch (script loading)


        } catch (error) { // Catch for outer try (HTML loading)
            console.error(`Erro ao carregar o HTML do módulo ${moduleName}:`, error);
            if (mainContentArea) {
                mainContentArea.innerHTML = `<div class="alert alert-danger m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--danger-color);">
                                                <i class="fas fa-exclamation-circle me-2"></i> Erro ao carregar o HTML do módulo: ${moduleName}. Verifique o console.
                                            </div>`;
            }
            setActiveNavLink(null); // Clear active link on failure
        }
    }

    navLinks.forEach(link => {
        link.addEventListener('click', (event) => {
            event.preventDefault();
            const moduleName = link.dataset.module;
            if (moduleName) {
                const currentHashModule = window.location.hash.substring(1);
                if (moduleName !== currentHashModule) {
                    loadModulePage(moduleName);
                } else {
                    console.log("Módulo já ativo:", moduleName);
                }
            }
            // Para fechar o menu hamburger em mobile após clicar
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('#navbarContent');
            if (navbarToggler && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, { toggle: false });
                bsCollapse.hide();
            }
        });
    });
    
    if (homeLink) {
        homeLink.addEventListener('click', (event) => {
            event.preventDefault();
            loadModulePage('transformer_inputs', true);
            // Também fechar o menu hamburger em mobile
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('#navbarContent');
            if (navbarToggler && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, { toggle: false });
                bsCollapse.hide();
            }
        });
    }

    window.addEventListener('popstate', (event) => {
        let moduleToLoad = 'transformer_inputs';
        if (event.state && event.state.module) {
            moduleToLoad = event.state.module;
        } else {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === hash);
                if (isValidModule) {
                    moduleToLoad = hash;
                } else {
                    console.warn(`Módulo inválido no hash em popstate: ${hash}. Carregando padrão.`);
                }
            }
        }
        loadModulePage(moduleToLoad, false);
    });    // Função inteligente para verificar se existem dados de projeto anterior
    async function checkForExistingData() {
        try {
            console.log('[checkForExistingData] Iniciando verificação de dados existentes...');
            const apiSystem = await waitForApiSystem();
            if (!apiSystem) {
                console.log('[checkForExistingData] apiSystem não disponível');
                return false;
            }

            await apiSystem.init();
            console.log('[checkForExistingData] apiSystem inicializado');

            // Lista de stores importantes que indicam um projeto ativo
            const importantStores = ['transformerInputs', 'losses', 'shortCircuit', 'appliedVoltage', 'inducedVoltage', 'temperatureRise', 'dielectricAnalysis', 'impulse'];
            let hasSignificantData = false;

            for (const storeName of importantStores) {
                try {
                    const store = apiSystem.getStore(storeName);
                    const data = await store.getData();

                    if (data && data.formData && Object.keys(data.formData).length > 0) {
                        // Verifica se há campos importantes preenchidos
                        const hasValidData = Object.entries(data.formData).some(([, value]) => {
                            // Ignora campos vazios, nulos ou apenas espaços
                            if (value === null || value === undefined || value === '') return false;
                            if (typeof value === 'string' && value.trim() === '') return false;

                            // Considera campos numéricos válidos (incluindo 0)
                            if (typeof value === 'number') return true;

                            // Considera strings não-vazias válidas
                            if (typeof value === 'string' && value.trim().length > 0) return true;

                            return false;
                        });

                        if (hasValidData) {
                            console.log(`[checkForExistingData] Dados válidos encontrados no store '${storeName}':`, data.formData);
                            hasSignificantData = true;
                            break; // Encontrou dados, não precisa verificar outros stores
                        }
                    }

                    // Verifica também se há resultados de cálculos salvos
                    if (data && (data.results || data.resultsNoLoad || data.resultsLoad)) {
                        console.log(`[checkForExistingData] Resultados de cálculos encontrados no store '${storeName}'`);
                        hasSignificantData = true;
                        break;
                    }
                } catch (storeError) {
                    console.warn(`[checkForExistingData] Erro ao verificar store '${storeName}':`, storeError);
                    // Continua verificando outros stores
                }
            }

            console.log('[checkForExistingData] Resultado final:', hasSignificantData);
            return hasSignificantData;
        } catch (error) {
            console.error('[checkForExistingData] Erro ao verificar dados existentes:', error);
            return false;
        }
    }



    // Função para limpar todos os dados
    async function clearAllData() {
        try {
            console.log('🧹 [FLUXO] LIMPEZA: INICIANDO LIMPEZA COMPLETA DE DADOS');

            const apiSystem = await waitForApiSystem();
            if (!apiSystem) return;

            // Lista de stores conhecidos para limpar
            const storesToClear = ['transformerInputs', 'losses', 'shortCircuit', 'appliedVoltage', 'inducedVoltage', 'temperatureRise', 'dielectricAnalysis', 'impulse', 'sessions'];

            for (const storeId of storesToClear) {
                try {
                    const store = apiSystem.getStore(storeId);

                    // Limpa o cache do store ANTES de atualizar
                    if (store.clearCache) {
                        store.clearCache();
                        console.log(`🧹 [FLUXO] LIMPEZA: Cache do store '${storeId}' limpo.`);
                    }

                    // Para limpeza completa, usar setData (PUT) em vez de updateData (PATCH)
                    // setData substitui COMPLETAMENTE, updateData faz merge
                    const emptyData = {};

                    await store.setData(emptyData);
                    console.log(`🧹 [FLUXO] LIMPEZA: Store '${storeId}' SUBSTITUÍDO COMPLETAMENTE (setData).`);

                } catch (error) {
                    console.warn(`❌ [FLUXO] LIMPEZA: Erro ao limpar store '${storeId}':`, error);
                }
            }

            // Limpar localStorage também
            clearLocalStorageData();

            // Limpar todos os formulários no frontend
            clearAllFormFields();

            // Limpar templates de informações do transformador (DESABILITADO - causava limpeza dos dados básicos)
            // clearTransformerInfoTemplates();

            // Forçar limpeza adicional de todos os caches
            await forceClearAllCaches();

            // Forçar recarregamento de todos os templates vazios
            await forceReloadEmptyTemplates();

            // Resetar o contador de uso no frontend após a limpeza
            currentUsage = 0;
            updateUsageDisplay();
            console.log('[clearAllData] Contador de uso resetado no frontend.');

            // Verificar se a limpeza funcionou
            await verifyDataClearing();

            console.log('✅ [FLUXO] LIMPEZA: Todos os dados foram limpos.');
        } catch (error) {
            console.error('❌ [FLUXO] LIMPEZA: Erro ao limpar dados:', error);
        }
    }

    // Função para verificar se a limpeza funcionou
    async function verifyDataClearing() {
        try {
            console.log('🔍 [FLUXO] VERIFICAÇÃO: Iniciando verificação de limpeza...');

            // Aguarda um pouco para garantir que a limpeza foi processada
            await new Promise(resolve => setTimeout(resolve, 500));

            const apiSystem = await waitForApiSystem();
            if (!apiSystem) return;

            // Lista de stores para verificar
            const storesToCheck = ['transformerInputs', 'losses', 'appliedVoltage', 'inducedVoltage', 'temperatureRise', 'shortCircuit'];

            for (const storeId of storesToCheck) {
                try {
                    const store = apiSystem.getStore(storeId);
                    const data = await store.getData();

                    if (data && data.formData && Object.keys(data.formData).length > 0) {
                        console.log(`🔍 [FLUXO] VERIFICAÇÃO: Store '${storeId}' ainda tem dados:`, data.formData);

                        // Verifica se há valores não-nulos
                        const nonNullValues = Object.entries(data.formData).filter(([key, value]) =>
                            value !== null && value !== undefined && value !== ''
                        );

                        if (nonNullValues.length > 0) {
                            console.warn(`⚠️ [FLUXO] VERIFICAÇÃO: Store '${storeId}' tem ${nonNullValues.length} valores não-nulos:`, nonNullValues);
                        } else {
                            console.log(`✅ [FLUXO] VERIFICAÇÃO: Store '${storeId}' limpo corretamente (apenas valores nulos).`);
                        }
                    } else {
                        console.log(`✅ [FLUXO] VERIFICAÇÃO: Store '${storeId}' completamente vazio.`);
                    }
                } catch (error) {
                    console.error(`❌ [FLUXO] VERIFICAÇÃO: Erro ao verificar store '${storeId}':`, error);
                }
            }

            console.log('🔍 [FLUXO] VERIFICAÇÃO: Verificação de limpeza concluída.');
        } catch (error) {
            console.error('❌ [FLUXO] VERIFICAÇÃO: Erro na verificação:', error);
        }
    }

    // Função para forçar limpeza de todos os caches
    async function forceClearAllCaches() {
        try {
            console.log('🧹 [FLUXO] CACHE: Forçando limpeza de TODOS os caches...');

            const apiSystem = await waitForApiSystem();
            if (!apiSystem) return;

            // Lista de todos os stores para limpar cache
            const allStores = ['transformerInputs', 'losses', 'shortCircuit', 'appliedVoltage',
                              'inducedVoltage', 'temperatureRise', 'dielectricAnalysis', 'impulse', 'sessions'];

            for (const storeId of allStores) {
                try {
                    const store = apiSystem.getStore(storeId);
                    if (store && store.clearCache) {
                        store.clearCache();
                        console.log(`🧹 [FLUXO] CACHE: Cache do store '${storeId}' forçadamente limpo.`);
                    }
                } catch (error) {
                    console.warn(`⚠️ [FLUXO] CACHE: Erro ao limpar cache do store '${storeId}':`, error);
                }
            }

            console.log('✅ [FLUXO] CACHE: Limpeza forçada de todos os caches concluída.');
        } catch (error) {
            console.error('❌ [FLUXO] CACHE: Erro na limpeza forçada:', error);
        }
    }

    // Função para limpar dados do localStorage
    function clearLocalStorageData() {
        try {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('transformer') || key.includes('losses') || key.includes('voltage') || key.includes('temperature') || key.includes('circuit') || key.includes('dielectric') || key.includes('impulse'))) {
                    keysToRemove.push(key);
                }
            }

            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log(`[clearLocalStorageData] Removido: ${key}`);
            });
        } catch (error) {
            console.error('[clearLocalStorageData] Erro ao limpar localStorage:', error);
        }
    }

    // Função para limpar templates de informações do transformador
    function clearTransformerInfoTemplates() {
        try {
            console.log('🧹 [FLUXO] LIMPEZA: INICIANDO LIMPEZA COMPLETA DE TEMPLATES');

            // Procura por todos os elementos que contêm templates de informações do transformador
            const infoElements = document.querySelectorAll('[id*="transformer-info"]');
            console.log(`🧹 [FLUXO] LIMPEZA: Encontrados ${infoElements.length} templates para limpar`);

            infoElements.forEach(element => {
                // Limpa o conteúdo do template COMPLETAMENTE
                element.innerHTML = '';
                console.log(`🧹 [FLUXO] LIMPEZA: Template HTML limpo: ${element.id}`);
            });

            // Lista COMPLETA de todos os campos do template para limpeza forçada
            const allTemplateFields = [
                // Especificações Gerais
                'info-potencia-mva', 'info-frequencia', 'info-tipo-transformador', 'info-grupo-ligacao',
                'info-liquido-isolante', 'info-norma-iso', 'info-elevacao-oleo-topo', 'info-elevacao-enrol',
                'info-peso-parte-ativa', 'info-peso-tanque', 'info-peso-oleo', 'info-peso-total', 'info-tipo-isolamento',

                // Alta Tensão (AT)
                'info-tensao-at', 'info-classe-tensao-at', 'info-corrente-nominal-at', 'info-impedancia',
                'info-nbi-at', 'info-sil-im-at', 'info-conexao-at', 'info-tensao-neutro-at', 'info-nbi-neutro-at',
                'info-sil-im-neutro-at', 'info-iac-at',

                // TAPs AT
                'info-tensao-at-tap-maior', 'info-tensao-at-tap-menor', 'info-corrente-nominal-at-tap-maior',
                'info-corrente-nominal-at-tap-menor', 'info-impedancia-tap-maior', 'info-impedancia-tap-menor',
                'info-degrau-comutador', 'info-num-degraus-comutador', 'info-posicao-comutador',

                // Baixa Tensão (BT)
                'info-tensao-bt', 'info-classe-tensao-bt', 'info-corrente-nominal-bt', 'info-nbi-bt',
                'info-sil-im-bt', 'info-conexao-bt', 'info-classe-neutro-bt', 'info-nbi-neutro-bt',
                'info-sil-im-neutro-bt', 'info-iac-bt',

                // Terciário
                'info-tensao-terciario', 'info-classe-tensao-terciario', 'info-corrente-nominal-terciario',
                'info-nbi-terciario', 'info-sil-im-terciario', 'info-conexao-terciario', 'info-classe-neutro-terciario',
                'info-nbi-neutro-terciario', 'info-sil-im-neutro-terciario', 'info-impedancia-at-terciario',
                'info-impedancia-bt-terciario', 'info-iac-terciario',

                // Tensões de Ensaio
                'info-teste-tensao-aplicada-at', 'info-teste-tensao-induzida-at', 'info-teste-tensao-aplicada-bt',
                'info-teste-tensao-aplicada-terciario',

                // Ensaios e Perdas
                'info-perdas-vazio', 'info-perdas-curto-circuito', 'info-corrente-excitacao', 'info-fator-k',
                'info-classe-precisao', 'info-frequencia-ressonancia'
            ];

            // Força limpeza de TODOS os campos do template
            allTemplateFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = '-';
                    console.log(`🧹 [FLUXO] LIMPEZA: Campo limpo: ${fieldId}`);
                }
            });

            console.log('✅ [FLUXO] LIMPEZA: Templates de informações do transformador COMPLETAMENTE limpos.');
        } catch (error) {
            console.error('❌ [FLUXO] LIMPEZA: Erro ao limpar templates:', error);
        }
    }

    // Função para forçar recarregamento de templates vazios
    async function forceReloadEmptyTemplates() {
        try {
            console.log('[forceReloadEmptyTemplates] FORÇANDO RECARREGAMENTO DE TEMPLATES VAZIOS');

            // Aguarda um pouco para garantir que a limpeza foi processada
            await new Promise(resolve => setTimeout(resolve, 100));

            // Procura por todos os elementos de template e força recarregamento com dados vazios
            const infoElements = document.querySelectorAll('[id*="transformer-info"]');

            for (const element of infoElements) {
                if (window.loadAndPopulateTransformerInfo) {
                    console.log(`[forceReloadEmptyTemplates] Recarregando template vazio: ${element.id}`);
                    await window.loadAndPopulateTransformerInfo(element.id);
                }
            }

            console.log('[forceReloadEmptyTemplates] Recarregamento de templates vazios concluído');
        } catch (error) {
            console.error('[forceReloadEmptyTemplates] Erro ao forçar recarregamento:', error);
        }
    }

    // Função para limpar todos os campos de formulário no frontend
    function clearAllFormFields() {
        try {
            // Limpar todos os inputs de texto e número
            const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[type="email"], input[type="tel"]');
            inputs.forEach(input => {
                if (!input.disabled && !input.readOnly) {
                    input.value = '';
                }
            });

            // Limpar todos os selects (resetar para primeira opção que geralmente é "Selecione...")
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                if (!select.disabled) {
                    select.selectedIndex = 0; // Seleciona a primeira opção
                }
            });

            // Limpar textareas
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                if (!textarea.disabled && !textarea.readOnly) {
                    textarea.value = '';
                }
            });

            // Limpar checkboxes e radio buttons
            const checkboxes = document.querySelectorAll('input[type="checkbox"], input[type="radio"]');
            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = false;
                }
            });

            console.log('[clearAllFormFields] Todos os campos de formulário foram limpos.');
        } catch (error) {
            console.error('[clearAllFormFields] Erro ao limpar campos:', error);
        }
    }

    // Função para mostrar modal de inicialização
    async function showStartupModal() {
        console.log('[showStartupModal] Iniciando exibição do modal...');
        return new Promise((resolve) => {
            const startupModalElement = document.getElementById('startupModal');
            console.log('[showStartupModal] Elemento do modal encontrado:', !!startupModalElement);
            if (!startupModalElement) {
                console.warn('[showStartupModal] Modal não encontrado no DOM, resolvendo com continue');
                resolve('continue'); // Default se modal não existir
                return;
            }

            console.log('[showStartupModal] Criando instância do modal Bootstrap...');
            const startupModal = new bootstrap.Modal(startupModalElement);

            const continueButton = document.getElementById('continueProjectButton');
            const newProjectButton = document.getElementById('newProjectButton');
            console.log('[showStartupModal] Botões encontrados - Continue:', !!continueButton, 'New:', !!newProjectButton);

            const handleContinue = () => {
                startupModal.hide();
                resolve('continue');
            };

            const handleNewProject = async () => {
                console.log('🧹 [FLUXO] MODAL: Usuário escolheu NOVO PROJETO - iniciando limpeza');
                startupModal.hide();
                await clearAllData();
                console.log('✅ [FLUXO] MODAL: Limpeza concluída - dados devem estar vazios');
                resolve('new');
            };

            // Remove listeners anteriores se existirem
            if (continueButton) {
                continueButton.replaceWith(continueButton.cloneNode(true));
                document.getElementById('continueProjectButton').addEventListener('click', handleContinue);
            }

            if (newProjectButton) {
                newProjectButton.replaceWith(newProjectButton.cloneNode(true));
                document.getElementById('newProjectButton').addEventListener('click', handleNewProject);
            }

            console.log('[showStartupModal] Exibindo modal...');
            startupModal.show();
        });
    }

    async function initializeAppRouting() { // Tornar a função assíncrona
        console.log('🚀 [FLUXO] ===== INICIANDO APLICAÇÃO =====');

        // Aguarda o sistema de persistência estar disponível
        console.log('[initializeAppRouting] Aguardando sistema de persistência...');
        const apiSystem = await waitForApiSystem(); // Usa a função importada diretamente

        if (apiSystem) {
            console.log('[initializeAppRouting] Inicializando sistema de persistência...');
            await apiSystem.init();
            console.log('[initializeAppRouting] Sistema de persistência inicializado.');
        } else {
            console.warn('[initializeAppRouting] Sistema de persistência não encontrado após aguardar.');
        }

        // Aguarda um pouco para garantir que o DOM esteja completamente carregado
        await new Promise(resolve => setTimeout(resolve, 100));

        // Incrementa contador de uso na entrada do programa
        await incrementUsageAndSave();

        // Verifica se existem dados salvos e mostra modal se necessário
        console.log('[initializeAppRouting] ===== VERIFICAÇÃO INICIAL DE DADOS =====');
        const hasExistingData = await checkForExistingData();
        console.log('[initializeAppRouting] hasExistingData resultado:', hasExistingData);

        if (hasExistingData) {
            console.log('📋 [FLUXO] Dados existentes encontrados. Mostrando modal de escolha...');
            // Aguarda mais um pouco para garantir que o Bootstrap esteja carregado
            await new Promise(resolve => setTimeout(resolve, 200));
            const userChoice = await showStartupModal();
            console.log(`🎯 [FLUXO] Usuário escolheu: ${userChoice}`);

            if (userChoice === 'new') {
                console.log('🧹 [FLUXO] Usuário escolheu NOVO PROJETO - dados devem estar limpos');
            } else {
                console.log('📂 [FLUXO] Usuário escolheu CONTINUAR PROJETO - mantendo dados existentes');
            }
        } else {
            console.log('✨ [FLUXO] Nenhum dado existente encontrado, prosseguindo normalmente.');
        }

        const initialHash = window.location.hash.substring(1);
        let moduleToLoadOnStart = 'transformer_inputs';

        if (initialHash) {
            const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === initialHash);
            if (isValidModule) {
                moduleToLoadOnStart = initialHash;
                loadModulePage(moduleToLoadOnStart, false);
            } else {
                console.warn(`Módulo inválido no hash da URL inicial: '${initialHash}'. Carregando módulo padrão.`);
                loadModulePage(moduleToLoadOnStart, true);
            }
        } else {
            loadModulePage(moduleToLoadOnStart, true);
        }
    }

    // Inicializar o roteamento da aplicação
    await initializeAppRouting();

    // ===== FUNÇÕES GLOBAIS PARA DEBUG =====
    // Disponibiliza globalmente para debug (dentro do escopo onde loadModulePage existe)
    window.loadModulePage = loadModulePage;
});

// Função global para limpar cache de módulos (funciona no console do navegador)
window.clearModuleCache = function(moduleNames) {
    if (typeof moduleNames === 'string') {
        moduleNames = [moduleNames];
    }

    console.log(`[clearModuleCache] Iniciando limpeza para: ${moduleNames.join(', ')}`);

    moduleNames.forEach(moduleName => {
        // Limpa cache do localStorage
        const keys = Object.keys(localStorage);
        let removedCount = 0;

        keys.forEach(key => {
            if (key.includes(moduleName)) {
                localStorage.removeItem(key);
                console.log(`[clearModuleCache] Removido do localStorage: ${key}`);
                removedCount++;
            }
        });

        // Dispara evento de limpeza
        const event = new CustomEvent('clearModuleData', { detail: { module: moduleName } });
        document.dispatchEvent(event);

        console.log(`[clearModuleCache] Módulo '${moduleName}': ${removedCount} itens removidos`);
    });

    const result = `✅ Cache limpo para: ${moduleNames.join(', ')}`;
    console.log(`[clearModuleCache] ${result}`);
    return result;
};

// Função para limpar todos os caches
window.clearAllCache = function() {
    const allModules = ['history', 'standards', 'transformerInputs', 'losses', 'impulse',
                       'appliedVoltage', 'inducedVoltage', 'shortCircuit', 'temperatureRise', 'dielectricAnalysis'];
    return window.clearModuleCache(allModules);
};

// Função para mostrar ajuda
window.showCacheHelp = function() {
    console.log(`
🔧 COMANDOS DE CACHE DISPONÍVEIS (execute no console do navegador):

1. Limpar módulo específico:
   clearModuleCache('history')
   clearModuleCache('standards')

2. Limpar múltiplos módulos:
   clearModuleCache(['history', 'standards'])

3. Limpar todos os caches:
   clearAllCache()

4. Mostrar esta ajuda:
   showCacheHelp()

⚠️ IMPORTANTE: Execute estes comandos no CONSOLE DO NAVEGADOR (F12),
   NÃO no terminal do Windows!
`);
    return "Ajuda exibida no console";
};

// ===== LIMPEZA AUTOMÁTICA DE CACHE =====

// Limpa automaticamente cache antigo na inicialização
function autoCleanCache() {
    const cacheVersion = '1.0.0';
    const lastVersion = localStorage.getItem('tts_cache_version');

    if (lastVersion !== cacheVersion) {
        console.log('[autoCleanCache] Nova versão detectada, limpando cache automaticamente...');

        // Limpa todos os caches automaticamente
        const keys = Object.keys(localStorage);
        let removedCount = 0;

        keys.forEach(key => {
            if (key.startsWith('store_') || key.includes('cache') || key.includes('session')) {
                localStorage.removeItem(key);
                removedCount++;
            }
        });

        // Atualiza versão do cache
        localStorage.setItem('tts_cache_version', cacheVersion);

        console.log(`[autoCleanCache] ✅ ${removedCount} itens de cache removidos automaticamente`);
        console.log('[autoCleanCache] Cache limpo e atualizado para nova versão');
    } else {
        console.log('[autoCleanCache] Cache já está atualizado');
    }
}

// Executa limpeza automática na inicialização
// autoCleanCache(); // Temporariamente desabilitado para debug

console.log("🔧 Sistema de cache automático ativo. Cache será limpo automaticamente quando necessário.");
document.addEventListener('DOMContentLoaded', function() {
    const currentYearSpan = document.getElementById('current-year');
    if (currentYearSpan) {
        currentYearSpan.textContent = new Date().getFullYear();
    }
});

// ===== SISTEMA DE GERAÇÃO DE RELATÓRIOS =====

function setupReportButton() {
    const reportBtn = document.getElementById('generate-report-btn');
    if (reportBtn) {
        reportBtn.addEventListener('click', showReportModal);
        console.log('[main.js] Botão de relatório configurado');
    }
}

async function showReportModal() {
    const modal = new bootstrap.Modal(document.getElementById('reportModulesModal'));

    // Configurar botões do modal
    setupModalButtons();

    // Verificar status dos módulos
    await checkModulesStatus();

    modal.show();
}

async function checkModulesStatus() {
    const modules = [
        { id: 'transformerInputs', name: 'Dados Básicos' },
        { id: 'losses', name: 'Perdas' },
        { id: 'appliedVoltage', name: 'Tensão Aplicada' },
        { id: 'inducedVoltage', name: 'Tensão Induzida' },
        { id: 'temperatureRise', name: 'Elevação de Temperatura' },
        { id: 'shortCircuit', name: 'Curto-Circuito' }
    ];

    for (const module of modules) {
        const checkbox = document.getElementById(`module-${module.id}`);
        const label = checkbox?.parentElement?.querySelector('label');

        if (checkbox && label) {
            try {
                // Verificar se o módulo tem dados
                const store = window.apiDataSystem?.getStore(module.id);
                const data = store ? await store.getData() : null;

                let hasData = false;
                let statusIcon = '';
                let statusClass = '';

                if (data && typeof data === 'object') {
                    // Verificar se há dados úteis
                    const hasFormData = data.formData && Object.keys(data.formData).length > 0;
                    const hasResults = data.results && Object.keys(data.results).length > 0;
                    const hasInputs = data.inputs && Object.keys(data.inputs).length > 0;
                    const hasBasicData = data.basicData && Object.keys(data.basicData).length > 0;

                    hasData = hasFormData || hasResults || hasInputs || hasBasicData || Object.keys(data).length > 2;
                }

                if (hasData) {
                    statusIcon = '<i class="fas fa-check-circle text-success me-1"></i>';
                    statusClass = 'text-success';
                } else {
                    statusIcon = '<i class="fas fa-exclamation-triangle text-warning me-1"></i>';
                    statusClass = 'text-muted';
                    checkbox.checked = false; // Desmarcar módulos sem dados
                }

                // Atualizar o label com o status
                const originalHtml = label.innerHTML;
                const iconRegex = /<i class="fas fa-(check-circle|exclamation-triangle)[^>]*><\/i>/;

                if (iconRegex.test(originalHtml)) {
                    label.innerHTML = originalHtml.replace(iconRegex, statusIcon);
                } else {
                    // Adicionar ícone após o ícone do módulo
                    const moduleIconMatch = originalHtml.match(/(<i class="fas fa-[^>]*><\/i>)/);
                    if (moduleIconMatch) {
                        label.innerHTML = originalHtml.replace(moduleIconMatch[1], moduleIconMatch[1] + statusIcon);
                    } else {
                        label.innerHTML = statusIcon + originalHtml;
                    }
                }

                // Adicionar classe de status
                label.className = label.className.replace(/text-(success|warning|muted)/, '') + ` ${statusClass}`;

            } catch (error) {
                console.error(`[checkModulesStatus] Erro ao verificar módulo ${module.id}:`, error);
                // Em caso de erro, desmarcar o módulo
                checkbox.checked = false;
            }
        }
    }
}

function setupModalButtons() {
    // Botão selecionar todos
    const selectAllBtn = document.getElementById('selectAllModules');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#reportModulesModal input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
        });
    }

    // Botão desmarcar todos
    const deselectAllBtn = document.getElementById('deselectAllModules');
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#reportModulesModal input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        });
    }

    // Botão gerar relatório
    const generateBtn = document.getElementById('generateReportBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateReport);
    }

    // Botão download Excel
    const downloadExcelBtn = document.getElementById('downloadExcelBtn');
    if (downloadExcelBtn) {
        downloadExcelBtn.addEventListener('click', downloadExcelData);
    }
}

async function generateReport() {
    const generateBtn = document.getElementById('generateReportBtn');
    const spinner = document.getElementById('reportSpinner');

    try {
        // Mostrar loading
        generateBtn.disabled = true;
        spinner.classList.remove('d-none');

        // Coletar módulos selecionados
        const selectedModules = [];
        const checkboxes = document.querySelectorAll('#reportModulesModal input[type="checkbox"]:checked');

        checkboxes.forEach(cb => {
            selectedModules.push(cb.value);
        });

        if (selectedModules.length === 0) {
            alert('Por favor, selecione pelo menos um módulo para incluir no relatório.');
            return;
        }

        console.log('[generateReport] Módulos selecionados:', selectedModules);

        // Fazer requisição para o backend
        const response = await fetch('/api/transformer/generate-report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                selected_modules: selectedModules
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `Erro HTTP ${response.status}`);
        }

        // Baixar o PDF
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Criar nome do arquivo baseado no título do relatório
        const reportTitle = document.getElementById('reportTitle').value.trim() || 'Relatório TTS';
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');

        // Limpar o título para usar como nome de arquivo
        const cleanTitle = reportTitle
            .replace(/[^a-zA-Z0-9\s\-_]/g, '') // Remove caracteres especiais
            .replace(/\s+/g, '_') // Substitui espaços por underscore
            .substring(0, 50); // Limita o tamanho

        let filename = `${cleanTitle}_${timestamp}.pdf`;

        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('[generateReport] Relatório gerado e baixado com sucesso');

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('reportModulesModal'));
        modal.hide();

        // Mostrar mensagem de sucesso
        showSuccessMessage('Relatório PDF gerado com sucesso!');

    } catch (error) {
        console.error('[generateReport] Erro ao gerar relatório:', error);
        alert(`Erro ao gerar relatório: ${error.message}`);
    } finally {
        // Esconder loading
        generateBtn.disabled = false;
        spinner.classList.add('d-none');
    }
}

async function downloadExcelData() {
    const downloadBtn = document.getElementById('downloadExcelBtn');
    const spinner = document.getElementById('excelSpinner');

    try {
        // Mostrar loading
        downloadBtn.disabled = true;
        spinner.classList.remove('d-none');

        // Coletar módulos selecionados
        const selectedModules = [];
        const checkboxes = document.querySelectorAll('#reportModulesModal input[type="checkbox"]:checked');

        checkboxes.forEach(cb => {
            selectedModules.push(cb.value);
        });

        if (selectedModules.length === 0) {
            alert('Por favor, selecione pelo menos um módulo para incluir no download.');
            return;
        }

        console.log('[downloadExcelData] Módulos selecionados:', selectedModules);

        // Fazer requisição para o backend
        const response = await fetch('/api/transformer/download-excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                selected_modules: selectedModules
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `Erro HTTP ${response.status}`);
        }

        // Baixar o Excel
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Criar nome do arquivo baseado no título do relatório
        const reportTitle = document.getElementById('reportTitle').value.trim() || 'Dados TTS';
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');

        // Limpar o título para usar como nome de arquivo
        const cleanTitle = reportTitle
            .replace(/[^a-zA-Z0-9\s\-_]/g, '') // Remove caracteres especiais
            .replace(/\s+/g, '_') // Substitui espaços por underscore
            .substring(0, 50); // Limita o tamanho

        let filename = `${cleanTitle}_${timestamp}.xlsx`;

        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('[downloadExcelData] Dados Excel baixados com sucesso');

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('reportModulesModal'));
        modal.hide();

        // Mostrar mensagem de sucesso
        showSuccessMessage('Dados Excel baixados com sucesso!');

    } catch (error) {
        console.error('[downloadExcelData] Erro ao baixar dados Excel:', error);
        alert(`Erro ao baixar dados Excel: ${error.message}`);
    } finally {
        // Esconder loading
        downloadBtn.disabled = false;
        spinner.classList.add('d-none');
    }
}

function showSuccessMessage(message) {
    // Criar toast de sucesso
    const toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';

    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    document.body.appendChild(toastContainer);

    const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
    bsToast.show();

    // Remover após esconder
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toastContainer);
    });
}

// Configurar botão de relatório quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    setupReportButton();
});