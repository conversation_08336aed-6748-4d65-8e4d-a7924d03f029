import os
import re
from collections import defaultdict

def find_duplicate_lines(filepaths, min_lines=3):
    """
    Encontra blocos de linhas duplicadas em uma lista de arquivos.
    Retorna um dicionário onde a chave é o conteúdo duplicado e o valor é uma lista de (filepath, start_line, end_line).
    """
    all_lines_with_meta = [] # [(line_content, filepath, line_num)]
    
    for filepath in filepaths:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f, 1):
                    all_lines_with_meta.append((line.strip(), filepath, i)) # strip para ignorar espaços em branco no início/fim
        except Exception as e:
            print(f"Erro ao ler o arquivo {filepath}: {e}")
            continue

    # Mapeia cada bloco de linhas para suas ocorrências
    block_occurrences = defaultdict(list)

    # Itera sobre todas as linhas para formar blocos e verificar duplicidades
    for i in range(len(all_lines_with_meta) - min_lines + 1):
        current_block_lines = []
        current_block_meta = []
        
        for j in range(min_lines):
            line_content, filepath, line_num = all_lines_with_meta[i+j]
            current_block_lines.append(line_content)
            current_block_meta.append((filepath, line_num))
        
        block_key = tuple(current_block_lines) # Tupla para ser hashable
        
        # Adiciona a ocorrência do bloco
        block_occurrences[block_key].append(current_block_meta)
    
    duplicates = {}
    for block_key, occurrences in block_occurrences.items():
        if len(occurrences) > 1:
            # Formata as ocorrências para o relatório
            formatted_occurrences = []
            for occ in occurrences:
                # Corrigido: occ é uma lista de (filepath, line_num)
                filepath = occ[0][0] # Pega o filepath do primeiro item do bloco
                start_line = occ[0][1] # Pega o número da primeira linha do bloco
                end_line = occ[-1][1] # Pega o número da última linha do bloco
                formatted_occurrences.append((filepath, start_line, end_line))
            
            # Adiciona apenas se o bloco não for vazio (ex: várias linhas em branco)
            if any(line.strip() for line in block_key):
                duplicates["\n".join(block_key)] = formatted_occurrences
                
    return duplicates

def find_duplicate_styles_in_html(filepaths):
    """
    Encontra blocos <style> duplicados em arquivos HTML.
    """
    duplicate_styles = defaultdict(list)
    
    for filepath in filepaths:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                # Regex para encontrar blocos <style>
                style_blocks = re.findall(r'<style>(.*?)</style>', content, re.DOTALL)
                for block in style_blocks:
                    # Normaliza o bloco (remove espaços em branco extras, quebras de linha)
                    normalized_block = re.sub(r'\s+', ' ', block).strip()
                    if normalized_block: # Ignora blocos vazios
                        duplicate_styles[normalized_block].append(filepath)
        except Exception as e:
            print(f"Erro ao ler o arquivo {filepath}: {e}")
            continue
            
    report = {}
    for style_content, occurrences in duplicate_styles.items():
        if len(occurrences) > 1:
            report[style_content] = occurrences
    return report

def find_duplicate_assets_references(filepaths, asset_dir="public/assets"):
    """
    Verifica se referências a assets (CSS, JS, imagens) estão sendo feitas corretamente.
    Apenas um check básico para ver se o caminho 'assets' está presente.
    """
    incorrect_references = defaultdict(list)
    
    for filepath in filepaths:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                # Regex para encontrar src, href, url que não começam com 'assets/' ou 'public/assets/'
                # Corrigido o regex para aspas literais e o grupo de captura
                css_urls = re.findall(r'url\([\'"]?(?!/?(?:public/)?assets/)([^)\'"]*?)[\'"]?\)', content)
                for url in css_urls:
                    if not url.startswith('data:') and not url.startswith('#'): # Ignora data URIs e âncoras
                        incorrect_references[f"Referência de asset incorreta em CSS: {url}"].append(filepath)
                
                # Para HTML: src="..." ou href="..."
                html_refs = re.findall(r'(?:src|href)=[\'"](?!/?(?:public/)?assets/)([^"\']*?)[\'"]', content)
                for ref in html_refs:
                    if not ref.startswith('http') and not ref.startswith('//') and not ref.startswith('#'): # Ignora URLs externas e âncoras
                        incorrect_references[f"Referência de asset incorreta em HTML: {ref}"].append(filepath)

        except Exception as e: # Adicionado bloco except
            print(f"Erro ao ler o arquivo {filepath}: {e}")
            continue
            
    return incorrect_references

def check_constants_usage(filepaths, constants_file="backend/utils/constants.py"):
    """
    Verifica se constantes estão sendo usadas de constants.py e não duplicadas.
    Isso é um check heurístico, não uma análise de AST completa.
    """
    constants_content = ""
    try:
        with open(constants_file, 'r', encoding='utf-8') as f:
            constants_content = f.read()
    except FileNotFoundError:
        print(f"Arquivo de constantes não encontrado: {constants_file}")
        return {"Erro": [f"Arquivo de constantes não encontrado: {constants_file}"]}

    # Extrai nomes de constantes do arquivo constants.py
    # Procura por linhas como CONST_NAME = value ou CONST_NAME = { ... }
    constant_names = re.findall(r'^\s*([A-Z_][A-Z0-9_]*)\s*=\s*(?:[^#\n]*?)(?:#.*)?$', constants_content, re.MULTILINE)
    
    # Remove constantes que são apenas comentários ou docstrings
    constant_names = [name for name in constant_names if not name.startswith('__')]

    # Remove constantes que são tabelas grandes e podem ser duplicadas por engano
    # Ex: potencia_magnet_data, perdas_nucleo_data
    constant_names_to_ignore = ['potencia_magnet_data', 'perdas_nucleo_data', 'potencia_magnet_data_H110_27', 'perdas_nucleo_data_H110_27']
    constant_names = [name for name in constant_names if name not in constant_names_to_ignore]

    duplication_report = defaultdict(list)

    for filepath in filepaths:
        if filepath == constants_file:
            continue # Não verifica o próprio arquivo de constantes
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Verifica se alguma constante definida em constants.py é definida localmente
                for const_name in constant_names:
                    # Procura por "const_name = " ou "const_name ="
                    # Evita pegar usos da constante, apenas definições
                    if re.search(r'^\s*' + re.escape(const_name) + r'\s*=\s*(?:[^#\n]*?)(?:#.*)?$', content, re.MULTILINE):
                        # Verifica se a constante está sendo importada
                        if not re.search(r'from\s+backend\.utils\.constants\s+import\s+(?:.*,\s*)?' + re.escape(const_name) + r'(?:,\s*.*)?', content) and \
                           not re.search(r'from\s+backend\.utils\.constants\s+import\s+\*', content) and \
                           not re.search(r'import\s+backend\.utils\.constants', content):
                            duplication_report[f"Constante '{const_name}' duplicada ou redefinida localmente"].append(filepath)
                        
        except Exception as e:
            print(f"Erro ao ler o arquivo {filepath}: {e}")
            continue
            
    return duplication_report


def main():
    base_dir = os.path.dirname(os.path.abspath(__file__)) # TTS/
    
    # Listas de arquivos para verificação
    python_files = [os.path.join(root, f) for root, _, files in os.walk(os.path.join(base_dir, "backend")) for f in files if f.endswith('.py')]
    js_files = [os.path.join(root, f) for root, _, files in os.walk(os.path.join(base_dir, "public", "scripts")) for f in files if f.endswith('.js')]
    html_files = [os.path.join(root, f) for root, _, files in os.walk(os.path.join(base_dir, "public", "pages")) for f in files if f.endswith('.html')]
    
    all_code_files = python_files + js_files + html_files
    all_frontend_files = js_files + html_files
    
    print("--- Verificando Duplicidade de Linhas (blocos de 3+ linhas) ---")
    line_duplicates = find_duplicate_lines(all_code_files, min_lines=3)
    if line_duplicates:
        for block, locations in line_duplicates.items():
            print(f"\nBloco duplicado:\n---\n{block}\n---")
            for loc in locations:
                print(f"  Ocorrência em: {loc[0]}:{loc[1]}-{loc[2]}")
    else:
        print("Nenhuma duplicação de blocos de linhas encontrada.")

    print("\n--- Verificando Estilos CSS Embutidos em HTML ---")
    embedded_styles = find_duplicate_styles_in_html(html_files)
    if embedded_styles:
        for style_content, files in embedded_styles.items():
            print(f"\nEstilo <style> embutido encontrado:\n---\n{style_content}\n---")
            print(f"  Arquivos afetados: {', '.join(files)}")
    else:
        print("Nenhum estilo <style> embutido encontrado.")

    print("\n--- Verificando Referências de Assets (CSS/HTML) ---")
    incorrect_asset_refs = find_duplicate_assets_references(all_frontend_files)
    if incorrect_asset_refs:
        for error_msg, files in incorrect_asset_refs.items():
            print(f"\n{error_msg}")
            print(f"  Arquivos afetados: {', '.join(files)}")
    else:
        print("Todas as referências de assets parecem corretas.")

    print("\n--- Verificando Duplicação de Constantes Python ---")
    constants_duplication = check_constants_usage(python_files, os.path.join(base_dir, "backend", "utils", "constants.py"))
    if constants_duplication:
        for error_msg, files in constants_duplication.items():
            print(f"\n{error_msg}")
            print(f"  Arquivos afetados: {', '.join(files)}")
    else:
        print("Nenhuma duplicação de constantes Python encontrada (além das tabelas de dados).")

if __name__ == "__main__":
    main()