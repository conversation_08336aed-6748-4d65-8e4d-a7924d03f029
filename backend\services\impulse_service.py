"""
Serviço para cálculos de ensaios de impulso
Implementa os algoritmos descritos em docs/instrucoes_impulso.md
"""

import sys
import pathlib
from typing import Dict, Any, Optional, Union, List, Tuple
import math
import logging
import numpy as np
from ..utils.helpers import setup_path_for_imports

setup_path_for_imports()

# Tenta importar constantes para o serviço
from ..utils import constants as const


def calculate_impulse_waveform_parameters(resistor_frontal: float, resistor_cauda: float, 
                                         capacitancia_gerador: float, capacitancia_objeto: float) -> Dict[str, Any]:
    """
    Calcula os parâmetros da forma de onda de impulso conforme IEC 60060-1.
    
    Args:
        resistor_frontal: Valor do resistor frontal em Ohms
        resistor_cauda: Valor do resistor de cauda em Ohms
        capacitancia_gerador: Capacitância do gerador em nF
        capacitancia_objeto: Capacitância do objeto de teste em pF
        
    Returns:
        Dicionário com os parâmetros calculados (alfa, beta, tempo de frente, tempo de cauda, eficiência)
    """
    # Converte capacitância do objeto para nF para cálculos consistentes
    c_objeto_nf = capacitancia_objeto / 1000.0
    
    # Capacitância total do circuito (gerador + objeto)
    c_total = capacitancia_gerador + c_objeto_nf
      # Cálculo correto de alfa e beta para circuito RLC duplo-exponencial
    # Para um gerador de impulso padrão conforme IEC 60060-1:
    # alfa ≈ 1/(R_tail * C_total) - relacionado ao tempo de cauda 
    # beta ≈ 1/(R_front * C_gen) - relacionado ao tempo de frente
    
    # Fórmulas corrigidas para obter valores típicos de lightning impulse (1.2/50 μs)
    alfa = 1 / (resistor_cauda * c_total * 1e-6)  # 1/μs
    beta = 1 / (resistor_frontal * capacitancia_gerador * 1e-6)  # 1/μs
    
    # Garante que beta > alfa para forma de onda válida
    if beta <= alfa:
        # Ajusta beta para garantir forma de onda correta
        beta = alfa * 10  # Fator empírico para lightning impulse
        logging.warning(f"Beta ajustado para {beta:.6f} para garantir forma de onda válida")
    
    # Cálculo dos tempos de frente e cauda
    tempo_frente, tempo_cauda = calculate_front_tail_times(alfa, beta)
    
    # Cálculo da eficiência
    eficiencia = calculate_efficiency(alfa, beta)
    
    return {
        "alfa": alfa,
        "beta": beta,
        "tempo_frente": tempo_frente,
        "tempo_cauda": tempo_cauda,
        "eficiencia": eficiencia,
        "dentro_tolerancia_frente": is_within_tolerance(tempo_frente, 
                                                      const.LIGHTNING_IMPULSE_FRONT_TIME_NOM, 
                                                      const.LIGHTNING_FRONT_TOLERANCE),
        "dentro_tolerancia_cauda": is_within_tolerance(tempo_cauda,
                                                     const.LIGHTNING_IMPULSE_TAIL_TIME_NOM,
                                                     const.LIGHTNING_TAIL_TOLERANCE)
    }


def impulse_waveform(t: float, alfa: float, beta: float) -> float:
    """
    Calcula o valor da tensão na forma de onda de impulso para um dado tempo.
    Implementa a equação duplo-exponencial padrão V(t) = K*(e^(-αt) - e^(-βt))
    
    Args:
        t: Tempo em μs
        alfa: Parâmetro alfa da equação de impulso (relacionado à cauda)
        beta: Parâmetro beta da equação de impulso (relacionado à frente)
        
    Returns:
        Valor relativo da tensão (0-1) para o tempo t
    """
    if t < 0:
        return 0.0
    
    # Fator de normalização para que o pico seja 1.0
    if beta <= alfa:
        return 0.0
    
    # Tempo do pico para normalização
    t_peak = np.log(beta / alfa) / (beta - alfa)
    k_normalization = 1.0 / (np.exp(-alfa * t_peak) - np.exp(-beta * t_peak))
    
    # Equação duplo-exponencial normalizada
    return k_normalization * (np.exp(-alfa * t) - np.exp(-beta * t))


def switching_impulse_waveform(t: float, T_p: float, T_2: float) -> float:
    """
    Calcula o valor da tensão na forma de onda de impulso de manobra para um dado tempo.
    Implementa a função específica para switching impulse conforme IEC 60060-1.
    
    Args:
        t: Tempo em μs
        T_p: Tempo para o pico em μs (tipicamente 250 μs)
        T_2: Tempo de meia onda em μs (tipicamente 2500 μs)
        
    Returns:
        Valor relativo da tensão (0-1) para o tempo t
    """
    if t < 0:
        return 0.0
    
    # Parâmetros simplificados para switching impulse
    # Usar aproximação com função híbrida exponencial-trigonométrica
    k1 = 4.0 / T_p  # Constante de subida
    k2 = 0.7 / T_2  # Constante de descida
    
    # Função aproximada para switching impulse
    if t <= T_p:
        # Fase de subida (aproximação)
        return (t / T_p) * np.exp(-k1 * (t - T_p) / T_p)
    else:
        # Fase de descida
        return np.exp(-k2 * (t - T_p))


def calculate_front_tail_times(alfa: float, beta: float) -> Tuple[float, float]:
    """
    Calcula os tempos de frente (T1) e cauda (T2) da forma de onda de impulso 
    usando aproximações válidas para circuitos típicos.
    
    Args:
        alfa: Parâmetro alfa da equação de impulso
        beta: Parâmetro beta da equação de impulso

    Returns:
        Tupla (tempo_frente, tempo_cauda) em μs
    """
    if beta <= alfa + const.EPSILON:
        logging.warning("Beta deve ser maior que alfa para forma de onda válida")
        return 0.0, 0.0
    
    # Fórmulas aproximadas válidas para lightning impulse
    # T1 ≈ 1.67 / beta (tempo virtual de frente)
    # T2 ≈ 0.7 / alfa (tempo de meia onda na cauda)
    
    tempo_frente = 1.67 / beta if beta > const.EPSILON else 0.0
    tempo_cauda = 0.7 / alfa if alfa > const.EPSILON else 0.0

    return tempo_frente, tempo_cauda


def calculate_efficiency(alfa: float, beta: float) -> float:
    """
    Calcula a eficiência do gerador de impulso.
    
    Args:
        alfa: Parâmetro alfa da equação de impulso
        beta: Parâmetro beta da equação de impulso
        
    Returns:
        Eficiência do gerador (0-1)
    """
    # Tempo para o pico
    t_pico = np.log(beta / alfa) / (beta - alfa)
    
    # Valor máximo da forma de onda (eficiência)
    eficiencia = impulse_waveform(t_pico, alfa, beta)
    
    return eficiencia


def is_within_tolerance(valor: float, nominal: float, tolerancia: float) -> bool:
    """
    Verifica se um valor está dentro da tolerância especificada.
    
    Args:
        valor: Valor medido
        nominal: Valor nominal
        tolerancia: Tolerância em fração (ex: 0.3 para 30%)
        
    Returns:
        True se estiver dentro da tolerância, False caso contrário
    """
    limite_inferior = nominal * (1 - tolerancia)
    limite_superior = nominal * (1 + tolerancia)
    return limite_inferior <= valor <= limite_superior


def calculate_impulse_test(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula os parâmetros do teste de impulso com base nos dados do transformador.
    
    Args:
        data: Dicionário com os parâmetros de entrada
        
    Returns:
        Dicionário com os resultados calculados para o teste de impulso
    """
    # Extrai parâmetros básicos
    tipo_transformador = data.get("tipo_transformador", "Trifásico")
    tensao_at = data.get("tensao_at", 0)
    classe_tensao_at = data.get("classe_tensao_at", 0)
    bil_especificado = data.get("nbi_at", 0)  # Nível Básico de Isolamento especificado para AT
    norma_isolamento = data.get("norma_isolamento", "NBR/IEC") # Norma de isolamento (NBR/IEC ou IEEE)
    tipo_impulso = data.get("tipo_impulso", "Atmosférico") # Tipo de impulso (Atmosférico, Manobra, Cortado)

    # Parâmetros do circuito de impulso
    resistor_frontal = data.get("resistor_frontal", 500)  # Ohms
    resistor_cauda = data.get("resistor_cauda", 2000)  # Ohms
    capacitancia_gerador = data.get("capacitancia_gerador", 1.0)  # nF
    capacitancia_objeto = data.get("capacitancia_objeto", 1000.0)  # pF
    indutancia = data.get("indutancia", 5.0)  # μH
    tempo_corte_input = data.get("tempo_corte", None)  # μs para impulso cortado (pode ser None)
    gap_distance_mm = data.get("gap_distance_mm", None) # Distância do gap em mm (para calcular tempo de corte)


    # 1.1. Seleção do BIL/SIL com base na norma e tensão
    bil_norma = None
    sil_norma = None
    tensao_max_sistema = classe_tensao_at # Usando classe de tensão como proxy para tensão máxima do sistema

    if tipo_impulso == "Atmosférico":
        if norma_isolamento == "NBR/IEC":
            tabela_bil = const.BIL_NBR_IEC
        elif norma_isolamento == "IEEE":
            tabela_bil = const.BIL_IEEE
        else:
            tabela_bil = {} # Norma desconhecida

        # Encontra o BIL na tabela
        tensoes_tabela = sorted(tabela_bil.keys())
        bil_norma_valores = []
        for tensao_tabela in tensoes_tabela:
            if tensao_max_sistema <= tensao_tabela:
                bil_norma_valores = tabela_bil.get(tensao_tabela, [])
                break
        # Seleciona o maior valor se houver múltiplos
        bil_norma = max(bil_norma_valores) if bil_norma_valores else None

    elif tipo_impulso == "Manobra":
         if norma_isolamento == "NBR/IEC":
            tabela_sil = const.SIL_NBR_IEC
         elif norma_isolamento == "IEEE":
            tabela_sil = const.SIL_IEEE
         else:
            tabela_sil = {} # Norma desconhecida

         # Encontra o SIL na tabela
         tensoes_tabela = sorted(tabela_sil.keys())
         sil_norma_valores = []
         for tensao_tabela in tensoes_tabela:
             if tensao_max_sistema <= tensao_tabela:
                 sil_norma_valores = tabela_sil.get(tensao_tabela, [])
                 break
         # Seleciona o maior valor se houver múltiplos
         sil_norma = max(sil_norma_valores) if sil_norma_valores else None


    # Cálculo dos parâmetros da forma de onda (para impulso atmosférico ou manobra)
    waveform_params = calculate_impulse_waveform_parameters(
        resistor_frontal, resistor_cauda, capacitancia_gerador, capacitancia_objeto
    )

    # Cálculo da tensão de carga e energia
    tensao_pico_desejada = bil_especificado if tipo_impulso == "Atmosférico" else sil_norma # Usar BIL especificado para Atmosférico, SIL da norma para Manobra
    if tensao_pico_desejada is None or waveform_params["eficiencia"] <= const.EPSILON:
        tensao_carregamento = 0
    else:
        tensao_carregamento = tensao_pico_desejada / waveform_params["eficiencia"]

    # Energia do impulso (em Joules) - Seção 3.4
    energia_impulso_joules = 0.5 * (capacitancia_gerador * 1e-9) * (tensao_carregamento * 1000)**2 # C em Farads, V em Volts


    # 4. Impulso Cortado (LIC)
    tempo_corte_us = tempo_corte_input
    if tempo_corte_us is None and gap_distance_mm is not None:
        # Calcular tempo de corte baseado na distância do gap (simplificação)
        # V_ruptura = 30 * distancia_gap_cm * 1000 (em Volts)
        # Precisamos encontrar o tempo 't' onde V(t) = V_ruptura
        # V_ruptura_kv = 30 * (gap_distance_mm / 10) # kV
        # V_ruptura_relativa = V_ruptura_kv / tensao_pico_desejada if tensao_pico_desejada > const.EPSILON else 0
        # def func_tempo_corte(t):
        #     return impulse_waveform(t, waveform_params["alfa"], waveform_params["beta"]) - V_ruptura_relativa
        # try:
        #     tempo_corte_us = fsolve(func_tempo_corte, 3.0)[0] # Estimar tempo de corte em 3 μs
        # except Exception as e:        #     tempo_corte_us = fsolve(func_tempo_corte, 3.0)[0] # Estimar tempo de corte em 3 μs
        # except Exception as e:
        #     log.warning(f"Não foi possível calcular tempo de corte baseado no gap: {e}")
        #     tempo_corte_us = None # Não foi possível calcular

        # Simplificação: usar um valor padrão se gap_distance_mm for fornecido mas tempo_corte_input não
        tempo_corte_us = 3.0 # Valor padrão se gap_distance_mm for fornecido mas tempo_corte_input não

    # Inicializar variáveis de impulso cortado
    tensao_corte_kv = 0.0
    sobretensao_corte_kv = 0.0
    fator_sobretensao_corte = 0.2  # Fator padrão de sobretensão (20%)
    
    if tempo_corte_us is not None and tensao_carregamento > 0:
        # V_corte = V₀ * (e^(-α*t_corte) - e^(-β*t_corte))
        # Onde V₀ é a tensão de carregamento (V_carga)
        tensao_corte_kv = tensao_carregamento * impulse_waveform(tempo_corte_us, waveform_params["alfa"], waveform_params["beta"])

        # Sobretensão de corte (simplificação)
        # V_sobretensao = V_corte * (1 + k)
        # k depende da indutância e impedância. Simplificando, assumimos um fator k fixo ou calculado de forma simples.
        # A documentação menciona que a indutância afeta a sobretensão.
        # Uma simplificação comum é V_sobretensao ≈ V_corte * (1 + Z_onda / Z_circuito)
        # Onde Z_onda é a impedância de surto do enrolamento e Z_circuito é a impedância do circuito de impulso.
        # Sem esses valores, usaremos um fator de sobretensao placeholder.
        sobretensao_corte_kv = tensao_corte_kv * (1 + fator_sobretensao_corte)

    # 5. Simulação da Forma de Onda (melhorada)
    # Definir tipo de impulso para mapeamento
    tipo_impulso_map = {
        "Atmosférico": "lightning",
        "Manobra": "switching", 
        "Cortado": "chopped"
    }
    impulse_type_key = tipo_impulso_map.get(tipo_impulso, "lightning")
    
    # Preparar parâmetros para geração da forma de onda
    waveform_gen_params = waveform_params.copy()
    if tempo_corte_us is not None:
        waveform_gen_params["tempo_corte"] = tempo_corte_us
        waveform_gen_params["fator_sobretensao"] = 1 + fator_sobretensao_corte
    
    # Definir duração da simulação baseada no tipo de impulso
    if tipo_impulso == "Manobra":
        tempo_max_simulacao = 5000.0  # μs para switching impulse
        passo_tempo = 1.0  # μs
    else:
        tempo_max_simulacao = 200.0  # μs para lightning/chopped
        passo_tempo = 0.1  # μs
    
    # Gerar forma de onda usando função melhorada
    waveform_data = generate_impulse_waveform_data(
        waveform_gen_params, 
        impulse_type_key, 
        tempo_max_simulacao, 
        passo_tempo
    )
    
    # Aplicar tensão de carregamento para obter valores em kV
    tempos = np.array(waveform_data["tempos_us"])
    tensoes_pu = np.array(waveform_data["tensoes_pu"])
    tensoes = tensoes_pu * tensao_carregamento  # Converter para kV

    # 6. Análise dos Resultados e Conformidade
    analise_conformidade = {}
    if tipo_impulso == "Atmosférico":
        analise_conformidade["tipo"] = "Impulso Atmosférico (LI)"
        analise_conformidade["tempo_frente_us"] = round(waveform_params["tempo_frente"], 2)
        analise_conformidade["tempo_cauda_us"] = round(waveform_params["tempo_cauda"], 2)
        analise_conformidade["dentro_tolerancia_frente"] = waveform_params["dentro_tolerancia_frente"]
        analise_conformidade["dentro_tolerancia_cauda"] = waveform_params["dentro_tolerancia_cauda"]
        analise_conformidade["overshoot"] = "Não calculado (requer simulação detalhada)" # TODO: Calcular overshoot
        analise_conformidade["tensao_pico_kv"] = round(np.max(tensoes), 2) # Tensão de pico da simulação
        analise_conformidade["tensao_pico_especificada_kv"] = bil_especificado
        analise_conformidade["status_tensao_pico"] = "OK" # TODO: Verificar tolerância da tensão de pico

    elif tipo_impulso == "Manobra":
        analise_conformidade["tipo"] = "Impulso de Manobra (SI)"
        analise_conformidade["tempo_pico_us"] = round(tempos[np.argmax(tensoes)], 2) # Tempo para o pico da simulação
        analise_conformidade["tempo_meia_onda_us"] = "Não calculado (requer análise da cauda)" # TODO: Calcular tempo de meia onda
        analise_conformidade["tensao_pico_kv"] = round(np.max(tensoes), 2) # Tensão de pico da simulação
        analise_conformidade["tensao_pico_especificada_kv"] = sil_norma # Usar SIL da norma para Manobra
        analise_conformidade["status_tensao_pico"] = "OK" # TODO: Verificar tolerância da tensão de pico

    elif tipo_impulso == "Cortado":
        analise_conformidade["tipo"] = "Impulso Cortado (LIC)"
        analise_conformidade["tempo_corte_us"] = round(tempo_corte_us, 2) if tempo_corte_us is not None else None
        analise_conformidade["tensao_corte_kv"] = round(tensao_corte_kv, 2)
        analise_conformidade["sobretensao_corte_kv"] = round(sobretensao_corte_kv, 2)
        analise_conformidade["tensao_pico_simulacao_kv"] = round(np.max(tensoes), 2) # Tensão de pico da simulação (pode ser a sobretensão)
        analise_conformidade["status"] = "Análise de forma de onda e conformidade para LIC requer simulação detalhada." # TODO: Implementar análise detalhada para LIC


    # Prepara resultados
    results: Dict[str, Any] = {
        # Parâmetros de entrada relevantes
        "bil_especificado_kv": bil_especificado,
        "norma_isolamento": norma_isolamento,
        "tipo_impulso": tipo_impulso,
        "resistor_frontal_ohm": resistor_frontal,
        "resistor_cauda_ohm": resistor_cauda,
        "capacitancia_gerador_nf": capacitancia_gerador,
        "capacitancia_objeto_pf": capacitancia_objeto,
        "indutancia_uh": indutancia,
        "tempo_corte_input_us": tempo_corte_input,
        "gap_distance_mm": gap_distance_mm,

        # Níveis de isolamento da norma
        "bil_norma_kv": bil_norma,
        "sil_norma_kv": sil_norma,

        # Parâmetros calculados da forma de onda
        "alfa": round(waveform_params["alfa"], 4),
        "beta": round(waveform_params["beta"], 4),
        "tempo_frente_us_calc": round(waveform_params["tempo_frente"], 2),
        "tempo_cauda_us_calc": round(waveform_params["tempo_cauda"], 2),
        "eficiencia": round(waveform_params["eficiencia"], 4),
        "dentro_tolerancia_frente": waveform_params["dentro_tolerancia_frente"],
        "dentro_tolerancia_cauda": waveform_params["dentro_tolerancia_cauda"],

        # Tensões e Energia calculadas
        "tensao_carregamento_kv": round(tensao_carregamento, 2),
        "energia_impulso_joules": round(energia_impulso_joules, 2),

        # Resultados de Impulso Cortado (se aplicável)
        "tempo_corte_us_calc": round(tempo_corte_us, 2) if tempo_corte_us is not None else None,
        "tensao_corte_kv_calc": round(tensao_corte_kv, 2),
        "sobretensao_corte_kv_calc": round(sobretensao_corte_kv, 2),

        # Simulação da forma de onda (pontos de tempo e tensão)
        "simulacao_forma_onda": {
            "tempos_us": tempos.tolist(),
            "tensoes_kv": [round(v, 2) for v in tensoes],
        },

        # Análise de Conformidade
        "analise_conformidade": analise_conformidade,

        # TODO: Adicionar Recomendações para o Teste (Seção 7)
        "recomendacoes_teste": "Implementação pendente."
    }

    # Adicionar resultados da análise da forma de onda medida
    measured_times = _analyze_waveform_times_from_data(
        tempos_us=results["simulacao_forma_onda"]["tempos_us"],
        tensoes_pu=results["simulacao_forma_onda"]["tensoes_kv"], # Use tensoes_kv and find peak from it
        is_pu=False # Indicate that input is not PU, so peak needs to be found from actual values
    )
    results["analise_forma_onda_medida"] = {
        "tempo_frente_us_medido": round(measured_times["T1_measured"], 2) if measured_times["T1_measured"] is not None else None,
        "tempo_cauda_us_medido": round(measured_times["T2_measured"], 2) if measured_times["T2_measured"] is not None else None,
    }

    # Verificar tolerâncias para tempos medidos (apenas para impulso atmosférico por enquanto)
    if tipo_impulso == "Atmosférico" and measured_times["T1_measured"] is not None and measured_times["T2_measured"] is not None:
        results["analise_forma_onda_medida"]["dentro_tolerancia_frente_medido"] = is_within_tolerance(
            measured_times["T1_measured"],
            const.LIGHTNING_IMPULSE_FRONT_TIME_NOM,
            const.LIGHTNING_FRONT_TOLERANCE
        )
        results["analise_forma_onda_medida"]["dentro_tolerancia_cauda_medido"] = is_within_tolerance(
            measured_times["T2_measured"],
            const.LIGHTNING_IMPULSE_TAIL_TIME_NOM,
            const.LIGHTNING_TAIL_TOLERANCE
        )
    else:
        results["analise_forma_onda_medida"]["dentro_tolerancia_frente_medido"] = None
        results["analise_forma_onda_medida"]["dentro_tolerancia_cauda_medido"] = None
        
    return results


def _analyze_waveform_times_from_data(tempos_us: List[float], tensoes_pu: List[float], is_pu: bool = True) -> Dict[str, Optional[float]]:
    """
    Analisa a forma de onda fornecida (tempos e tensões) para extrair T1 e T2 medidos.
    Assume que a forma de onda já está normalizada se is_pu=True, caso contrário, normaliza internamente.
    Args:
        tempos_us: Lista de tempos em μs.
        tensoes_pu: Lista de tensões (normalizadas para PU se is_pu=True, ou valores reais se is_pu=False).
        is_pu: Indica se tensoes_pu já está em por unidade (PU).
    Returns:
        Dicionário com "T1_measured" e "T2_measured" em μs.
    """
    if not tempos_us or not tensoes_pu or len(tempos_us) != len(tensoes_pu) or len(tempos_us) < 2:
        logging.warning("Dados de forma de onda insuficientes para análise.")
        return {"T1_measured": None, "T2_measured": None}

    tempos_np = np.array(tempos_us)
    tensoes_np = np.array(tensoes_pu)

    if not is_pu:
        v_abs_max = np.max(np.abs(tensoes_np))
        if v_abs_max < const.EPSILON:
            logging.warning("Pico de tensão muito baixo para normalizar e analisar.")
            return {"T1_measured": None, "T2_measured": None}
        # Se o pico for negativo (raro, mas possível com oscilações), usar o valor absoluto para normalização
        # mas manter o sinal original para a forma de onda. A análise de tempos é geralmente em magnitude.
        # Para simplificar, assumimos impulso positivo. Se for negativo, esta análise pode não ser ideal.
        # A forma de onda de impulso padrão é positiva.
        v_peak_actual = np.max(tensoes_np) 
        if v_peak_actual < const.EPSILON: # Se o max for zero ou negativo, tentar com o min se for um impulso negativo
             v_peak_actual = np.min(tensoes_np)
             if v_peak_actual > -const.EPSILON: # Ainda perto de zero
                 logging.warning("Pico de tensão muito baixo para análise após tentativa com mínimo.")
                 return {"T1_measured": None, "T2_measured": None}
             tensoes_norm = tensoes_np / v_peak_actual # Normaliza, resultando em pico de 1 (ou -1 se negativo)
             # Para análise de tempos, trabalhamos com magnitude, então se for impulso negativo, invertemos para positivo.
             if v_peak_actual < 0:
                 tensoes_norm = -tensoes_norm 
                 v_peak_actual = -v_peak_actual # Pico agora é positivo
        else:
            tensoes_norm = tensoes_np / v_peak_actual
    else:
        tensoes_norm = tensoes_np # Já está em PU
        v_peak_actual = np.max(tensoes_norm) # Pico da forma de onda PU
        if v_peak_actual < const.EPSILON:
            logging.warning("Pico de tensão PU muito baixo para análise.")
            return {"T1_measured": None, "T2_measured": None}


    t_peak_idx = np.argmax(tensoes_norm)
    # v_peak_pu = tensoes_norm[t_peak_idx] # Deveria ser próximo de 1.0

    # Tempos de frente (10%, 30%, 90%)
    front_times = tempos_np[:t_peak_idx + 1]
    front_voltages = tensoes_norm[:t_peak_idx + 1]

    t_10, t_30, t_90 = None, None, None

    if len(front_times) < 2 or front_voltages[-1] < 0.9: # Pico não atingiu 0.9
        logging.warning("Forma de onda não atinge 90% do pico na frente ou dados insuficientes.")
    else:
        try:
            # Garantir que front_voltages seja estritamente crescente para interpolação
            # Isso pode não ser verdade se houver platôs ou pequenas oscilações
            # Uma forma simples é remover duplicatas consecutivas em front_voltages mantendo o primeiro tempo
            unique_front_voltages, unique_indices = np.unique(front_voltages, return_index=True)
            unique_front_times = front_times[unique_indices]

            if len(unique_front_voltages) < 2:
                 logging.warning("Não há pontos suficientes na frente após unique.")
                 raise ValueError("Pontos insuficientes na frente")


            if unique_front_voltages[-1] >= 0.1:
                 t_10 = np.interp(0.1, unique_front_voltages, unique_front_times)
            if unique_front_voltages[-1] >= 0.3:
                 t_30 = np.interp(0.3, unique_front_voltages, unique_front_times)
            if unique_front_voltages[-1] >= 0.9:
                 t_90 = np.interp(0.9, unique_front_voltages, unique_front_times)
        except Exception as e:
            logging.warning(f"Erro ao interpolar tempos de frente: {e}")


    # Tempo de cauda (50%)
    tail_times = tempos_np[t_peak_idx:]
    tail_voltages = tensoes_norm[t_peak_idx:]
    t_50_tail = None

    if len(tail_times) < 2 or tail_voltages[-1] > 0.5: # Não caiu para 50% ou não há cauda suficiente
        logging.warning("Forma de onda não atinge 50% na cauda ou dados insuficientes.")
    else:
        try:
            # Para interpolação na cauda, xp (tail_voltages_sorted) deve ser crescente.
            # Então, invertemos a cauda e buscamos 0.5.
            # tail_voltages_inverted_sorted = tail_voltages[::-1] # Valores da cauda, agora em ordem crescente (de V_fim para V_pico)
            # tail_times_inverted = tail_times[::-1] # Tempos correspondentes, agora em ordem decrescente

            # Filtrar para garantir que os valores em tail_voltages_inverted_sorted sejam únicos e crescentes
            # unique_tail_voltages_inv, unique_indices_inv = np.unique(tail_voltages_inverted_sorted, return_index=True)
            # unique_tail_times_inv = tail_times_inverted[unique_indices_inv]

            # if len(unique_tail_voltages_inv) < 2 or 0.5 > unique_tail_voltages_inv[-1] or 0.5 < unique_tail_voltages_inv[0]: # 0.5 fora do range
            #     logging.warning("Valor de 0.5 para interpolação na cauda fora do range dos dados únicos.")
            # else:
            #     t_50_tail = np.interp(0.5, unique_tail_voltages_inv, unique_tail_times_inv)
            
            # Abordagem mais simples para t_50_tail: encontrar onde cruza 0.5
            # Encontrar o primeiro índice onde a tensão é <= 0.5
            indices_below_half = np.where(tail_voltages <= 0.5)[0]
            if len(indices_below_half) > 0:
                first_idx_below_half = indices_below_half[0]
                if first_idx_below_half == 0: # Pico já é <= 0.5 (improvável para forma de onda válida)
                    if tail_voltages[0] <= 0.5 : # Se o pico for <= 0.5, t_50 é o tempo do pico
                         t_50_tail = tail_times[0]
                    else: # Algo estranho
                         logging.warning("Pico da cauda já é <= 0.5, mas o valor do pico não é. Estranho.")
                else:
                    # Interpolação linear entre (t_prev, v_prev) e (t_curr, v_curr)
                    # Onde v_prev > 0.5 e v_curr <= 0.5
                    v_prev = tail_voltages[first_idx_below_half - 1]
                    v_curr = tail_voltages[first_idx_below_half]
                    t_prev = tail_times[first_idx_below_half - 1]
                    t_curr = tail_times[first_idx_below_half]
                    
                    if v_prev == v_curr: # Evita divisão por zero se platô
                        t_50_tail = t_curr if v_curr <= 0.5 else t_prev
                    else:
                        t_50_tail = t_prev + (t_curr - t_prev) * (0.5 - v_prev) / (v_curr - v_prev)
            else:
                logging.warning("Não foi possível encontrar o ponto de 50% na cauda.")

        except Exception as e:
            logging.warning(f"Erro ao interpolar tempo de cauda: {e}")

    T1_measured, T2_measured = None, None
    if t_10 is not None and t_90 is not None:
        if t_90 > t_10:
            T1_measured = 1.25 * (t_90 - t_10)
        else:
            logging.warning(f"t_90 ({t_90}) não é maior que t_10 ({t_10}). Não é possível calcular T1 medido.")

    if t_50_tail is not None:
        T2_measured = t_50_tail # Assume origem virtual em t=0

    # Certificar que os valores retornados são float ou None
    return {
        "T1_measured": float(T1_measured) if T1_measured is not None else None,
        "T2_measured": float(T2_measured) if T2_measured is not None else None
    }


def generate_impulse_waveform_data(waveform_params: Dict[str, Any], impulse_type: str = "lightning", 
                                 duration_us: float = 200.0, step_us: float = 0.1) -> Dict[str, Any]:
    """
    Gera dados completos da forma de onda de impulso para plotagem.
    
    Args:
        waveform_params: Parâmetros calculados da forma de onda
        impulse_type: Tipo de impulso ("lightning", "switching", "chopped")
        duration_us: Duração total da simulação em μs
        step_us: Passo de tempo em μs
        
    Returns:
        Dicionário com arrays de tempo e tensão
    """
    # Gerar array de tempos
    tempos = np.arange(0, duration_us + step_us, step_us)
    tensoes = np.zeros_like(tempos)
    
    if impulse_type == "lightning":
        alfa = waveform_params.get("alfa", 0.02)
        beta = waveform_params.get("beta", 0.5)
        
        for i, t in enumerate(tempos):
            tensoes[i] = impulse_waveform(float(t), alfa, beta)
            
    elif impulse_type == "switching":
        T_p = waveform_params.get("tempo_pico", const.SWITCHING_IMPULSE_PEAK_TIME_NOM)
        T_2 = waveform_params.get("tempo_meia_onda", const.SWITCHING_IMPULSE_TAIL_TIME_NOM)
        
        for i, t in enumerate(tempos):
            tensoes[i] = switching_impulse_waveform(float(t), T_p, T_2)
            
    elif impulse_type == "chopped":
        alfa = waveform_params.get("alfa", 0.02)
        beta = waveform_params.get("beta", 0.5)
        tempo_corte = waveform_params.get("tempo_corte", 3.0)
        fator_sobretensao = waveform_params.get("fator_sobretensao", 1.2)
        
        for i, t in enumerate(tempos):
            if t < tempo_corte:
                tensoes[i] = impulse_waveform(float(t), alfa, beta)
            elif t < tempo_corte + 0.5:  # Período de sobretensão
                # Pico de sobretensão no momento do corte
                tensao_corte = impulse_waveform(tempo_corte, alfa, beta)
                tensoes[i] = tensao_corte * fator_sobretensao
            else:
                # Decaimento exponencial após o corte
                gamma = 0.5  # Constante de decaimento
                tensao_corte = impulse_waveform(tempo_corte, alfa, beta)
                sobretensao = tensao_corte * fator_sobretensao
                tensoes[i] = sobretensao * np.exp(-gamma * (float(t) - tempo_corte - 0.5))
    
    return {
        "tempos_us": tempos.tolist(),
        "tensoes_pu": tensoes.tolist(),  # Por unidade (0-1)
        "pico_tensao_pu": float(np.max(tensoes)),
        "tempo_pico_us": float(tempos[np.argmax(tensoes)])
    }