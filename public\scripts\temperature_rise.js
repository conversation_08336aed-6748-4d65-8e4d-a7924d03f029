// public/scripts/temperature_rise.js - ATUALIZADO

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { collectFormData, fillFormWithData } from './api_persistence.js';

// Função local para aguardar o sistema de API estar disponível
async function waitForApiSystem() {
    if (window.waitForApiSystem) {
        return await window.waitForApiSystem();
    }

    // Implementação local se a função global não estiver disponível
    return new Promise((resolve) => {
        const checkApiSystem = () => {
            if (window.apiDataSystem) {
                resolve();
            } else {
                setTimeout(checkApiSystem, 100);
            }
        };
        checkApiSystem();
    });
}

// Função para carregar dados do store 'temperatureRise' e preencher o formulário
async function loadTemperatureRiseDataAndPopulateForm() {
    try {
        console.log('[temperature_rise] Carregando dados do store "temperatureRise" e preenchendo formulário...');
        await waitForApiSystem(); // Garante que o sistema de persistência esteja pronto

        const store = window.apiDataSystem.getStore('temperatureRise');
        const data = await store.getData();

        if (data && data.formData) {
            const formElement = document.getElementById('temperature-rise-form');
            if (formElement) {
                fillFormWithData(formElement, data.formData);
                console.log('[temperature_rise] Formulário de elevação de temperatura preenchido com dados do store:', data.formData);
            } else {
                console.warn('[temperature_rise] Formulário "temperature-rise-form" não encontrado para preenchimento.');
            }
        } else {
            console.log('[temperature_rise] Nenhum dado de elevação de temperatura encontrado no store.');
        }

        // Carregar e exibir resultados salvos se existirem
        if (data && data.calculationResults) {
            console.log('[temperature_rise] Carregando resultados salvos:', data.calculationResults);
            displayCalculationResults(data.calculationResults);
        }
    } catch (error) {
        console.error('[temperature_rise] Erro ao carregar e preencher dados de elevação de temperatura:', error);
    }
}

// Função auxiliar para obter valor de input
function getInputValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : null;
}

// Função auxiliar para definir valor de output
function setOutputValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.value = value;
    }
}

// Função para exibir resultados calculados salvos
function displayCalculationResults(results) {
    console.log('[temperature_rise] Restaurando resultados calculados:', results);

    try {
        // Restaurar campos de resultado
        if (results.avgWindingTemp !== undefined) {
            setOutputValue('avg-winding-temp', results.avgWindingTemp);
        }
        if (results.avgWindingRise !== undefined) {
            setOutputValue('avg-winding-rise', results.avgWindingRise);
        }
        if (results.topOilRise !== undefined) {
            setOutputValue('top-oil-rise', results.topOilRise);
        }
        if (results.ptotUsed !== undefined) {
            setOutputValue('ptot-used', results.ptotUsed);
        }
        if (results.tau0Result !== undefined) {
            setOutputValue('tau0-result', results.tau0Result);
        }

        console.log('[temperature_rise] Resultados calculados restaurados com sucesso');
    } catch (error) {
        console.error('[temperature_rise] Erro ao restaurar resultados calculados:', error);
    }
}

async function getTransformerFormData() {
    const store = window.apiDataSystem.getStore('transformerInputs');
    const transformerData = await store.getData();
    return transformerData.formData || {};
}

async function saveCalculationResults(results) {
    try {
        if (window.apiDataSystem && window.collectFormData) {
            const temperatureRiseStore = window.apiDataSystem.getStore('temperatureRise');
            const currentStoreData = await temperatureRiseStore.getData() || {};
            const newFormData = window.collectFormData(document.getElementById('temperature-rise-form'));

            // Preservar dados existentes e adicionar novos resultados
            const updatedStoreData = {
                calculationResults: results,
                formData: { ...(currentStoreData.formData || {}), ...newFormData },
                timestamp: new Date().toISOString()
            };

            await temperatureRiseStore.updateData(updatedStoreData);
            console.log('[temperature_rise] Resultados salvos no store:', updatedStoreData);
        } else {
            console.warn('[temperature_rise] Sistema de persistência ou collectFormData não disponível para salvar resultados');
        }
    } catch (error) {
        console.error('[temperature_rise] Erro ao salvar resultados:', error);
    }
}

// Função para resetar resultados de cálculos de elevação de temperatura
function resetTemperatureRiseResults() {
    console.log('[temperature_rise] resetTemperatureRiseResults: Resetando resultados de cálculos');

    try {
        // Limpar campos de resultado
        const resultFields = [
            'avg-winding-temp',
            'avg-winding-rise',
            'top-oil-rise',
            'ptot-used',
            'tau0-result'
        ];

        resultFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = '';
            }
        });

        // Limpar mensagem de erro
        const errorElement = document.getElementById('temp-rise-error-message');
        if (errorElement) {
            errorElement.textContent = '';
        }

        console.log('[temperature_rise] Resultados de elevação de temperatura resetados');

    } catch (error) {
        console.error('[temperature_rise] Erro ao resetar resultados:', error);
    }
}

// Variável global para o gráfico
let temperatureChart = null;

// Função para gerar o diagrama de elevação de temperatura
async function generateTemperatureChart() {
    try {
        console.log('[temperature_rise] Gerando diagrama de elevação de temperatura...');

        // Obter dados básicos do transformador
        const transformerStore = window.apiDataSystem.getStore('transformerInputs');
        const transformerData = await transformerStore.getData();
        const basicData = transformerData.formData || {};

        // Preparar dados para o backend
        const requestData = {
            potencia_mva: parseFloat(basicData.potencia_mva) || 100,
            temp_ambiente: parseFloat(getInputValue('temp-amb')) || 40,
            elevacao_oleo_topo: 55, // Valor típico
            elevacao_enrol: 65, // Valor típico
            perdas_vazio: 50, // kW - valor exemplo
            perdas_carga: 500, // kW - valor exemplo
            carga_percentual: 1.0, // 100% de carga
            tipo_resfriamento: "ONAN"
        };

        // Por enquanto, mostrar mensagem informativa em vez de chamar endpoint inexistente
        console.log('[temperature_rise] Dados preparados para gráfico:', requestData);

        // Mostrar mensagem informativa
        const chartContainer = document.getElementById('temperature-chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Funcionalidade em Desenvolvimento</strong><br>
                    O diagrama de elevação de temperatura será implementado em uma versão futura.
                </div>
            `;
        }

        // Limpar mensagem de erro
        const errorElement = document.getElementById('temp-rise-error-message');
        if (errorElement) {
            errorElement.textContent = '';
        }

        console.log('[temperature_rise] Diagrama de temperatura: Funcionalidade temporariamente desabilitada');

    } catch (error) {
        console.error('[temperature_rise] Erro ao configurar diagrama:', error);
        // Exibir erro para o usuário
        const errorElement = document.getElementById('temp-rise-error-message');
        if (errorElement) {
            errorElement.textContent = `Erro: ${error.message}`;
        }
    }
}

// Função para gerar curvas de exemplo
function generateExampleCurves() {
    const tempo = [];
    const temp_oleo = [];
    const temp_enrol = [];
    const temp_ambiente = 40;

    // Gerar dados de exemplo para 8 horas
    for (let t = 0; t <= 8; t += 0.1) {
        tempo.push(t);
        // Curva exponencial para óleo
        const theta_oleo = 55 * (1 - Math.exp(-t / 2.5));
        temp_oleo.push(temp_ambiente + theta_oleo);

        // Curva exponencial para enrolamento (mais rápida)
        const theta_enrol = 65 * (1 - Math.exp(-t / 1.8));
        temp_enrol.push(temp_ambiente + theta_enrol);
    }

    return {
        tempo: tempo,
        temp_oleo: temp_oleo,
        temp_enrol: temp_enrol,
        temp_ambiente: temp_ambiente
    };
}

// Função para exibir o gráfico de temperatura
function displayTemperatureChart(curves) {
    const canvas = document.getElementById('temperature-rise-chart');
    if (!canvas) {
        console.error('[temperature_rise] Canvas do gráfico não encontrado');
        return;
    }

    // Destruir gráfico anterior se existir
    if (temperatureChart) {
        temperatureChart.destroy();
    }

    const ctx = canvas.getContext('2d');

    temperatureChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: curves.tempo.map(t => t.toFixed(1)),
            datasets: [
                {
                    label: 'Temperatura do Óleo (°C)',
                    data: curves.temp_oleo,
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Temperatura do Enrolamento (°C)',
                    data: curves.temp_enrol,
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Temperatura Ambiente (°C)',
                    data: curves.tempo.map(() => curves.temp_ambiente),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 1,
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Curvas de Elevação de Temperatura',
                    color: '#ecf0f1'
                },
                legend: {
                    display: true,
                    labels: {
                        color: '#ecf0f1'
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Tempo (horas)',
                        color: '#ecf0f1'
                    },
                    ticks: {
                        color: '#ecf0f1'
                    },
                    grid: {
                        color: '#2c3e50'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Temperatura (°C)',
                        color: '#ecf0f1'
                    },
                    ticks: {
                        color: '#ecf0f1'
                    },
                    grid: {
                        color: '#2c3e50'
                    }
                }
            }
        }
    });
}

// Lógica para o botão "Calcular Elevação"
async function setupCalcButton() {
    const calcBtn = document.getElementById('limpar-temp-rise'); // ID original é "limpar-temp-rise"
    if (calcBtn) {
        calcBtn.addEventListener('click', async function() {
            console.log('Botão Calcular Elevação clicado!');

            const tempAmb = parseFloat(getInputValue('temp-amb'));
            const windingMaterial = getInputValue('winding-material');
            const resCold = parseFloat(getInputValue('res-cold'));
            const tempCold = parseFloat(getInputValue('temp-cold'));
            const resHot = parseFloat(getInputValue('res-hot'));
            const tempTopOil = parseFloat(getInputValue('temp-top-oil'));
            const deltaThetaOilMax = parseFloat(getInputValue('delta-theta-oil-max'));

            const transformerData = await getTransformerFormData();
            const basicData = transformerData || {}; // formData já é o objeto de dados básicos
            const pesosData = transformerData || {}; // Pesos também estão no formData

            // Obter dados de perdas do store de perdas
            const lossesStore = window.apiDataSystem.getStore('losses');
            const lossesData = await lossesStore.getData();
            const lossesFormData = lossesData.formData || {};

            // Usar "Perdas em Carga Totais Tap- (kW)" conforme solicitado
            const perdasTotaisTapMenor = parseFloat(lossesFormData['perdas-carga-kw_U_min']) || 0;
            const perdasTotais = perdasTotaisTapMenor;

            let errorMessage = '';

            try {
                if (isNaN(tempAmb) || isNaN(resCold) || isNaN(tempCold) || isNaN(resHot) || isNaN(tempTopOil)) {
                    throw new Error("Preencha todos os campos de medição e ambiente.");
                }

                const C_material = (windingMaterial === 'cobre') ? 234.5 : 225;

                const thetaW = tempAmb + ((resHot / resCold) * (C_material + tempCold) - C_material);
                setOutputValue('avg-winding-temp', thetaW.toFixed(2));

                const deltaThetaW = thetaW - tempAmb;
                setOutputValue('avg-winding-rise', deltaThetaW.toFixed(2));

                const deltaThetaOil = tempTopOil - tempAmb;
                setOutputValue('top-oil-rise', deltaThetaOil.toFixed(2));

                setOutputValue('ptot-used', perdasTotais.toFixed(2));

                let tau0 = '';
                if (!isNaN(deltaThetaOilMax) && pesosData.peso_total && pesosData.peso_oleo && perdasTotais > 0) {
                     const mT_ton = pesosData.peso_total;
                     const mO_ton = pesosData.peso_oleo;
                     tau0 = (0.132 * (mT_ton - 0.5 * mO_ton) * deltaThetaOilMax) / perdasTotais;
                    setOutputValue('tau0-result', tau0.toFixed(2));
                } else {
                    tau0 = 'N/A';
                    setOutputValue('tau0-result', tau0);
                }

                // Salvar resultados calculados para persistência
                const calculationResults = {
                    avgWindingTemp: thetaW.toFixed(2),
                    avgWindingRise: deltaThetaW.toFixed(2),
                    topOilRise: deltaThetaOil.toFixed(2),
                    ptotUsed: perdasTotais.toFixed(2),
                    tau0Result: (tau0 === 'N/A') ? tau0 : tau0.toFixed(2)
                };

                await saveCalculationResults(calculationResults);
                console.log('[temperature_rise] Resultados calculados salvos:', calculationResults);

            } catch (error) {
                errorMessage = error.message;
            }

            document.getElementById('temp-rise-error-message').textContent = errorMessage;
        });
    }
}

// Função para limpar valores padrão indesejados
function clearUnwantedDefaultValues() {
    console.log('[temperature_rise] Verificando valores padrão indesejados...');

    // NÃO limpar dropdown de material de enrolamento - deixar a persistência funcionar
    const materialSelect = document.getElementById('winding-material');
    if (materialSelect) {
        console.log('[temperature_rise] Material de enrolamento atual:', materialSelect.value, '- mantendo valor');
    }

    // Limpar outros campos que possam ter valores padrão indesejados
    const fieldsToCheck = [
        'ptot-used',
        'ambient-temp',
        'altitude'
    ];

    fieldsToCheck.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field && field.value && field.hasAttribute('data-clear-default')) {
            console.log(`[temperature_rise] Limpando valor padrão do campo ${fieldId}:`, field.value);
            field.value = '';
        }
    });

    console.log('[temperature_rise] Verificação de valores padrão concluída');
}

// Função para garantir persistência do dropdown Material Enrolamento
function ensureWindingMaterialPersistence() {
    const materialSelect = document.getElementById('winding-material');
    if (!materialSelect) {
        console.warn('[temperature_rise] Dropdown winding-material não encontrado');
        return;
    }

    console.log('[temperature_rise] Configurando persistência para dropdown Material Enrolamento');

    // Adicionar listener para salvar quando valor muda
    materialSelect.addEventListener('change', async function() {
        console.log('[temperature_rise] Material enrolamento alterado para:', this.value);

        // Método 1: Usar sistema de persistência automática se disponível
        if (window.setupApiFormPersistence) {
            try {
                // Forçar salvamento imediato
                const event = new Event('input', { bubbles: true });
                this.dispatchEvent(event);
                console.log('[temperature_rise] Evento de input disparado para persistência automática');
            } catch (error) {
                console.error('[temperature_rise] Erro ao disparar evento de persistência:', error);
            }
        }

        // Método 2: Salvamento manual direto no store
        if (window.apiDataSystem) {
            try {
                const temperatureRiseStore = window.apiDataSystem.getStore('temperatureRise');
                const currentData = await temperatureRiseStore.getData() || {};

                // Coletar dados do formulário manualmente se collectFormData não estiver disponível
                let formData = {};
                if (window.collectFormData) {
                    formData = window.collectFormData(document.getElementById('temperature-rise-form'));
                } else {
                    // Fallback: coletar apenas o campo específico
                    formData = { 'winding-material': this.value };
                }

                const updatedData = {
                    ...currentData,
                    formData: { ...(currentData.formData || {}), ...formData },
                    timestamp: new Date().toISOString()
                };

                await temperatureRiseStore.updateData(updatedData);
                console.log('[temperature_rise] Material enrolamento salvo no store:', this.value);
            } catch (error) {
                console.error('[temperature_rise] Erro ao salvar material enrolamento no store:', error);
            }
        }

        // Método 3: Salvamento em localStorage como backup
        try {
            const backupKey = 'temperatureRise_windingMaterial';
            localStorage.setItem(backupKey, this.value);
            console.log('[temperature_rise] Material enrolamento salvo em localStorage:', this.value);
        } catch (error) {
            console.error('[temperature_rise] Erro ao salvar em localStorage:', error);
        }
    });

    // Carregar valor salvo ao inicializar
    loadSavedWindingMaterial();

    console.log('[temperature_rise] Persistência do material enrolamento configurada com múltiplos métodos');
}

// Função para carregar valor salvo do material de enrolamento
async function loadSavedWindingMaterial() {
    const materialSelect = document.getElementById('winding-material');
    if (!materialSelect) return;

    try {
        // Tentar carregar do store primeiro
        if (window.apiDataSystem) {
            const temperatureRiseStore = window.apiDataSystem.getStore('temperatureRise');
            const data = await temperatureRiseStore.getData();

            if (data && data.formData && data.formData['winding-material']) {
                materialSelect.value = data.formData['winding-material'];
                console.log('[temperature_rise] Material enrolamento carregado do store:', materialSelect.value);
                return;
            }
        }

        // Fallback: carregar do localStorage
        const backupKey = 'temperatureRise_windingMaterial';
        const savedValue = localStorage.getItem(backupKey);
        if (savedValue) {
            materialSelect.value = savedValue;
            console.log('[temperature_rise] Material enrolamento carregado do localStorage:', savedValue);
        }
    } catch (error) {
        console.error('[temperature_rise] Erro ao carregar material enrolamento salvo:', error);
    }
}

// Função de inicialização do módulo Elevação de Temperatura
async function initTemperatureRise() {
    console.log('Módulo Elevação de Temperatura carregado e pronto para interatividade.');

    if (window.setupApiFormPersistence) {
        await window.setupApiFormPersistence('temperature-rise-form', 'temperatureRise');
    } else {
        console.warn('[temperature_rise] Sistema de persistência não disponível');
    }

    // Carregar e preencher os dados do próprio módulo de elevação de temperatura
    await loadTemperatureRiseDataAndPopulateForm();

    // Configurar persistência do dropdown Material Enrolamento
    ensureWindingMaterialPersistence();

    // Adicionar listener para o evento transformerDataUpdated
    document.addEventListener('transformerDataUpdated', async (event) => {
        console.log('[temperature_rise] Evento transformerDataUpdated recebido:', event.detail);

        // REMOVIDO resetTemperatureRiseResults() - já é feito pelo sistema de ocultação em common_module.js
        // resetTemperatureRiseResults();

        // Recarrega os dados do próprio módulo para garantir consistência (incluindo resultados salvos)
        await loadTemperatureRiseDataAndPopulateForm();

        // Limpar valores padrão indesejados após recarregamento
        clearUnwantedDefaultValues();

        // Recarregar o painel de informações do transformador
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);

        console.log('[temperature_rise] Dados do transformador atualizados - resultados resetados');
    });

    const transformerInfoPlaceholderId = 'transformer-info-temperature_rise-page';

    // Verificar se o elemento existe antes de tentar carregar o painel
    const placeholderElement = document.getElementById(transformerInfoPlaceholderId);
    if (placeholderElement) {
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);
    } else {
        console.warn(`[temperature_rise] Elemento ${transformerInfoPlaceholderId} não encontrado - módulo pode não estar ativo`);
    }

    // Limpar valores padrão indesejados após carregamento completo
    clearUnwantedDefaultValues();

    setupCalcButton();
    setupChartButton();
}

// Função para configurar o botão de gerar diagrama
function setupChartButton() {
    const chartBtn = document.getElementById('generate-temp-chart-btn');
    if (chartBtn) {
        chartBtn.addEventListener('click', generateTemperatureChart);
        console.log('[temperature_rise] Botão de gerar diagrama configurado');
    }
}

// SPA routing: executa quando o módulo temperature_rise é carregado
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'temperature_rise') {
        console.log('[temperature_rise] SPA routing init');
        initTemperatureRise();
    }
});

// Fallback para carregamento direto da página (se não for SPA)
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('temperature-rise-form')) { // Assumindo um ID de formulário principal para o módulo
        console.log('[temperature_rise] DOMContentLoaded init (fallback)');
        initTemperatureRise();
    }
});