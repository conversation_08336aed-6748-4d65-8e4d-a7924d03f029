<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dados Básicos do Transformador - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF;
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E;
            --card-bg-color: #2D2D2D;
            --sidebar-bg-color: #252525;
            --border-color: #6c757d;
            --link-color: #4DA3FF;
            --link-hover-color: #80BDFF;
            --heading-color: #FFFFFF;
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc-h1 {
            margin-top: 10px;
            font-weight: bold;
        }
        .toc-h2 {
            padding-left: 15px;
        }
        .toc-h3 {
            padding-left: 30px;
            font-size: 0.9em;
        }
        .toc-h4 {
            padding-left: 45px;
            font-size: 0.85em;
        }
        .toc a {
            color: var(--link-color);
            text-decoration: none;
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h2 {
            border-bottom: 1px solid var(--primary-color);
            padding-bottom: 5px;
        }
        code {
            color: #ff9d00;
            background-color: #2a2a2a;
            padding: 2px 4px;
            border-radius: 3px;
        }
        pre {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 12px;
            border: 1px solid #555;
        }
        th {
            background-color: #444;
        }
        tr:nth-child(even) {
            background-color: #3f3f3f;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px;
            background-color: #444;
            border: 1px solid #555;
            color: #e0e0e0;
            border-radius: 4px;
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const markdownContent = `# Dados Básicos do Transformador

Este documento detalha os parâmetros básicos de entrada para transformadores, suas relações e os cálculos derivados desses dados que são utilizados em diversos módulos de ensaio.

---

## 1. Parâmetros de Entrada

Os dados básicos do transformador são divididos em várias categorias:

### 1.1. Especificações Gerais

| Parâmetro               | Descrição                                  | Unidade  | Variável no Código             |
|-------------------------|-------------------------------------------|----------|--------------------------------|
| Tipo de Transformador   | Monofásico ou Trifásico                   | -        | \`tipo_transformador\`         |
| Potência Nominal        | Potência nominal do transformador         | MVA      | \`potencia_mva\`               |
| Frequência              | Frequência nominal de operação            | Hz       | \`frequencia\`                 |
| Grupo de Ligação        | Defasamento angular (ex: Dyn11)           | -        | \`grupo_ligacao\`              |
| Líquido Isolante        | Tipo de óleo ou fluido isolante           | -        | \`liquido_isolante\`           |
| Elevação de Óleo        | Elevação de temperatura do óleo           | °C       | \`elevacao_oleo_topo\`         |
| Tipo de Isolamento      | Uniforme ou não-uniforme                  | -        | \`tipo_isolamento\`            |
| Norma ISO               | Norma de fabricação (ex: IEC)             | -        | \`norma_iso\`                  |

### 1.2. Parâmetros dos Enrolamentos

Para cada enrolamento (AT, BT, Terciário), os seguintes parâmetros são necessários:

| Parâmetro               | Descrição                                  | Unidade  | Variável no Código (ex: AT)    |
|-------------------------|-------------------------------------------|----------|--------------------------------|
| Tensão                  | Tensão nominal do enrolamento             | kV       | \`tensao_at\`                  |
| Classe de Tensão        | Classe de isolamento                      | kV       | \`classe_tensao_at\`           |
| Elevação do Enrolamento | Elevação de temperatura do enrolamento    | °C       | \`elevacao_enrol\`             |
| Impedância              | Impedância percentual                     | %        | \`impedancia\`                 |
| NBI / BIL               | Nível Básico de Impulso                   | kV       | \`nbi_at\`                     |
| IM / SIL                | Impulso de Manobra                        | kV       | \`sil_at\`                     |
| Conexão                 | Tipo de conexão (estrela, triângulo)      | -        | \`conexao_at\`                 |
| Tensão Bucha Neutro     | Classe de tensão da bucha de neutro       | kV       | \`tensao_bucha_neutro_at\`     |
| NBI Neutro              | NBI da bucha de neutro                    | kV       | \`nbi_neutro_at\`              |

### 1.3. Parâmetros de Tap

| Parâmetro               | Descrição                                  | Unidade  | Variável no Código             |
|-------------------------|-------------------------------------------|----------|--------------------------------|
| Tensão AT Tap Maior     | Tensão no tap de maior tensão             | kV       | \`tensao_at_tap_maior\`        |
| Impedância Tap Maior    | Impedância no tap de maior tensão         | %        | \`impedancia_tap_maior\`       |
| Tensão AT Tap Menor     | Tensão no tap de menor tensão             | kV       | \`tensao_at_tap_menor\`        |
| Impedância Tap Menor    | Impedância no tap de menor tensão         | %        | \`impedancia_tap_menor\`       |

### 1.4. Tensões de Ensaio

| Parâmetro               | Descrição                                  | Unidade  | Variável no Código                 |
|-------------------------|-------------------------------------------|----------|------------------------------------|
| Tensão Aplicada AT      | Tensão de ensaio aplicada AT              | kV       | \`teste_tensao_aplicada_at\`       |
| Tensão Aplicada BT      | Tensão de ensaio aplicada BT              | kV       | \`teste_tensao_aplicada_bt\`       |
| Tensão Aplicada Ter.    | Tensão de ensaio aplicada Terciário       | kV       | \`teste_tensao_aplicada_terciario\`|
| Tensão Induzida AT      | Tensão de ensaio induzida AT              | kV       | \`teste_tensao_induzida_at\`       |

### 1.5. Pesos

| Parâmetro               | Descrição                                  | Unidade  | Variável no Código             |
|-------------------------|-------------------------------------------|----------|--------------------------------|
| Peso Total              | Peso total do transformador               | ton      | \`peso_total\`                 |
| Peso Parte Ativa        | Peso da parte ativa                       | ton      | \`peso_parte_ativa\`           |
| Peso Óleo               | Peso do óleo isolante                     | ton      | \`peso_oleo\`                  |
| Peso Tanque/Acessórios  | Peso do tanque e acessórios               | ton      | \`peso_tanque_acessorios\`     |

## 2. Cálculos Derivados

### 2.1. Correntes Nominais
O fator de cálculo (\`fator\`) é **1.0** para transformadores monofásicos e **√3** para trifásicos.

*   **Corrente Nominal AT:** $$I_{nom, AT} = \\frac{S_{MVA} \\times 1000}{fator \\times V_{AT, kV}}$$
*   **Corrente Nominal BT:** $$I_{nom, BT} = \\frac{S_{MVA} \\times 1000}{fator \\times V_{BT, kV}}$$
*   **Corrente Nominal Terciário:** $$I_{nom, Ter} = \\frac{S_{MVA} \\times 1000}{fator \\times V_{Ter, kV}}$$

### 2.2. Correntes e Impedâncias nos Taps
As correntes nos taps são calculadas de forma análoga às correntes nominais, utilizando as tensões dos respectivos taps.

*   **Corrente Tap Maior:** $$I_{nom, tap\\_maior} = \\frac{S_{MVA} \\times 1000}{fator \\times V_{tap\\_maior, kV}}$$
*   **Corrente Tap Menor:** $$I_{nom, tap\\_menor} = \\frac{S_{MVA} \\times 1000}{fator \\times V_{tap\\_menor, kV}}$$

Se a impedância de um tap não for fornecida, ela é calculada com base na impedância nominal, ajustada pelo quadrado da relação de tensões:

*   **Impedância Tap (Calculada):** $$Z_{tap, \\%} = Z_{nom, \\%} \\times \\left( \\frac{V_{tap, kV}}{V_{nom, kV}} \\right)^2$$

### 2.3. Impedância Base e Indutância de Curto-Circuito

*   **Impedância Base AT:** $$Z_{base, AT} = \\frac{V_{AT, kV}^2 \\times 1000}{S_{MVA}} \\quad (\\Omega)$$
*   **Impedância de Curto-Circuito:** $$Z_{cc, \\Omega} = Z_{base, AT} \\times \\frac{Z_{nom, \\%}}{100} \\quad (\\Omega)$$
*   **Indutância de Curto-Circuito:** $$L_{cc, H} = \\frac{Z_{cc, \\Omega}}{2 \\pi f} \\quad (H)$$

### 2.4. IAC (Impulso Atmosférico Cortado)
Se a norma especificada (\`norma_iso\`) for **IEC**, o valor do IAC é calculado a partir do NBI.

*   **IAC (kV):** $$IAC = NBI \\times 1.15$$
O fator de 1.15 corresponde a \`const.IAC_NBI_FACTOR\`.

## 3. Níveis de Isolamento (Tabelas Completas)

### 3.1. NBI / BIL (Nível Básico de Impulso)

| Classe de Tensão (kV) | NBI / BIL Típico (kVp) - IEC/NBR | NBI / BIL Típico (kVp) - IEEE |
|-----------------------|----------------------------------|-------------------------------|
| 1.2                   | -                                | 30                            |
| 3.6                   | 40                               | -                             |
| 5                     | -                                | 60                            |
| 7.2                   | 60                               | -                             |
| 8.7                   | -                                | 75                            |
| 12                    | 75                               | -                             |
| 15                    | 95, 110                          | 95, 110                       |
| 17.5                  | 95                               | -                             |
| 24                    | 125                              | -                             |
| 25.8                  | -                                | 125, 150                      |
| 34.5                  | -                                | 150, 200                      |
| 36                    | 170                              | -                             |
| 46                    | -                                | 200, 250                      |
| 52                    | 250                              | -                             |
| 69                    | -                                | 250, 350                      |
| 72.5                  | 325                              | -                             |
| 115                   | -                                | 350, 450, 550                 |
| 123                   | 450, 550                         | -                             |
| 138                   | -                                | 450, 550, 650                 |
| 145                   | 550, 650                         | -                             |
| 161                   | -                                | 550, 650, 750                 |
| 170                   | 650, 750                         | -                             |
| 230                   | -                                | 650, 750, 825, 900            |
| 245                   | 750, 850, 950, 1050              | -                             |
| 345                   | -                                | 900, 1050, 1175               |
| 362                   | 950, 1050, 1175                  | -                             |
| 500                   | -                                | 1300, 1425, 1550, 1675, 1800 |
| 525                   | 1300, 1425, 1550                 | -                             |
| 765                   | -                                | 1800, 1925, 2050              |
| 800                   | 1800, 1950, 2100                 | -                             |

### 3.2. IM / SIL (Impulso de Manobra)
*Exibido apenas para classes de tensão > 170 kV*
| Classe de Tensão (kV) | IM / SIL Típico (kVp) - IEC/NBR | IM / SIL Típico (kVp) - IEEE |
|-----------------------|---------------------------------|------------------------------|
| 230                   | -                               | 540, 620, 685, 745           |
| 245                   | 650, 750, 850                   | -                            |
| 345                   | -                               | 745, 870, 975                |
| 362                   | 850, 950                        | -                            |
| 500                   | -                               | 1080, 1180, 1290, 1390, 1500|
| 525                   | 1050, 1175                      | -                            |
| 765                   | -                               | 1500, 1600, 1700             |
| 800                   | 1425, 1550                      | -                            |

### 3.3. Tensão Aplicada
| Classe de Tensão (kV) | Tensão Aplicada (kV rms) - IEC/NBR | Tensão Aplicada (kV rms) - IEEE |
|-----------------------|------------------------------------|---------------------------------|
| 1.2                   | 10                                 | 10                              |
| 3.6                   | 10                                 | -                               |
| 5                     | -                                  | 19                              |
| 7.2                   | 20                                 | -                               |
| 8.7                   | -                                  | 26                              |
| 12                    | 28                                 | -                               |
| 15                    | 38                                 | 34                              |
| 17.5                  | 38                                 | -                               |
| 24                    | 50                                 | -                               |
| 25.8                  | -                                  | 50                              |
| 34.5                  | -                                  | 70                              |
| 36                    | 70                                 | -                               |
| 46                    | -                                  | 95                              |
| 52                    | 95                                 | -                               |
| 69                    | -                                  | 140                             |
| 72.5                  | 140                                | -                               |
| 115                   | -                                  | 140, 185, 230                   |
| 123                   | 185, 230                           | -                               |
| 138                   | -                                  | 185, 230, 275                   |
| 145                   | 230, 275                           | -                               |
| 161                   | -                                  | 230, 275, 325                   |
| 170                   | 275, 325                           | -                               |
| 230                   | -                                  | 275, 325, 360, 395              |
| 245                   | 325, 360, 395, 460                 | -                               |
| 345                   | -                                  | 395, 460, 520                   |
| 362                   | 460, 510                           | -                               |
| 500                   | -                                  | -                               |
| 525                   | 570, 630, 680                      | -                               |
| 765                   | -                                  | 800, 840, 900                   |
| 800                   | 830, 900, 960                      | -                               |

### 3.4. Tensão Induzida
| Classe de Tensão (kV) | ACSD (kV rms) - IEC/NBR | ACLD (kV rms) - IEC/NBR | ACSD (kV rms) - IEEE | ACLD (kV rms) - IEEE |
|-----------------------|-------------------------|-------------------------|----------------------|----------------------|
| 72.5                  | 140                     | -                       | -                    | -                    |
| 115                   | -                       | -                       | 140, 185, 230        | 120                  |
| 123                   | 230                     | 230                     | -                    | -                    |
| 138                   | -                       | -                       | 185, 230, 275        | 145                  |
| 145                   | 275                     | 325                     | -                    | -                    |
| 161                   | -                       | -                       | 230, 275, 325        | 170                  |
| 170                   | 325                     | 360                     | -                    | -                    |
| 230                   | -                       | -                       | 275, 325, 360, 395   | 240                  |
| 245                   | 360, 395, 460           | 395, 460                | -                    | -                    |
| 345                   | -                       | -                       | 395, 460, 520        | 360                  |
| 362                   | 510                     | 570                     | -                    | -                    |
| 500                   | -                       | -                       | -                    | 550                  |
| 525                   | 630, 680                | 680, 740                | -                    | -                    |
| 765                   | -                       | -                       | 800, 840, 900        | 900                  |
| 800                   | 900, 960                | 960, 1030               | -                    | -                    |

`;

        // Function to generate TOC and add IDs to headings
        function generateTOC(html) {
            const container = document.createElement('div');
            container.innerHTML = html;

            const headings = container.querySelectorAll('h1, h2, h3, h4');
            const toc = document.getElementById('toc');
            toc.innerHTML = '';

            headings.forEach((heading, index) => {
                const text = heading.textContent.trim();
                const level = parseInt(heading.tagName.substring(1));
                const slug = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
                const id = `${slug}-${index}`;
                
                heading.id = id;

                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;
                
                const link = document.createElement('a');
                link.href = `#${id}`;
                link.textContent = text;
                link.setAttribute('data-heading-id', id);

                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-heading-id');
                    document.getElementById(targetId).scrollIntoView({ behavior: 'smooth' });
                    history.pushState(null, null, `#${targetId}`);
                    document.querySelectorAll('.toc a').forEach(a => a.classList.remove('active'));
                    this.classList.add('active');
                });

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }
        
        // Function to render markdown
        function renderMarkdown() {
            const contentDiv = document.getElementById('markdown-content');
            const searchTerm = document.getElementById('search-input').value.trim();
            
            const renderedHTML = marked.parse(markdownContent);
            const finalHTML = searchTerm ? renderedHTML.replace(new RegExp(`(${searchTerm})`, 'gi'), '<span class="highlight">$1</span>') : renderedHTML;

            contentDiv.innerHTML = finalHTML;
            generateTOC(contentDiv.innerHTML);
            contentDiv.querySelectorAll('pre code').forEach(hljs.highlightBlock);
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            renderMarkdown();
            document.getElementById('search-input').addEventListener('input', renderMarkdown);
        });
    </script>
</body>
</html>