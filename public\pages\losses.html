<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>An<PERSON><PERSON><PERSON> <PERSON>das - Transformer Test Suite</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <link rel="stylesheet" href="/assets/custom.css">
</head>
<body>

<div class="container-fluid ps-2 pe-0 d-flex flex-column container-full-height">
    <div id="transformer-info-losses-page" class="mb-2"><div class="text-muted text-center p-2 small">Carregando informações...</div></div>

    <div class="card flex-grow-1 d-flex flex-column">
        <div class="card-header d-flex align-items-center"><h5 class="text-center m-0 flex-grow-1 card-header-title">ANÁLISE DE PERDAS</h5></div>
        <div class="card-body d-flex flex-column flex-grow-1 p-2">
            <ul class="nav nav-tabs mb-2" id="lossesTab" role="tablist">
                <li class="nav-item" role="presentation"><button class="nav-link active losses-tab-btn" id="perdas-vazio-tab-btn" data-bs-toggle="tab" data-bs-target="#perdas-vazio-content-pane" type="button" role="tab">Perdas em Vazio</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link losses-tab-btn" id="perdas-carga-tab-btn" data-bs-toggle="tab" data-bs-target="#perdas-carga-content-pane" type="button" role="tab">Perdas em Carga</button></li>
            </ul>

            <div class="tab-content flex-grow-1" id="lossesTabContent">
                <!-- ================== PAINEL PERDAS EM VAZIO ================== -->
                <div class="tab-pane fade show active h-100" id="perdas-vazio-content-pane" role="tabpanel">
                    <div class="d-flex flex-column h-100">
                        <div class="row g-2 mb-2">
                            <div class="col-lg-4 col-md-12 d-flex flex-column">
                                <div class="card h-100 card-sm">
                                    <div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">PARÂMETROS DE ENTRADA (PERDAS EM VAZIO)</h6></div>
                                    <div class="card-body p-2 d-flex flex-column">
                                        <div class="mb-2 row gx-1 align-items-center"><label for="perdas-vazio-kw" class="col-sm-7 col-form-label col-form-label-sm text-end">Perdas em Vazio (kW):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="perdas-vazio-kw" name="perdas-vazio-kw" placeholder="Ex: 50" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="peso-projeto-Ton" class="col-sm-7 col-form-label col-form-label-sm text-end">Peso do Núcleo (Ton):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="peso-projeto-Ton" name="peso-projeto-Ton" placeholder="Ex: 25" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="corrente-excitacao" class="col-sm-7 col-form-label col-form-label-sm text-end">Corrente Excitação (%):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="corrente-excitacao" name="corrente-excitacao" placeholder="Ex: 0.3" step="0.01"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="inducao-nucleo" class="col-sm-7 col-form-label col-form-label-sm text-end">Indução Núcleo (T):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="inducao-nucleo" name="inducao-nucleo" placeholder="Ex: 1.7" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="corrente-excitacao-1-1" class="col-sm-7 col-form-label col-form-label-sm text-end">Corrente Exc. 1.1pu (%):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="corrente-excitacao-1-1" name="corrente-excitacao-1-1" placeholder="Opcional" step="0.01"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="corrente-excitacao-1-2" class="col-sm-7 col-form-label col-form-label-sm text-end">Corrente Exc. 1.2pu (%):</label><div class="col-sm-5"><input type="number" class="form-control form-control-sm" id="corrente-excitacao-1-2" name="corrente-excitacao-1-2" placeholder="Opcional" step="0.01"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="tipo-aco" class="col-sm-7 col-form-label col-form-label-sm text-end">Tipo de Aço:</label><div class="col-sm-5"><select class="form-select form-select-sm" id="tipo-aco" name="tipo-aco"><option value="M4" selected>M4</option><option value="H110-27">H110-27</option></select></div></div>
                                        <div class="mt-auto pt-2"><button type="button" class="btn btn-primary w-100 btn-sm" id="calcular-perdas-vazio">Calcular Perdas em Vazio</button></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6 d-flex flex-column"><div class="card h-100 card-sm"><div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">PARÂMETROS GERAIS E DE MATERIAL</h6></div><div class="card-body p-1 d-flex align-items-center justify-content-center results-placeholder" id="parametros-gerais-card-body"></div></div></div>
                            <div class="col-lg-4 col-md-6 d-flex flex-column"><div class="card h-100 card-sm"><div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">RESULTADOS POR NÍVEL DE TENSÃO (DUT)</h6></div><div class="card-body p-1 d-flex align-items-center justify-content-center results-placeholder" id="dut-voltage-level-results-body"></div></div></div>
                        </div>
                        <div class="row g-2 mb-2"><div class="col-md-12"><div class="card card-sm"><div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">ANÁLISE TAPS SUT / CORRENTE EPS (PERDAS EM VAZIO)</h6></div><div class="card-body p-1 results-placeholder" id="sut-analysis-results-area"></div></div></div></div>
                    </div>
                </div>

                <!-- ================== PAINEL PERDAS EM CARGA ================== -->
                <div class="tab-pane fade h-100" id="perdas-carga-content-pane" role="tabpanel">
                    <div class="d-flex flex-column h-100">
                        <div class="row g-2 mb-2">
                            <!-- Coluna de Entradas -->
                            <div class="col-lg-3 col-md-12 d-flex flex-column pe-lg-1">
                                <div class="card h-100 card-sm">
                                    <div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">PARÂMETROS DE ENTRADA (PERDAS EM CARGA)</h6></div>
                                    <div class="card-body p-2 d-flex flex-column">
                                        <div class="mb-2 row gx-1 align-items-center"><label for="temperatura-referencia" class="col-sm-8 col-form-label col-form-label-sm text-end">Temp. Referência (°C):</label><div class="col-sm-4"><select class="form-select form-select-sm" id="temperatura-referencia" name="temperatura-referencia"><option value="75" selected>75°C</option><option value="85">85°C</option><option value="115">115°C</option></select></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="perdas-carga-kw_U_min" class="col-sm-8 col-form-label col-form-label-sm text-end">Perdas em Carga Totais Tap- (kW):</label><div class="col-sm-4"><input type="number" class="form-control form-control-sm" id="perdas-carga-kw_U_min" name="perdas-carga-kw_U_min" placeholder="Ex: 150" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="perdas-carga-kw_U_nom" class="col-sm-8 col-form-label col-form-label-sm text-end">Perdas em Carga Totais Tap Nom (kW):</label><div class="col-sm-4"><input type="number" class="form-control form-control-sm" id="perdas-carga-kw_U_nom" name="perdas-carga-kw_U_nom" placeholder="Ex: 160" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="perdas-carga-kw_U_max" class="col-sm-8 col-form-label col-form-label-sm text-end">Perdas em Carga Totais Tap+ (kW):</label><div class="col-sm-4"><input type="number" class="form-control form-control-sm" id="perdas-carga-kw_U_max" name="perdas-carga-kw_U_max" placeholder="Ex: 140" step="0.1"></div></div>
                                        <div class="mb-2 row gx-1 align-items-center"><label for="factor-cap-banc-overvoltage" class="col-sm-8 col-form-label col-form-label-sm text-end">Fator sobretensão V> Banco:</label><div class="col-sm-4"><input type="number" class="form-control form-control-sm" id="factor-cap-banc-overvoltage" name="factor-cap-banc-overvoltage" value="1.1" step="0.1" min="1.0" max="2.0" title="Fator para V> (Vteste > Vbanco)"></div></div>
                                        <div class="mt-auto pt-2"><button type="button" class="btn btn-primary w-100 btn-sm" id="calcular-perdas-carga">Calcular Perdas em Carga</button></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Coluna de Condições Nominais -->
                            <div class="col-lg-3 col-md-6 d-flex flex-column ps-lg-1 mt-2 mt-lg-0">
                                <div class="card h-100 card-sm">
                                    <div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">CONDIÇÕES NOMINAIS</h6></div>
                                    <div class="card-body p-1 results-placeholder" id="condicoes-nominais-card-body">
                                        <!-- Populated by JS -->
                                    </div>
                                </div>
                            </div>
                            <!-- Coluna de Status Geral -->
                            <div class="col-lg-6 col-md-6 d-flex flex-column ps-lg-1 mt-2 mt-lg-0">
                                <div class="card card-sm h-100">
                                    <div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">STATUS GERAL</h6></div>
                                    <div class="card-body p-2" id="status-geral-card-body">
                                        <!-- Populated by JS -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Seção LEGENDA E LIMITES - Nova linha abaixo dos cards principais -->
                        <div class="row g-2 mb-2">
                            <div class="col-12">
                                <div class="card card-sm">
                                    <div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">LEGENDA E LIMITES</h6></div>
                                    <div class="card-body p-1" id="legenda-status-card-body">
                                        <!-- Populated by JS -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="resultados-perdas-carga" class="flex-grow-1 overflow-auto results-placeholder">
                            <!-- JavaScript populates this section -->
                        </div> 
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/scripts/common_module.js"></script>
<script src="/scripts/api_persistence.js"></script>
<script type="module" src="/scripts/losses.js"></script>

</body>
</html>