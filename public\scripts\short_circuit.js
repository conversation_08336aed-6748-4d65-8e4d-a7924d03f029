// public/scripts/short_circuit.js - ATUALIZADO

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { collectFormData, fillFormWithData, waitForApiSystem } from './api_persistence.js';

// Função para carregar dados do store 'shortCircuit' e preencher o formulário
async function loadShortCircuitDataAndPopulateForm() {
    try {
        console.log('[short_circuit] Carregando dados do store "shortCircuit" e preenchendo formulário...');
        await waitForApiSystem(); // Garante que o sistema de persistência esteja pronto

        const store = window.apiDataSystem.getStore('shortCircuit');
        const data = await store.getData();

        if (data && data.formData) {
            const formElement = document.getElementById('short-circuit-form');
            if (formElement) {
                fillFormWithData(formElement, data.formData);
                console.log('[short_circuit] Formulário de curto-circuito preenchido com dados do store:', data.formData);
            } else {
                console.warn('[short_circuit] Formulário "short-circuit-form" não encontrado para preenchimento.');
            }
        } else {
            console.log('[short_circuit] Nenhum dado de curto-circuito encontrado no store.');
        }

        // Carregar e exibir resultados salvos se existirem
        if (data && data.calculationResults) {
            console.log('[short_circuit] Carregando resultados salvos:', data.calculationResults);
            displayCalculationResults(data.calculationResults);
        }
    } catch (error) {
        console.error('[short_circuit] Erro ao carregar e preencher dados de curto-circuito:', error);
    }
}

// Função auxiliar para obter valor de input
function getInputValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : null;
}

async function getTransformerFormData() {
    const store = window.apiDataSystem.getStore('transformerInputs');
    const transformerData = await store.getData();
    return transformerData.formData || {};
}

async function saveCalculationResults(results) {
    try {
        if (window.apiDataSystem && window.collectFormData) {
            const shortCircuitStore = window.apiDataSystem.getStore('shortCircuit');
            const currentStoreData = await shortCircuitStore.getData() || {};
            const newFormData = window.collectFormData(document.getElementById('short-circuit-form'));

            // Preservar dados existentes e adicionar novos resultados
            const updatedStoreData = {
                calculationResults: results,
                formData: { ...(currentStoreData.formData || {}), ...newFormData },
                timestamp: new Date().toISOString()
            };

            await shortCircuitStore.updateData(updatedStoreData);
            console.log('[short_circuit] Resultados salvos no store:', updatedStoreData);
        } else {
            console.warn('[short_circuit] Sistema de persistência ou collectFormData não disponível para salvar resultados');
        }
    } catch (error) {
        console.error('[short_circuit] Erro ao salvar resultados:', error);
    }
}

// Função para exibir resultados salvos
function displayCalculationResults(results) {
    try {
        // Verificar se os elementos existem antes de tentar definir valores
        if (results.iscSym && results.iscSym !== '-') {
            const iscSymElement = document.getElementById('isc-sym-result');
            if (iscSymElement) {
                iscSymElement.value = results.iscSym;
            } else {
                console.warn('[short_circuit] Elemento isc-sym-result não encontrado (pode ter sido ocultado)');
            }
        }

        if (results.iscPeak && results.iscPeak !== '-') {
            const iscPeakElement = document.getElementById('isc-peak-result');
            if (iscPeakElement) {
                iscPeakElement.value = results.iscPeak;
            } else {
                console.warn('[short_circuit] Elemento isc-peak-result não encontrado (pode ter sido ocultado)');
            }
        }

        if (results.deltaZ && results.deltaZ !== '-') {
            const deltaZElement = document.getElementById('delta-impedance-result');
            if (deltaZElement) {
                deltaZElement.value = results.deltaZ;
            } else {
                console.warn('[short_circuit] Elemento delta-impedance-result não encontrado (pode ter sido ocultado)');
            }
        }

        if (results.checkStatus && results.checkStatus !== '-') {
            const checkStatusElement = document.getElementById('impedance-check-status');
            if (checkStatusElement) {
                checkStatusElement.innerHTML = results.checkStatus;
            } else {
                console.warn('[short_circuit] Elemento impedance-check-status não encontrado (pode ter sido ocultado)');
            }
        }

        if (results.errorMessage) {
            const errorElement = document.getElementById('short-circuit-error-message');
            if (errorElement) {
                errorElement.textContent = results.errorMessage;
            } else {
                console.warn('[short_circuit] Elemento short-circuit-error-message não encontrado (pode ter sido ocultado)');
            }
        }

        console.log('[short_circuit] Resultados processados (elementos disponíveis foram atualizados)');
    } catch (error) {
        console.error('[short_circuit] Erro ao exibir resultados:', error);
    }
}

// Função para resetar resultados de cálculos de curto-circuito
function resetShortCircuitResults() {
    console.log('[short_circuit] resetShortCircuitResults: Resetando resultados de cálculos');

    try {
        // Limpar campos de resultado
        const resultFields = [
            'isc-sym-result',
            'isc-peak-result',
            'delta-impedance-result'
        ];

        resultFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = '-';
            }
        });

        // Limpar status de verificação
        const statusElement = document.getElementById('impedance-check-status');
        if (statusElement) {
            statusElement.innerHTML = '-';
        }

        // Limpar mensagem de erro
        const errorElement = document.getElementById('short-circuit-error-message');
        if (errorElement) {
            errorElement.textContent = '';
        }

        // Limpar gráfico de impedância
        const graphDiv = document.getElementById('impedance-variation-graph');
        if (graphDiv) {
            graphDiv.innerHTML = '<div class="text-muted text-center p-3">Nenhum cálculo realizado ainda.</div>';
            graphDiv.classList.add('plotly-graph-placeholder');
        }

        console.log('[short_circuit] Resultados de curto-circuito resetados');

    } catch (error) {
        console.error('[short_circuit] Erro ao resetar resultados:', error);
    }
}

function updateImpedanceGraph(Z_before, Z_after, limitZ, currentDeltaZ, powerCategory) {
    const graphDiv = document.getElementById('impedance-variation-graph');
    if (!graphDiv) return;

    graphDiv.innerHTML = '';
    graphDiv.classList.remove('plotly-graph-placeholder');

    // Determinar cor baseada no resultado
    const isApproved = Math.abs(currentDeltaZ) <= limitZ;
    const barColor = isApproved ? '#27ae60' : '#e74c3c';
    const statusText = isApproved ? 'APROVADO' : 'REPROVADO';
    const statusIcon = isApproved ? 'fa-check-circle' : 'fa-times-circle';

    // Informações das categorias
    const categoryInfo = {
        'I': { label: 'I (≤ 2.5 MVA)', limit: 7.5 },
        'II': { label: 'II (> 2.5 a 100 MVA)', limit: 5.0 },
        'III': { label: 'III (> 100 MVA)', limit: 2.0 }
    };

    const currentCategory = categoryInfo[powerCategory] || { label: 'Não selecionada', limit: 0 };

    let graphHtml = `
        <div style="width: 100%; height: 300px; display: flex; flex-direction: column; background-color: #2c3e50; border-radius: 8px; padding: 15px;">
            <!-- Cabeçalho com informações da categoria -->
            <div style="text-align: center; margin-bottom: 15px; color: #ecf0f1;">
                <h6 style="margin: 0; color: #ecf0f1;">Análise de Variação da Impedância</h6>
                <p style="margin: 5px 0; font-size: 0.9em; color: #bdc3c7;">
                    Categoria: <strong>${currentCategory.label}</strong> |
                    Limite: <strong>±${limitZ}%</strong> |
                    Status: <span style="color: ${barColor};"><i class="fas ${statusIcon}"></i> ${statusText}</span>
                </p>
            </div>

            <!-- Área do gráfico -->
            <div style="flex: 1; display: flex; align-items: flex-end; justify-content: center; position: relative; border-left: 2px solid #34495e; border-bottom: 2px solid #34495e; margin: 10px;">

                <!-- Linhas de limite -->
                <div style="position: absolute; top: ${50 - (limitZ / 10) * 50}%; width: 100%; border-top: 2px dashed #e74c3c; z-index: 1;">
                    <span style="position: absolute; right: 5px; top: -10px; font-size: 0.7em; color: #e74c3c; background: #2c3e50; padding: 0 3px;">+${limitZ}%</span>
                </div>
                <div style="position: absolute; top: ${50 + (limitZ / 10) * 50}%; width: 100%; border-top: 2px dashed #e74c3c; z-index: 1;">
                    <span style="position: absolute; right: 5px; top: -10px; font-size: 0.7em; color: #e74c3c; background: #2c3e50; padding: 0 3px;">-${limitZ}%</span>
                </div>

                <!-- Linha zero -->
                <div style="position: absolute; top: 50%; width: 100%; border-top: 1px solid #7f8c8d; z-index: 1;">
                    <span style="position: absolute; right: 5px; top: -8px; font-size: 0.7em; color: #7f8c8d; background: #2c3e50; padding: 0 3px;">0%</span>
                </div>

                <!-- Barras comparativas -->
                <div style="display: flex; align-items: flex-end; justify-content: space-around; width: 80%; height: 100%; position: relative; z-index: 2;">

                    <!-- Barra da variação medida -->
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="
                            height: ${Math.min(80, Math.abs(currentDeltaZ / limitZ) * 40)}%;
                            width: 60px;
                            background: linear-gradient(to top, ${barColor}, ${barColor}aa);
                            border-radius: 4px 4px 0 0;
                            position: relative;
                            border: 2px solid ${barColor};
                            ${currentDeltaZ < 0 ? 'transform: scaleY(-1); margin-top: auto;' : ''}
                        ">
                            <span style="
                                position: absolute;
                                ${currentDeltaZ < 0 ? 'top: -25px;' : 'bottom: -25px;'}
                                width: 100%;
                                text-align: center;
                                color: #ecf0f1;
                                font-weight: bold;
                                font-size: 0.8em;
                                ${currentDeltaZ < 0 ? 'transform: scaleY(-1);' : ''}
                            ">${currentDeltaZ.toFixed(2)}%</span>
                        </div>
                        <div style="margin-top: 10px; color: #ecf0f1; font-size: 0.8em; text-align: center;">
                            <strong>ΔZ Medido</strong><br>
                            <small>${Z_before.toFixed(2)}% → ${Z_after.toFixed(2)}%</small>
                        </div>
                    </div>

                    <!-- Barras dos limites (positivo e negativo) -->
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="
                            height: ${(limitZ / 10) * 40}%;
                            width: 40px;
                            background: linear-gradient(to top, #e74c3c, #e74c3caa);
                            border-radius: 4px 4px 0 0;
                            border: 2px solid #e74c3c;
                            margin-bottom: 5px;
                        "></div>
                        <div style="
                            height: ${(limitZ / 10) * 40}%;
                            width: 40px;
                            background: linear-gradient(to bottom, #e74c3c, #e74c3caa);
                            border-radius: 0 0 4px 4px;
                            border: 2px solid #e74c3c;
                            transform: scaleY(-1);
                        "></div>
                        <div style="margin-top: 10px; color: #ecf0f1; font-size: 0.8em; text-align: center;">
                            <strong>Limites</strong><br>
                            <small>±${limitZ}%</small>
                        </div>
                    </div>
                </div>

                <!-- Eixos -->
                <div style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); color: #bdc3c7; font-size: 0.8em;">
                    Variação da Impedância (%)
                </div>
                <div style="position: absolute; left: -40px; top: 50%; transform: translateY(-50%) rotate(-90deg); color: #bdc3c7; font-size: 0.8em;">
                    Amplitude
                </div>
            </div>

            <!-- Legenda das categorias -->
            <div style="margin-top: 15px; padding: 10px; background-color: #34495e; border-radius: 5px;">
                <div style="color: #ecf0f1; font-size: 0.8em; text-align: center;">
                    <strong>Limites por Categoria (NBR 5356-5 / IEC 60076-5):</strong><br>
                    <span style="color: #3498db;">Categoria I (≤ 2.5 MVA): ±7.5%</span> |
                    <span style="color: #f39c12;">Categoria II (> 2.5 a 100 MVA): ±5.0%</span> |
                    <span style="color: #e74c3c;">Categoria III (> 100 MVA): ±2.0%</span>
                </div>
            </div>
        </div>
    `;

    graphDiv.innerHTML = graphHtml;
}

// Lógica para o botão "Calcular / Verificar"
async function setupCalcButton() {
    const calcBtn = document.getElementById('calc-short-circuit-btn');
    if (calcBtn) {
        calcBtn.addEventListener('click', async function() {
            console.log('Botão Calcular / Verificar Curto-Circuito clicado!');

            const impedanceBefore = parseFloat(getInputValue('impedance-before'));
            const impedanceAfter = parseFloat(getInputValue('impedance-after'));
            const peakFactor = parseFloat(getInputValue('peak-factor'));
            const iscSide = getInputValue('isc-side');
            const powerCategory = getInputValue('power-category');
            const materialEnrolamento = getInputValue('material-enrolamento');

            const transformerData = await getTransformerFormData();
            const basicData = transformerData || {}; // formData já é o objeto de dados básicos
            const atData = transformerData || {}; // Os dados de AT estão no mesmo objeto
            const btData = transformerData || {}; // Os dados de BT estão no mesmo objeto
            const terciarioData = transformerData || {}; // Os dados de Terciário estão no mesmo objeto

            let errorMessage = '';
            let iscSym = '-';
            let iscPeak = '-';
            let deltaZ = '-';
            let checkStatus = '-';
            let limitDeltaZ = 0;
            let percentDeltaZ = 0;

            try {
                if (isNaN(impedanceBefore) || isNaN(impedanceAfter) || isNaN(peakFactor)) {
                    throw new Error("Preencha todos os campos de impedância e fator de pico.");
                }

                if (!iscSide || !powerCategory || !materialEnrolamento) {
                    throw new Error("Preencha todos os campos obrigatórios: Lado Cálculo Isc, Categoria e Material Enrolamento.");
                }

                if (!basicData.potencia_mva || !basicData.frequencia || !atData.tensao_at || !btData.tensao_bt) {
                    throw new Error("Dados básicos do transformador (potência, tensões) são necessários. Verifique a página 'Dados Básicos'.");
                }

                switch(powerCategory) {
                    case 'I': limitDeltaZ = 7.5; break;
                    case 'II': limitDeltaZ = 5.0; break;
                    case 'III': limitDeltaZ = 2.0; break;
                    default: errorMessage = "Selecione uma categoria de potência."; return;
                }

                const diffZ = Math.abs(impedanceAfter - impedanceBefore);
                percentDeltaZ = (diffZ / impedanceBefore) * 100;
                deltaZ = percentDeltaZ.toFixed(2) + ' %';

                if (percentDeltaZ <= limitDeltaZ) {
                    checkStatus = '<span style="color: var(--success-color); font-weight: bold;"><i class="fas fa-check-circle me-1"></i>APROVADO</span>';
                } else {
                    checkStatus = `<span style="color: var(--danger-color); font-weight: bold;"><i class="fas fa-times-circle me-1"></i>REPROVADO (Limite: ±${limitDeltaZ}%)</span>`;
                }

                const Sn_MVA = basicData.potencia_mva;
                const Zn_percent = atData.impedancia;

                if (isNaN(Sn_MVA) || isNaN(Zn_percent) || Zn_percent === 0) {
                     throw new Error("Potência nominal e impedância nominal do transformador são inválidas. Verifique 'Dados Básicos'.");
                }

                let nominalVoltagekV = 0;
                let nominalCurrentA = 0;
                if (iscSide === 'AT') {
                    nominalVoltagekV = atData.tensao_at;
                    nominalCurrentA = Sn_MVA * 1000 / (basicData.tipo_transformador === 'Trifásico' ? Math.sqrt(3) : 1) / nominalVoltagekV;
                } else if (iscSide === 'BT') {
                    nominalVoltagekV = btData.tensao_bt;
                    nominalCurrentA = Sn_MVA * 1000 / (basicData.tipo_transformador === 'Trifásico' ? Math.sqrt(3) : 1) / nominalVoltagekV;
                } else if (iscSide === 'TERCIARIO') {
                    nominalVoltagekV = terciarioData.tensao_terciario;
                    nominalCurrentA = Sn_MVA * 1000 / (basicData.tipo_transformador === 'Trifásico' ? Math.sqrt(3) : 1) / nominalVoltagekV;
                }
                if (isNaN(nominalVoltagekV) || nominalVoltagekV === 0) {
                    throw new Error(`Tensão nominal para o lado ${iscSide} inválida.`);
                }

                const Isc = nominalCurrentA / (Zn_percent / 100);
                iscSym = (Isc / 1000).toFixed(2);
                iscPeak = (Isc / 1000 * peakFactor).toFixed(2);

            } catch (error) {
                errorMessage = error.message;
            }

            document.getElementById('isc-sym-result').value = iscSym;
            document.getElementById('isc-peak-result').value = iscPeak;
            document.getElementById('delta-impedance-result').value = deltaZ;
            document.getElementById('impedance-check-status').innerHTML = checkStatus;
            document.getElementById('short-circuit-error-message').textContent = errorMessage;

            await saveCalculationResults({ iscSym, iscPeak, deltaZ, checkStatus, errorMessage });
            updateImpedanceGraph(impedanceBefore, impedanceAfter, limitDeltaZ, percentDeltaZ, powerCategory);
        });
    }
}

// Função para limpar valores padrão indesejados
function clearUnwantedDefaultValues() {
    // Limpar dropdown de lado de cálculo se estiver com valor padrão
    const iscSideSelect = document.getElementById('isc-side');
    if (iscSideSelect && iscSideSelect.value === 'AT') {
        iscSideSelect.value = '';
    }

    // Limpar campo de fator pico se estiver com valor padrão
    const peakFactorInput = document.getElementById('peak-factor');
    if (peakFactorInput && peakFactorInput.value === '2.55') {
        peakFactorInput.value = '';
    }

    console.log('[short_circuit] Valores padrão indesejados removidos');
}

// Função para garantir persistência do dropdown Material Enrolamento
function ensureWindingMaterialPersistence() {
    const materialSelect = document.getElementById('material-enrolamento');
    if (materialSelect) {
        // Adicionar listener para salvar quando valor muda
        materialSelect.addEventListener('change', async function() {
            console.log('[short_circuit] Material enrolamento alterado para:', this.value);

            // Salvar imediatamente no store
            if (window.apiDataSystem && window.collectFormData) {
                try {
                    const shortCircuitStore = window.apiDataSystem.getStore('shortCircuit');
                    const currentData = await shortCircuitStore.getData() || {};
                    const formData = window.collectFormData(document.getElementById('short-circuit-form'));

                    const updatedData = {
                        ...currentData,
                        formData: { ...(currentData.formData || {}), ...formData },
                        timestamp: new Date().toISOString()
                    };

                    await shortCircuitStore.updateData(updatedData);
                    console.log('[short_circuit] Material enrolamento salvo:', this.value);
                } catch (error) {
                    console.error('[short_circuit] Erro ao salvar material enrolamento:', error);
                }
            } else {
                console.warn('[short_circuit] Sistema de persistência ou collectFormData não disponível');
            }
        });

        console.log('[short_circuit] Persistência do material enrolamento configurada');
    }
}

// Função de inicialização do módulo Curto-Circuito
async function initShortCircuit() {
    console.log('Módulo Curto-Circuito carregado e pronto para interatividade.');

    if (window.setupApiFormPersistence) {
        await window.setupApiFormPersistence('short-circuit-form', 'shortCircuit');
    } else {
        console.warn('[short_circuit] Sistema de persistência não disponível');
    }

    // Carregar e preencher os dados do próprio módulo de curto-circuito
    await loadShortCircuitDataAndPopulateForm();

    // Configurar persistência do dropdown Material Enrolamento
    ensureWindingMaterialPersistence();

    // Limpar valores padrão indesejados apenas se não há dados salvos
    const store = window.apiDataSystem.getStore('shortCircuit');
    const existingData = await store.getData();
    if (!existingData || !existingData.formData || Object.keys(existingData.formData).length === 0) {
        clearUnwantedDefaultValues();
        console.log('[short_circuit] Valores padrão limpos (sem dados salvos)');
    } else {
        console.log('[short_circuit] Dados salvos encontrados, mantendo valores');
    }

    // Adicionar listener para o evento transformerDataUpdated
    document.addEventListener('transformerDataUpdated', async (event) => {
        console.log('[short_circuit] Evento transformerDataUpdated recebido:', event.detail);

        // REMOVIDO resetShortCircuitResults() - já é feito pelo sistema de ocultação em common_module.js
        // resetShortCircuitResults();

        // Recarrega os dados do próprio módulo para garantir consistência (incluindo resultados salvos)
        await loadShortCircuitDataAndPopulateForm();

        // Recarregar o painel de informações do transformador
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);

        console.log('[short_circuit] Dados do transformador atualizados - dados recarregados');
    });

    const transformerInfoPlaceholderId = 'transformer-info-short_circuit-page';

    // Verificar se o elemento existe antes de tentar carregar o painel
    const placeholderElement = document.getElementById(transformerInfoPlaceholderId);
    if (placeholderElement) {
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);
    } else {
        console.warn(`[short_circuit] Elemento ${transformerInfoPlaceholderId} não encontrado - módulo pode não estar ativo`);
    }

    setupCalcButton();
}

// SPA routing: executa quando o módulo short_circuit é carregado
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'short_circuit') {
        console.log('[short_circuit] SPA routing init');
        initShortCircuit();
    }
});

// Fallback para carregamento direto da página (se não for SPA)
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('short-circuit-form')) { // Assumindo um ID de formulário principal para o módulo
        console.log('[short_circuit] DOMContentLoaded init (fallback)');
        initShortCircuit();
    }
});