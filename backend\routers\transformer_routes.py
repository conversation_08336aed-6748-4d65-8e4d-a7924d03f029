# backend/routers/transformer_routes.py
import sys
import pathlib
from datetime import datetime
from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any, Optional, Union
from pydantic import BaseModel, field_validator
from ..utils.helpers import setup_path_for_imports

setup_path_for_imports()

try:
    from ..mcp import MCPDataManager
    from ..services import transformer_service
    from ..services import losses_service
    from ..services import impulse_service
    from ..services import applied_voltage_service
    from ..services import induced_voltage_service
    from ..services import short_circuit_service
    from ..services import temperature_service
    from ..services import dielectric_service
    from ..reports import pdf_generator
    from ..utils.constants import VECTOR_GROUPS, COMMON_VECTOR_GROUPS, get_vector_groups_by_type, TRANSFORMER_TYPE_OPTIONS, get_eps_limits, get_sut_limits, determine_sut_eps_type, get_all_sut_taps_with_limits
except ImportError as e:
    print(f"Erro ao importar módulos em transformer_routes: {e}")
    sys.exit(1) # Saia se as importações essenciais falharem
# Placeholder para a instância do MCP (será definida por main.py)
mcp_data_manager = None

router = APIRouter(prefix="/api/transformer", tags=["transformer"])

@router.get("/types")
async def get_transformer_types():
    """
    Returns the available transformer types.
    """
    return {
        "transformer_types": TRANSFORMER_TYPE_OPTIONS
    }

@router.get("/vector-groups")
async def get_vector_groups():
    """
    Returns the complete list of vector groups organized by transformer type.
    """
    return {
        "vector_groups": VECTOR_GROUPS,
        "common_groups": COMMON_VECTOR_GROUPS
    }

@router.get("/vector-groups/{transformer_type}")
async def get_vector_groups_by_transformer_type(transformer_type: str):
    """
    Returns a flattened list of vector groups for a specific transformer type.

    Args:
        transformer_type: "Trifásico", "Autotransformador", or "Monofásico"
    """
    # Map frontend values to backend keys
    type_mapping = {
        "trifasico": "Trifásico",
        "monofasico": "Monofásico",
        "autotransformador": "Autotransformador",
        "Trifásico": "Trifásico",
        "Monofásico": "Monofásico",
        "Autotransformador": "Autotransformador",
        "Autotransformador Monofásico": "Autotransformador Monofásico"
    }

    mapped_type = type_mapping.get(transformer_type, transformer_type)
    groups = get_vector_groups_by_type(mapped_type)
    common_groups = COMMON_VECTOR_GROUPS.get(mapped_type, [])

    if not groups:
        raise HTTPException(status_code=404, detail=f"Tipo de transformador '{transformer_type}' não encontrado")

    return {
        "transformer_type": mapped_type,
        "vector_groups": groups,
        "common_groups": common_groups
    }

@router.post("/sut-eps-limits")
async def get_sut_eps_limits_for_transformer(transformer_data: dict):
    """
    Retorna limites SUT/EPS baseados na configuração do transformador.

    Args:
        transformer_data: Dados do transformador (tipo_transformador, grupo_ligacao, etc.)

    Returns:
        Dict com tipo SUT/EPS determinado e respectivos limites
    """
    try:
        sut_eps_type = determine_sut_eps_type(transformer_data)
        eps_limits = get_eps_limits(transformer_data)
        sut_limits_at_reference = get_sut_limits(transformer_data, sut_winding="AT")  # AT referência
        sut_limits_terc_reference = get_sut_limits(transformer_data, sut_winding="TERC")  # TERC referência
        all_sut_taps = get_all_sut_taps_with_limits(transformer_data)

        return {
            "sut_eps_type": sut_eps_type,
            "eps_limits": eps_limits,
            "sut_limits_at_reference": sut_limits_at_reference,
            "sut_limits_terc_reference": sut_limits_terc_reference,
            "all_sut_taps": all_sut_taps,
            "transformer_data_used": {
                "tipo_transformador": transformer_data.get("tipo_transformador", ""),
                "grupo_ligacao": transformer_data.get("grupo_ligacao", "")
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Erro ao determinar limites SUT/EPS: {str(e)}")

class TransformerInputsData(BaseModel):
    # Campos do formulário transformer_inputs.html
    potencia_mva: Optional[Union[float, str]] = None
    frequencia: Optional[Union[float, str]] = None
    tipo_transformador: Optional[str] = None
    grupo_ligacao: Optional[str] = None
    liquido_isolante: Optional[str] = None
    tipo_isolamento: Optional[str] = None
    norma_iso: Optional[str] = None
    elevacao_oleo_topo: Optional[Union[float, str]] = None
    elevacao_enrol: Optional[Union[float, str]] = None
    peso_parte_ativa: Optional[Union[float, str]] = None
    peso_tanque_acessorios: Optional[Union[float, str]] = None
    peso_oleo: Optional[Union[float, str]] = None
    peso_total: Optional[Union[float, str]] = None
    peso_adicional: Optional[Union[float, str]] = None
    tensao_at: Optional[Union[float, str]] = None
    classe_tensao_at: Optional[Union[float, str]] = None
    impedancia: Optional[Union[float, str]] = None
    nbi_at: Optional[Union[float, str]] = None
    sil_at: Optional[Union[float, str]] = None
    conexao_at: Optional[str] = None
    tensao_bucha_neutro_at: Optional[Union[float, str]] = None
    nbi_neutro_at: Optional[Union[float, str]] = None
    sil_neutro_at: Optional[Union[float, str]] = None
    tensao_at_tap_maior: Optional[Union[float, str]] = None
    tensao_at_tap_menor: Optional[Union[float, str]] = None
    impedancia_tap_maior: Optional[Union[float, str]] = None
    impedancia_tap_menor: Optional[Union[float, str]] = None
    teste_tensao_aplicada_at: Optional[Union[float, str]] = None
    teste_tensao_induzida_at: Optional[Union[float, str]] = None
    tensao_bt: Optional[Union[float, str]] = None
    classe_tensao_bt: Optional[Union[float, str]] = None
    nbi_bt: Optional[Union[float, str]] = None
    sil_bt: Optional[Union[float, str]] = None
    conexao_bt: Optional[str] = None
    tensao_bucha_neutro_bt: Optional[Union[float, str]] = None
    nbi_neutro_bt: Optional[Union[float, str]] = None
    sil_neutro_bt: Optional[Union[float, str]] = None
    teste_tensao_aplicada_bt: Optional[Union[float, str]] = None
    tensao_terciario: Optional[Union[float, str]] = None
    potencia_terciario_mva: Optional[Union[float, str]] = None
    classe_tensao_terciario: Optional[Union[float, str]] = None
    nbi_terciario: Optional[Union[float, str]] = None
    sil_terciario: Optional[Union[float, str]] = None
    conexao_terciario: Optional[str] = None
    tensao_bucha_neutro_terciario: Optional[Union[float, str]] = None
    nbi_neutro_terciario: Optional[Union[float, str]] = None
    sil_neutro_terciario: Optional[Union[float, str]] = None
    teste_tensao_aplicada_terciario: Optional[Union[float, str]] = None

@router.post("/inputs")
async def update_transformer_inputs(data: TransformerInputsData = Body(...)):
    """
    Recebe os dados de entrada do formulário do transformador,
    calcula os valores derivados e os persiste no store 'transformer_inputs'.
    """
    try:
        # Converte o Pydantic Model para um dicionário
        input_data_dict = data.model_dump(exclude_unset=True)
        print(f"[DEBUG] Dados recebidos: {input_data_dict}")

        # Calcula os dados derivados (correntes nominais, etc.)
        calculated_data = transformer_service.calculate_and_process_transformer_data(input_data_dict)

        # Combina os dados de entrada com os dados calculados
        final_data = {**input_data_dict, **calculated_data}

        # Persiste os dados combinados no store 'transformer_inputs'
        if mcp_data_manager is None:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        success = mcp_data_manager.patch_data('transformerInputs', {"formData": final_data})

        if success:
            return {
                "status": "success",
                "message": "Dados do transformador atualizados e calculados com sucesso.",
                "updated_data": final_data
            }
        else:
            raise HTTPException(status_code=500, detail="Falha ao persistir dados do transformador.")
    except Exception as e:
        import traceback
        print(f"[ERROR] Erro completo: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Erro ao processar dados do transformador: {str(e)}")

@router.post("/propagate")
async def trigger_propagation():
    """
    Dispara propagação manual para todos os módulos dependentes
    """
    try:
        if mcp_data_manager is None:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        # Habilita propagação temporariamente
        mcp_data_manager.enable_auto_propagation()

        # Dispara propagação manual para transformerInputs
        mcp_data_manager._propagate_changes('transformerInputs')

        # Desabilita novamente
        mcp_data_manager.disable_auto_propagation()

        return {"status": "success", "message": "Propagação executada com sucesso"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao executar propagação: {str(e)}")

@router.post("/propagation/enable")
async def enable_propagation():
    """Habilita propagação automática"""
    if mcp_data_manager is None:
        raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

    mcp_data_manager.enable_auto_propagation()
    return {"status": "success", "message": "Propagação automática habilitada"}

@router.post("/propagation/disable")
async def disable_propagation():
    """Desabilita propagação automática"""
    if mcp_data_manager is None:
        raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

    mcp_data_manager.disable_auto_propagation()
    return {"status": "success", "message": "Propagação automática desabilitada"}

# Rotas para processamento de módulos específicos conforme arquitetura TTS
@router.post("/modules/{module_id}/process")
async def process_module_data(module_id: str, data: Dict[str, Any] = Body(...)):
    """
    Processa dados específicos de um módulo.
    Arquitetura TTS: Dados Básicos + Inputs Específicos → Services → MCP
    """
    try:
        # Valida módulos ativos conforme especificação
        valid_modules = ['losses', 'impulse', 'appliedVoltage', 'inducedVoltage',
                        'shortCircuit', 'temperatureRise', 'dielectricAnalysis']

        if module_id not in valid_modules:
            raise HTTPException(status_code=404, detail=f"Módulo '{module_id}' não encontrado")

        # Extrai dados básicos e dados específicos do módulo
        basic_data = data.get('basicData', {})
        module_data = data.get('moduleData', {})

        # Se basicData está vazio, obtém automaticamente do store transformerInputs
        if not basic_data and mcp_data_manager:
            transformer_data = mcp_data_manager.get_data('transformerInputs')
            if transformer_data:
                basic_data = transformer_data.get('formData', {})
                print(f"[DEBUG] Dados básicos obtidos automaticamente do store: {basic_data}")

        # Chama service específico com base no module_id
        processed_data = {}
        if module_id == 'losses':
            # Verificar se há uma operação específica
            operation = data.get('operation')
            if operation == 'no_load_losses':
                input_data = data.get('data', {})
                processed_data = losses_service.calculate_no_load_losses(input_data)
                # Salvar apenas os inputs no MCP
                if mcp_data_manager:
                    mcp_data_manager.patch_data(f"{module_id}-inputs", {
                        "no_load_inputs": input_data,
                        "timestamp": datetime.now().isoformat()
                    })
            elif operation == 'load_losses':
                input_data = data.get('data', {})
                processed_data = losses_service.calculate_load_losses(input_data)

                # Serialize the results for enhanced storage (simplified version)
                if mcp_data_manager:
                    try:
                        from backend.services.losses_service import serialize_losses_results_safe
                        serialized_data = serialize_losses_results_safe(processed_data, input_data)
                        if serialized_data:
                            processed_data["_serialized"] = serialized_data
                    except Exception as e:
                        print(f"Warning: Could not serialize losses data: {e}")

                # Salvar apenas os inputs no MCP - SOMENTE INPUTS
                load_inputs_only = {
                    "temperatura_referencia": input_data.get("temperatura_referencia"),
                    "perdas_carga_kw_u_min": input_data.get("perdas_carga_kw_u_min"),
                    "perdas_carga_kw_u_nom": input_data.get("perdas_carga_kw_u_nom"),
                    "perdas_carga_kw_u_max": input_data.get("perdas_carga_kw_u_max")
                }
                if mcp_data_manager:
                    mcp_data_manager.patch_data(f"{module_id}-inputs", {
                        "load_inputs": load_inputs_only,
                        "timestamp": datetime.now().isoformat()
                    })
            else:
                raise HTTPException(status_code=400, detail="Operação inválida para perdas")
        elif module_id == 'impulse':
            # Função correta baseada no código do impulse_service
            combined_data = {**basic_data, **module_data}
            processed_data = impulse_service.calculate_impulse_test(combined_data)
        elif module_id == 'appliedVoltage':
            # Função correta baseada no código do applied_voltage_service
            combined_data = {**basic_data, **module_data}
            processed_data = applied_voltage_service.calculate_applied_voltage_test(combined_data)

            # Serialize the results for enhanced storage (disabled temporarily)
            # if mcp_data_manager:
            #     try:
            #         serialized_data = mcp_data_manager.serialize_module_data(
            #             "appliedVoltage", processed_data, combined_data
            #         )
            #         if serialized_data:
            #             processed_data["_serialized"] = serialized_data
            #     except Exception as e:
            #         print(f"Warning: Could not serialize applied voltage data: {e}")
        elif module_id == 'inducedVoltage':
            # Função correta baseada no código do induced_voltage_service
            # Obter dados de perdas necessários para tensão induzida
            try:
                losses_data = mcp_data_manager.get_data('losses') if mcp_data_manager else {}
            except Exception as e:
                print(f"[DEBUG] Erro ao obter dados de perdas: {e}")
                losses_data = {}

            combined_data = {**basic_data, **module_data, 'losses_data': losses_data}
            processed_data = induced_voltage_service.calculate_induced_voltage_test(combined_data)

            # Serialize the results for enhanced storage (simplified version)
            if mcp_data_manager:
                try:
                    from backend.services.induced_voltage_service import serialize_induced_voltage_results_safe
                    serialized_data = serialize_induced_voltage_results_safe(processed_data, combined_data)
                    if serialized_data:
                        processed_data["_serialized"] = serialized_data
                except Exception as e:
                    print(f"Warning: Could not serialize induced voltage data: {e}")
        elif module_id == 'shortCircuit':
            # Função correta baseada no código do short_circuit_service
            combined_data = {**basic_data, **module_data}
            processed_data = short_circuit_service.calculate_short_circuit_analysis(combined_data)
        elif module_id == 'temperatureRise':
            # Função correta baseada no código do temperature_service
            combined_data = {**basic_data, **module_data}
            processed_data = temperature_service.calculate_temperature_analysis(combined_data)
        elif module_id == 'dielectricAnalysis':
            # Função correta baseada no código do dielectric_service
            processed_data = dielectric_service.analyze_dielectric(basic_data, module_data)
        else:
            # Isso não deve acontecer devido à validação de valid_modules, mas é um fallback
            raise HTTPException(status_code=500, detail=f"Serviço para o módulo '{module_id}' não implementado.")

        # Armazena no MCP
        if mcp_data_manager is None:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        store_data = {
            'inputs': module_data,
            'basicData': basic_data,
            'results': processed_data,
            'lastUpdated': str(pathlib.Path(__file__).stat().st_mtime)  # timestamp simples
        }

        success = mcp_data_manager.patch_data(module_id, store_data)

        if not success:
            raise HTTPException(status_code=500, detail=f"Erro ao armazenar dados do módulo {module_id}")

        return {
            'success': True,
            'module': module_id,
            'results': processed_data,
            'message': f'Dados do módulo {module_id} processados com sucesso'
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")

@router.post("/global-update")
async def trigger_global_update(data: Dict[str, Any] = Body(...)):
    """
    Dispara atualização global do MCP em ciclo estruturado.
    Arquitetura TTS: Dados Básicos → Propagam para todos os módulos → MCP atualizado
    """
    try:
        if mcp_data_manager is None:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        triggered_by = data.get('triggeredBy', 'unknown')

        # 1. Obtém dados básicos (fonte da verdade)
        basic_data = mcp_data_manager.get_data('transformerInputs')
        if not basic_data:
            basic_data = {}

        basic_form_data = basic_data.get('formData', {})

        # 2. Módulos ativos conforme especificação
        active_modules = ['losses', 'impulse', 'appliedVoltage', 'inducedVoltage',
                         'shortCircuit', 'temperatureRise', 'dielectricAnalysis']

        # 3. Ciclo estruturado de atualização
        update_results = {}

        for module_id in active_modules:
            try:
                # Obtém dados específicos do módulo
                module_data = mcp_data_manager.get_data(module_id)
                module_inputs = module_data.get('inputs', {}) if module_data else {}

                # Atualiza store do módulo com dados básicos propagados
                updated_store_data = {
                    'inputs': module_inputs,
                    'basicData': basic_form_data,  # Propagação dos dados básicos
                    'results': module_data.get('results', {}) if module_data else {},
                    'lastGlobalUpdate': str(pathlib.Path(__file__).stat().st_mtime)
                }

                success = mcp_data_manager.patch_data(module_id, updated_store_data)

                update_results[module_id] = {
                    'status': 'updated' if success else 'error',
                    'message': 'Dados básicos propagados' if success else 'Erro ao atualizar'
                }

            except Exception as e:
                update_results[module_id] = {
                    'status': 'error',
                    'message': f'Erro: {str(e)}'
                }

        return {
            'success': True,
            'triggeredBy': triggered_by,
            'modulesUpdated': len([r for r in update_results.values() if r['status'] == 'updated']),
            'results': update_results,
            'message': 'Atualização global concluída'
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro na atualização global: {str(e)}")


@router.post("/modules/inducedVoltage/frequency-analysis")
async def generate_frequency_analysis(data: dict):
    """
    Gera tabela de análise de frequências para tensão induzida.
    Igual ao SCRATCH.py - calcula resultados para frequências 100, 120, 150, 180, 200, 240 Hz.
    """
    try:
        from ..services.induced_voltage_service import generate_frequency_analysis_table

        print(f"[DEBUG] Gerando tabela de análise de frequências: {data}")

        # Gerar tabela de frequências
        frequency_table = generate_frequency_analysis_table(data)

        return {
            "message": "Tabela de análise de frequências gerada com sucesso",
            "frequency_analysis": frequency_table
        }

    except Exception as e:
        print(f"[DEBUG] Erro na geração da tabela de frequências: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


@router.post("/generate-report")
async def generate_pdf_report(request_data: Dict[str, Any] = Body(...)):
    """
    Gera um relatório PDF com os dados dos módulos selecionados.

    Espera um payload com:
    - selected_modules: lista dos módulos a incluir no relatório
    """
    try:
        if not mcp_data_manager:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        # Extrair módulos selecionados e título
        selected_modules = request_data.get('selected_modules', [])
        report_title = request_data.get('report_title', 'Relatório de Simulação de Testes de Transformadores')

        if not selected_modules:
            raise HTTPException(status_code=400, detail="Nenhum módulo selecionado para o relatório")

        print(f"[generate_pdf_report] Módulos selecionados: {selected_modules}")
        print(f"[generate_pdf_report] Título do relatório: {report_title}")

        # Coletar dados de todos os módulos selecionados
        modules_data = {}

        for module_name in selected_modules:
            try:
                module_data = mcp_data_manager.get_data(module_name)
                print(f"[generate_pdf_report] Dados do módulo {module_name}: {type(module_data)} - {len(str(module_data)) if module_data else 0} chars")

                if module_data:
                    modules_data[module_name] = module_data
                    # Log das chaves principais para debug
                    if isinstance(module_data, dict):
                        print(f"[generate_pdf_report] Chaves do módulo {module_name}: {list(module_data.keys())}")
                else:
                    print(f"[generate_pdf_report] Aviso: Módulo {module_name} não possui dados")
            except Exception as e:
                print(f"[generate_pdf_report] Erro ao obter dados do módulo {module_name}: {e}")
                # Continua com outros módulos mesmo se um falhar

        if not modules_data:
            raise HTTPException(status_code=400, detail="Nenhum dado encontrado nos módulos selecionados")

        # Gerar PDF
        pdf_buffer = pdf_generator.generate_pdf_report(modules_data, selected_modules, report_title)

        # Preparar resposta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"relatorio_tts_{timestamp}.pdf"

        # Retornar PDF como bytes
        from fastapi.responses import Response

        return Response(
            content=pdf_buffer.getvalue(),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/pdf"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[generate_pdf_report] Erro inesperado: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao gerar relatório: {str(e)}")


@router.post("/download-excel")
async def download_excel_data(request_data: dict):
    """
    Gera e retorna um arquivo Excel com os dados dos módulos selecionados.

    Espera um payload com:
    - selected_modules: lista dos módulos a incluir no arquivo Excel
    """
    try:
        if not mcp_data_manager:
            raise HTTPException(status_code=500, detail="Sistema de dados não inicializado")

        # Extrair módulos selecionados
        selected_modules = request_data.get('selected_modules', [])

        if not selected_modules:
            raise HTTPException(status_code=400, detail="Nenhum módulo selecionado para o download")

        print(f"[download_excel_data] Módulos selecionados: {selected_modules}")

        # Coletar dados de todos os módulos selecionados
        modules_data = {}
        for module_name in selected_modules:
            try:
                module_data = mcp_data_manager.get_data(module_name)
                if module_data:
                    modules_data[module_name] = module_data
                    print(f"[download_excel_data] Dados coletados para {module_name}: {len(str(module_data))} caracteres")
                else:
                    print(f"[download_excel_data] Nenhum dado encontrado para {module_name}")
            except Exception as e:
                print(f"[download_excel_data] Erro ao coletar dados de {module_name}: {e}")
                continue

        if not modules_data:
            raise HTTPException(status_code=400, detail="Nenhum dado encontrado nos módulos selecionados")

        # Gerar Excel com funcionalidade aprimorada
        try:
            from backend.reports.excel_generator import generate_enhanced_excel_report
            excel_buffer = generate_enhanced_excel_report(modules_data, selected_modules)
            print(f"[download_excel_data] Usando gerador Excel aprimorado com serialização")
        except ImportError:
            # Fallback para gerador original
            from backend.reports.excel_generator import generate_excel_report
            excel_buffer = generate_excel_report(modules_data, selected_modules)
            print(f"[download_excel_data] Usando gerador Excel original (fallback)")

        # Preparar resposta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dados_tts_{timestamp}.xlsx"

        # Retornar Excel como bytes
        from fastapi.responses import Response

        return Response(
            content=excel_buffer.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[download_excel_data] Erro inesperado: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao gerar arquivo Excel: {str(e)}")


@router.post("/history/download-excel")
async def download_history_excel(request_data: dict):
    """
    Gera e retorna um arquivo Excel aprimorado com dados do histórico.
    Requer senha 754210 para acesso.

    Espera um payload com:
    - password: senha de acesso (754210)
    - session_ids: lista opcional de IDs de sessão específicas
    """
    try:
        if not mcp_data_manager:
            raise HTTPException(status_code=500, detail="Gerenciador de dados não disponível")

        # Verificar senha
        password = request_data.get('password', '')
        if password != "754210":
            raise HTTPException(status_code=403, detail="Senha incorreta")

        # Obter IDs de sessão específicas ou todas
        session_ids = request_data.get('session_ids', [])

        # Carregar dados das sessões
        if session_ids:
            sessions_data = []
            for session_id in session_ids:
                try:
                    session_data = mcp_data_manager.load_session(session_id)
                    if session_data:
                        sessions_data.append({
                            'session_id': session_id,
                            'session_name': session_data.get('session_name', session_id),
                            'description': session_data.get('description', ''),
                            'stores': session_data.get('stores', {}),
                            'session_info': {
                                'created_at': session_data.get('timestamp', ''),
                                'updated_at': session_data.get('timestamp', '')
                            }
                        })
                except Exception as e:
                    print(f"[download_history_excel] Erro ao carregar sessão {session_id}: {e}")
                    continue
        else:
            # Carregar todas as sessões
            all_sessions = mcp_data_manager.list_sessions()
            sessions_data = []

            for session_info in all_sessions:
                try:
                    session_id = session_info['session_id']
                    session_data = mcp_data_manager.load_session(session_id)
                    if session_data:
                        sessions_data.append({
                            'session_id': session_id,
                            'session_name': session_info.get('session_name', session_id),
                            'description': session_info.get('description', ''),
                            'stores': session_data.get('stores', {}),
                            'session_info': {
                                'created_at': session_info.get('created_at', ''),
                                'updated_at': session_info.get('updated_at', '')
                            }
                        })
                except Exception as e:
                    print(f"[download_history_excel] Erro ao carregar sessão {session_id}: {e}")
                    continue

        if not sessions_data:
            raise HTTPException(status_code=404, detail="Nenhuma sessão encontrada")

        # Gerar Excel aprimorado
        try:
            from backend.reports.history_excel_generator import generate_enhanced_history_excel
            excel_buffer = generate_enhanced_history_excel(sessions_data, password)
            print(f"[download_history_excel] Usando gerador de histórico aprimorado com {len(sessions_data)} sessões")
        except ImportError:
            # Fallback para gerador original
            from backend.reports.history_excel_generator import generate_history_excel
            excel_buffer = generate_history_excel(sessions_data, password)
            print(f"[download_history_excel] Usando gerador de histórico original (fallback)")

        # Preparar resposta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"historico_tts_{timestamp}.xlsx"

        from fastapi.responses import Response

        return Response(
            content=excel_buffer.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(excel_buffer.getvalue()))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[download_history_excel] Erro inesperado: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao gerar arquivo Excel do histórico: {str(e)}")
