<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhamento dos Cálculos de Tensão Induzida - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF; /* Mais claro para melhor contraste */
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E; /* Fundo mais escuro */
            --card-bg-color: #2D2D2D; /* Fundo do card mais escuro */
            --sidebar-bg-color: #252525; /* Fundo da barra lateral mais escuro */
            --border-color: #6c757d;
            --link-color: #4DA3FF; /* Cor de link mais clara para melhor contraste */
            --link-hover-color: #80BDFF; /* Cor de hover mais clara */
            --heading-color: #FFFFFF; /* Cor de cabeçalho branca para melhor contraste */
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--link-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>        // Markdown content
        const markdownContent = `# Detalhamento dos Cálculos de Tensão Induzida

O ensaio de tensão induzida é realizado para verificar a capacidade do isolamento do transformador suportar sobretensões internas durante a operação. Este documento detalha os cálculos implementados conforme NBR 5356-3 / IEC 60076-3.

## 1. Parâmetros de Entrada

### 1.1. Dados Básicos do Transformador

| Parâmetro                     | Descrição                              | Unidade | Nome no Sistema                      |
| :---------------------------- | :------------------------------------- | :------ | :--------------------------------- |
| Tipo de Transformador         | Configuração (Monofásico/Trifásico)    | -       | \`tipo_transformador\`               |
| Tensão Nominal AT             | Tensão nominal do lado de Alta Tensão  | kV      | \`tensao_at\`                        |
| Tensão Nominal BT             | Tensão nominal do lado de Baixa Tensão | kV      | \`tensao_bt\`                        |
| Tensão Nominal Terciário      | Tensão nominal do terciário (se houver)| kV      | \`tensao_terciario\`                 |
| Frequência Nominal            | Frequência nominal da rede             | Hz      | \`frequencia\`                       |

### 1.2. Parâmetros Específicos do Módulo

| Parâmetro                     | Descrição                              | Unidade | Nome no Sistema                      |
| :---------------------------- | :------------------------------------- | :------ | :--------------------------------- |
| Frequência de Teste           | Frequência do ensaio de tensão induzida| Hz      | \`freq_teste\`                       |
| Tensão de Prova               | Tensão de ensaio aplicada              | kV      | \`teste_tensao_induzida_at\`         |
| Capacitância AT-GND           | Capacitância entre AT e terra          | pF      | \`capacitancia\`                     |

### 1.3. Dados de Materiais (dos dados de perdas)

| Parâmetro                     | Descrição                              | Unidade | Nome no Sistema                      |
| :---------------------------- | :------------------------------------- | :------ | :--------------------------------- |
| Tipo de Aço                   | Material do núcleo (M4, H110-27)      | -       | \`tipo_aco\`                         |
| Indução Nominal               | Nível de indução magnética nominal     | T       | \`inducao_nominal\`                  |
| Peso do Núcleo                | Peso do núcleo do transformador        | Ton     | \`peso_nucleo\`                      |
| Perdas em Vazio               | Perdas no núcleo                       | kW      | \`perdas_vazio\`                     |

## 2. Tabelas de Referência

### 2.1. Tipos de Aço Suportados

#### 2.1.1. Aço M4
- Tabelas de potência magnética e perdas específicas
- Frequências disponíveis: 50, 60, 100, 120, 150, 180, 200, 240 Hz
- Induções de 0.2 a 1.9 T

#### 2.1.2. Aço H110-27
- Tabelas de potência magnética e perdas específicas
- Frequências disponíveis: 50, 60 Hz
- Induções de 0.2 a 1.9 T
- **Extrapolação para outras frequências:** Lei de Steinmetz com 98.9% de precisão

### 2.2. Extrapolação para H110-27

Para frequências não disponíveis no H110-27, usa-se a Lei de Steinmetz modificada:

#### Perdas do Núcleo:
\`\`\`
P = k × f^1.3 × B^2.0
\`\`\`

#### Potência Magnética:
\`\`\`
Pm = k × f^1.2 × B^1.8
\`\`\`

Onde:
- P = Perdas específicas (W/kg)
- Pm = Potência magnética específica (VAr/kg)
- f = Frequência (Hz)
- B = Indução magnética (T)
- k = Constante do material

## 3. Cálculos Intermediários

### 3.1. Indução no Núcleo na Frequência de Teste

\`\`\`
β_teste = β_nominal × (U_teste / U_nominal_AT) × (f_nominal / f_teste)
\`\`\`

Onde:
- β_teste = Indução no teste (T)
- β_nominal = Indução nominal (T)
- U_teste = Tensão de ensaio (kV)
- U_nominal_AT = Tensão nominal AT (kV)
- f_nominal = Frequência nominal (Hz)
- f_teste = Frequência de teste (Hz)

**Limitação:** β_teste ≤ 1.9 T (limite físico)

### 3.2. Relação Up/Un

#### Para Transformadores Monofásicos:
\`\`\`
Up/Un = U_prova / U_nominal_AT
\`\`\`

#### Para Transformadores Trifásicos:
\`\`\`
Up/Un = U_prova / (U_nominal_AT / √3)
\`\`\`

### 3.3. Tensão Aplicada no Lado BT

\`\`\`
U_aplicada_BT = (U_nominal_BT / U_nominal_AT) × U_prova
\`\`\`

### 3.4. Verificação de Limites SUT e Seleção de Fonte

O sistema verifica se a tensão aplicada no BT está dentro dos limites do SUT:
- **Limite mínimo:** 5 kV
- **Limite máximo:** 800 kV

Se a tensão BT exceder os limites e o terciário estiver disponível, o sistema automaticamente seleciona o terciário como fonte de tensão.

## 4. Cálculos para Transformadores Monofásicos

### 4.1. Potência Ativa (Pw)

\`\`\`
Pw = fator_perdas × peso_nucleo_kg / 1000
\`\`\`

Onde:
- Pw = Potência ativa (kW)
- fator_perdas = Interpolado das tabelas (W/kg)
- peso_nucleo_kg = Peso do núcleo (kg)

### 4.2. Potência Magnética (Sm)

\`\`\`
Sm = fator_potencia_mag × peso_nucleo_kg / 1000
\`\`\`

Onde:
- Sm = Potência magnética (kVA)
- fator_potencia_mag = Interpolado das tabelas (VAr/kg)

### 4.3. Corrente de Excitação

\`\`\`
I_exc = Sm / U_aplicada_BT
\`\`\`

Onde:
- I_exc = Corrente de excitação (A)
- Sm = Potência magnética (kVA)
- U_aplicada_BT = Tensão aplicada no BT (kV)

### 4.4. Componente Indutiva (Sind)

\`\`\`
Sind = √(Sm² - Pw²)
\`\`\`

Onde:
- Sind = Potência reativa indutiva (kVAr)

**Verificação:** Se Sm² < Pw², então Sind = 0

### 4.5. Potência Capacitiva (Scap)

\`\`\`
U_dif = U_prova - (Up/Un × U_nominal_BT)
Scap = -((U_dif × 1000)² × 2π × f_teste × C × 10⁻¹²) / 3 / 1000
\`\`\`

Onde:
- U_dif = Tensão diferencial (kV)
- Scap = Potência capacitiva (kVAr)
- C = Capacitância AT-GND (pF)

## 5. Cálculos para Transformadores Trifásicos

### 5.1. Potência Ativa Total

\`\`\`
Pw_total = fator_perdas × peso_nucleo_kg / 1000
\`\`\`

### 5.2. Potência Magnética Total

\`\`\`
Sm_total = fator_potencia_mag × peso_nucleo_kg / 1000
\`\`\`

### 5.3. Corrente de Excitação

\`\`\`
I_exc = Sm_total / (√3 × U_aplicada_BT)
\`\`\`

### 5.4. Potência de Teste

\`\`\`
P_teste = I_exc × U_aplicada_BT × √3
\`\`\`

## 6. Análise dos Limites do EPS

### 6.1. Verificação de Potência Ativa

- **Limite DUT:** 100 kW
- **Status:** Verifica se Pw ≤ 100 kW

### 6.2. Verificação de Potência Reativa Magnética

- **Limite EPS:** 1800 kVA
- **Status:** Verifica se Sm ≤ 1800 kVA

### 6.3. Análise de Taps SUT

O sistema analisa os 5 taps SUT mais próximos da tensão aplicada e calcula:

#### Para Monofásico:
\`\`\`
I_EPS = I_exc × (V_SUT_tap / V_SUT_BT) × √3
\`\`\`

#### Para Trifásico:
\`\`\`
I_EPS = I_exc × (V_SUT_tap / V_SUT_BT)
\`\`\`

**Limite de corrente EPS:** -1800 A ≤ I_EPS ≤ 1800 A

### 6.4. Status de Viabilidade

- **OK:** Todos os limites atendidos
- **ATENÇÃO:** Próximo dos limites (>85%)
- **VIOLAÇÃO:** Limites excedidos

## 7. Tabela de Análise de Frequências

O sistema gera uma tabela com análise para diferentes frequências de teste:
- 100, 120, 150, 180, 200, 240 Hz

Para cada frequência, calcula:
- Indução de teste
- Potência ativa
- Potência magnética
- Potência capacitiva (se aplicável)
- Relação Scap/Sind (para monofásico)

## 8. Considerações Especiais

### 8.1. Uso do Terciário

Quando a tensão aplicada no BT excede os limites SUT:
- Sistema verifica disponibilidade do terciário
- Recalcula usando tensão e corrente do terciário
- Exibe mensagem informativa sobre a mudança

### 8.2. Precisão dos Cálculos

- **Interpolação bilinear:** Para valores intermediários nas tabelas
- **Extrapolação H110-27:** 98.9% de precisão para frequências não tabeladas
- **Limitações físicas:** Indução máxima 1.9 T

### 8.3. Validações

- Indução de teste deve ser positiva
- Potência magnética deve ser ≥ potência ativa
- Frequência de teste deve ser positiva
- Tensão de prova deve ser positiva

---

## Notas Importantes

1. **Tipo de aço:** Determina as tabelas de referência utilizadas
2. **Extrapolação:** Para H110-27, frequências não tabeladas usam Lei de Steinmetz
3. **Terciário:** Automaticamente usado se BT exceder limites SUT
4. **Limites EPS:** Análise completa de viabilidade do equipamento
5. **Análise de frequência:** Tabela comparativa para otimização do teste`;
`;

        // Function to generate TOC and add IDs to headings in the actual document
        function generateTOC(html) {
            const container = document.createElement('div');
            container.innerHTML = html;

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const toc = document.getElementById('toc');
            toc.innerHTML = ''; // Clear existing TOC

            // First pass: assign IDs to headings in our temporary container
            headings.forEach((heading, index) => {
                const headingText = heading.textContent.trim();
                // Create a slug from the heading text
                const slug = headingText
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special chars
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen

                // Ensure unique ID by adding index if slug is empty or duplicated
                heading.id = slug ? `${slug}-${index}` : `heading-${index}`;
            });

            // Now find the actual headings in the document and assign the same IDs
            const contentDiv = document.getElementById('markdown-content');
            const actualHeadings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

            actualHeadings.forEach((heading, index) => {
                if (index < headings.length) {
                    heading.id = headings[index].id;
                }
            });

            // Now build the TOC with links to the actual headings
            headings.forEach((heading, index) => {
                const level = parseInt(heading.tagName.substring(1));
                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;

                const link = document.createElement('a');
                link.href = `#${heading.id}`;
                link.textContent = heading.textContent;
                link.setAttribute('data-heading-id', heading.id);

                // Add click handler to ensure smooth scrolling and proper focus
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-heading-id');
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Scroll to the element smoothly
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Set focus to the heading for accessibility
                        targetElement.setAttribute('tabindex', '-1');
                        targetElement.focus();

                        // Update URL hash without jumping
                        history.pushState(null, null, `#${targetId}`);

                        // Add active class to the current TOC item
                        document.querySelectorAll('.toc a').forEach(a => {
                            a.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }

        // Function to highlight search terms
        function highlightSearchTerms(content, term) {
            if (!term) return content;

            const regex = new RegExp(`(${term})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Function to render markdown
        function renderMarkdown() {
            const searchTerm = document.getElementById('search-input').value.trim();
            let content = markdownContent;

            // Configure marked to add IDs to headings
            marked.use({
                headerIds: true,
                headerPrefix: ''
            });

            // Render markdown to HTML
            const renderedHTML = marked.parse(content);

            // Apply search highlighting if there's a search term
            const finalHTML = searchTerm ? highlightSearchTerms(renderedHTML, searchTerm) : renderedHTML;

            // Update content
            document.getElementById('markdown-content').innerHTML = finalHTML;

            // Generate TOC and ensure IDs are properly set
            generateTOC(finalHTML);

            // Initialize syntax highlighting
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });

            // Render math expressions
            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            // Check if there's a hash in the URL and scroll to it after rendering
            setTimeout(() => {
                if (window.location.hash) {
                    const hash = window.location.hash.substring(1);
                    const targetElement = document.getElementById(hash);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Highlight the corresponding TOC item
                        const tocLink = document.querySelector(`.toc a[data-heading-id="${hash}"]`);
                        if (tocLink) {
                            document.querySelectorAll('.toc a').forEach(a => {
                                a.classList.remove('active');
                            });
                            tocLink.classList.add('active');
                        }
                    }
                }
            }, 500); // Small delay to ensure rendering is complete
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            renderMarkdown();

            // Set up search
            document.getElementById('search-input').addEventListener('input', (e) => {
                renderMarkdown();

                // Scroll to first highlight if there is one
                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    </script>
</body>
</html>

