import os
import sys
import pathlib
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import webbrowser
import socket
import time
import threading
from fastapi.responses import Response
import logging # Importar o módulo de logging

# ==============================================================================
# Configuração Inicial de Logging (ADICIONAR AQUI NO TOPO)
# ==============================================================================
# Configura o logger raiz. Todos os loggers herdarão esta configuração
# se não tiverem uma configuração mais específica.
logging.basicConfig(
    level=logging.DEBUG,  # Nível mínimo de log a ser capturado (DEBUG é o mais detalhado)
    format='%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
# Você pode configurar loggers específicos se quiser níveis diferentes para módulos diferentes
# Exemplo: logging.getLogger("uvicorn.access").setLevel(logging.WARNING) para silenciar logs de acesso do uvicorn

# O logger para o seu losses_service será 'losses_service' (nome do arquivo sem .py)
# ou o nome que você deu em `log = logging.getLogger(__name__)` dentro do service.
# Se for `__name__`, será `backend.services.losses_service`.
# Para garantir que os logs do seu serviço apareçam:
logging.getLogger("backend.services.losses_service").setLevel(logging.DEBUG)
# Ou, se o nome do logger for apenas "losses_service":
# logging.getLogger("losses_service").setLevel(logging.DEBUG)

# ==============================================================================

# Configuração dos caminhos para importação correta independente de onde o script é executado
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
root_dir = current_dir.parent

# Adiciona o diretório raiz e o diretório backend ao path
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Tenta importar os módulos usando diferentes estratégias
try:
    from backend.routers import transformer_routes, data_routes
    from backend.mcp.data_manager import MCPDataManager
    from backend.mcp.session_manager import MCPSessionManager
except ImportError:
    try:
        from routers import transformer_routes, data_routes
        from mcp.data_manager import MCPDataManager
        from mcp.session_manager import MCPSessionManager
    except ImportError as e:
        logging.error(f"Falha ao importar módulos: {e}") # Usar logging aqui também
        print("ERRO: Não foi possível importar os módulos necessários.")
        print("Certifique-se de que está executando o script do diretório correto:")
        print("1. Do diretório raiz: python -m backend.main")
        print("2. Do diretório backend: python main.py")
        sys.exit(1)

# Criar a instância da aplicação FastAPI
app = FastAPI(
    title="Simulador de Testes de Transformadores",
    description="API para o Simulador de Testes de Transformadores",
    version="1.9.9"
)

# Configurar CORS para permitir requisições do frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inicializar o sistema de dados
mcp_data_manager = MCPDataManager()
session_manager = MCPSessionManager(mcp_data_manager)

# Configurar os data managers nos routers
transformer_routes.mcp_data_manager = mcp_data_manager
data_routes.set_data_manager(mcp_data_manager)

# Incluir routers na aplicação
app.include_router(transformer_routes.router)
app.include_router(data_routes.router)

# Rota de teste para verificar se a API está funcionando
@app.get("/api/health")
async def health_check():
    logging.info("Health check solicitado.") # Exemplo de log de info
    return {"status": "ok", "message": "API está funcionando"}

# Rota para servir favicon.ico para evitar 404
@app.get("/favicon.ico")
async def favicon():
    return Response(status_code=204)

# Montar arquivos estáticos (frontend)
frontend_dir = os.path.join(root_dir, "public")
if os.path.exists(frontend_dir):
    app.mount("/", StaticFiles(directory=frontend_dir, html=True), name="static")
else:
    logging.warning(f"Diretório frontend não encontrado em {frontend_dir}")

def check_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        try:
            result = sock.connect_ex(('localhost', port))
            return result == 0
        except Exception as e:
            logging.warning(f"Erro ao verificar porta {port}: {e}")
            return False

def open_browser_delayed():
    time.sleep(1.5)
    webbrowser.open('http://localhost:8000')

if __name__ == "__main__":
    port = 8000
     
    if check_port_in_use(port):
        logging.warning(f"Porta {port} já está em uso. O navegador não será aberto automaticamente.")
        print(f"⚠️  Porta {port} já está em uso. O navegador não será aberto automaticamente.")
        print(f"   Se for outra instância desta aplicação, acesse: http://localhost:{port}")
    else:
        logging.info(f"Iniciando servidor na porta {port}...")
        print(f"Iniciando servidor na porta {port}...")
        print(f"Abrindo navegador automaticamente em http://localhost:{port}")
        threading.Thread(target=open_browser_delayed, daemon=True).start()
    
    is_in_backend_dir = os.path.basename(os.getcwd()) == "backend"
    module_path = "main:app" if is_in_backend_dir else "backend.main:app"
    
    logging.info(f"Rodando uvicorn com module_path: {module_path}")
    uvicorn.run(
        module_path,
        host="0.0.0.0",
        port=port,
        reload=True,
        reload_dirs=[str(current_dir) if is_in_backend_dir else str(root_dir)],
        log_config=None, # Desabilita a configuração de log padrão do uvicorn para evitar problemas com PyInstaller
        # Ajuste o nível de log do uvicorn se quiser menos verbosidade dele
        log_level="info" # ou "warning" para menos logs do uvicorn
    )