# backend/routers/data_routes.py
"""
Rotas da API para gerenciamento de dados dos stores.
Implementa endpoints REST para persistência via MCPDataManager.
"""

from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any
from datetime import datetime, timezone, timedelta

# Importações com fallback para diferentes estruturas de projeto
try:
    from ..mcp.data_manager import MCPDataManager
except ImportError:
    try:
        from backend.mcp.data_manager import MCPDataManager
    except ImportError:
        from mcp.data_manager import MCPDataManager

# Instância global do data manager (será definida por main.py)
mcp_data_manager = None

router = APIRouter(prefix="/api/data", tags=["data"])

def get_local_timestamp():
    """Retorna timestamp local do Brasil (UTC-3)"""
    # Fuso horário do Brasil (UTC-3)
    brazil_tz = timezone(timedelta(hours=-3))
    return datetime.now(brazil_tz).strftime('%Y-%m-%d %H:%M:%S')

def set_data_manager(data_manager: MCPDataManager):
    """Define a instância do data manager para uso nas rotas."""
    global mcp_data_manager
    mcp_data_manager = data_manager

@router.get("/health")
async def health_check():
    """Verifica se a API de dados está funcionando."""
    return {"status": "ok", "message": "API de dados funcionando"}

@router.get("/stores")
async def list_stores():
    """Lista todos os stores disponíveis."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        stores = list(mcp_data_manager.store_definitions.keys())
        return {"stores": stores}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao listar stores: {str(e)}")

@router.get("/stores/{store_id}")
async def get_store_data(store_id: str):
    """Obtém os dados de um store específico."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        data = mcp_data_manager.get_data(store_id)
        return data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter dados: {str(e)}")

@router.put("/stores/{store_id}")
async def set_store_data(store_id: str, data: Dict[str, Any] = Body(...)):
    """Define os dados completos de um store."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        success = mcp_data_manager.set_data(store_id, data)
        if success:
            return mcp_data_manager.get_data(store_id)
        else:
            raise HTTPException(status_code=500, detail="Falha ao salvar dados")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao definir dados: {str(e)}")

@router.patch("/stores/{store_id}")
async def update_store_data(store_id: str, partial_data: Dict[str, Any] = Body(...)):
    """Atualiza parcialmente os dados de um store."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        success = mcp_data_manager.patch_data(store_id, partial_data)
        if success:
            return mcp_data_manager.get_data(store_id)
        else:
            raise HTTPException(status_code=500, detail="Falha ao atualizar dados")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao atualizar dados: {str(e)}")

@router.delete("/stores/{store_id}")
async def clear_store_data(store_id: str):
    """Limpa os dados de um store específico."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        success = mcp_data_manager.clear_store(store_id)
        if success:
            return {"message": f"Store {store_id} limpo com sucesso"}
        else:
            raise HTTPException(status_code=500, detail="Falha ao limpar store")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao limpar store: {str(e)}")

@router.delete("/stores")
async def clear_all_stores():
    """Limpa todos os stores."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        success = mcp_data_manager.clear_all_stores()
        if success:
            return {"message": "Todos os stores foram limpos com sucesso"}
        else:
            raise HTTPException(status_code=500, detail="Falha ao limpar stores")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao limpar stores: {str(e)}")

@router.get("/stores/{store_id}/export")
async def export_store_data(store_id: str):
    """Exporta os dados de um store em formato JSON."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        data = mcp_data_manager.get_data(store_id)
        return {
            "store_id": store_id,
            "data": data,
            "exported_at": datetime.now().isoformat()
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao exportar dados: {str(e)}")

@router.post("/stores/{store_id}/import")
async def import_store_data(store_id: str, import_data: Dict[str, Any] = Body(...)):
    """Importa dados para um store."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        # Extrai apenas os dados, ignorando metadados de exportação
        data_to_import = import_data.get("data", import_data)
        success = mcp_data_manager.set_data(store_id, data_to_import)

        if success:
            return {
                "message": f"Dados importados com sucesso para o store {store_id}",
                "data": mcp_data_manager.get_data(store_id)
            }
        else:
            raise HTTPException(status_code=500, detail="Falha ao importar dados")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao importar dados: {str(e)}")

@router.get("/backup")
async def backup_all_data():
    """Cria um backup completo de todos os stores."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        all_stores = mcp_data_manager.get_all_stores()
        return {
            "backup_timestamp": datetime.now().isoformat(),
            "stores": all_stores
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao criar backup: {str(e)}")

@router.post("/restore")
async def restore_all_data(backup_data: Dict[str, Any] = Body(...)):
    """Restaura dados de um backup completo."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        stores_data = backup_data.get("stores", {})
        restored_stores = []

        for store_id, data in stores_data.items():
            if store_id in mcp_data_manager.store_definitions:
                success = mcp_data_manager.set_data(store_id, data)
                if success:
                    restored_stores.append(store_id)

        return {
            "message": f"Backup restaurado com sucesso",
            "restored_stores": restored_stores,
            "total_stores": len(restored_stores)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao restaurar backup: {str(e)}")

# ============================================================================
# ROTAS DE SESSÃO - Integração com MCPDataManager
# ============================================================================

@router.get("/sessions")
async def list_sessions():
    """Lista todas as sessões disponíveis."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        sessions = mcp_data_manager.list_sessions()
        return {"sessions": sessions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao listar sessões: {str(e)}")

@router.post("/sessions")
async def create_session(session_data: Dict[str, Any] = Body(...)):
    """Cria uma nova sessão com o estado atual."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        session_id = session_data.get("session_id")
        session_name = session_data.get("session_name", "")
        description = session_data.get("description", "")

        if not session_id:
            raise HTTPException(status_code=400, detail="session_id é obrigatório")

        success = mcp_data_manager.save_session(session_id, description, session_name)
        if success:
            return {
                "message": "Sessão criada com sucesso",
                "session_id": session_id,
                "session_name": session_name,
                "description": description
            }
        else:
            raise HTTPException(status_code=500, detail="Falha ao criar sessão")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao criar sessão: {str(e)}")

@router.post("/sessions/{session_id}/restore")
async def restore_session(session_id: str):
    """Restaura uma sessão específica."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        success = mcp_data_manager.load_session(session_id)
        if success:
            return {
                "message": f"Sessão {session_id} restaurada com sucesso",
                "session_id": session_id
            }
        else:
            raise HTTPException(status_code=404, detail=f"Sessão {session_id} não encontrada")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao restaurar sessão: {str(e)}")

@router.put("/sessions/{session_id}")
async def update_session(session_id: str, session_data: Dict[str, Any] = Body(...)):
    """Atualiza os metadados de uma sessão específica (nome e descrição)."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        import sqlite3
        import json
        import logging

        session_name = session_data.get("session_name", "")
        description = session_data.get("description", "")

        logging.info(f"[update_session] Atualizando sessão {session_id} com nome: '{session_name}' e descrição: '{description}'")

        with sqlite3.connect(mcp_data_manager.db_path) as conn:
            cursor = conn.cursor()

            # Adicionar coluna updated_at se não existir
            try:
                cursor.execute('ALTER TABLE sessions ADD COLUMN updated_at TIMESTAMP')
                logging.info("[update_session] Coluna updated_at adicionada")
            except sqlite3.OperationalError:
                pass  # Coluna já existe

            # Verificar se a sessão existe
            cursor.execute('SELECT session_data FROM sessions WHERE session_id = ?', (session_id,))
            result = cursor.fetchone()
            if not result:
                logging.error(f"[update_session] Sessão {session_id} não encontrada")
                raise HTTPException(status_code=404, detail=f"Sessão {session_id} não encontrada")

            # Atualizar os metadados da sessão
            local_timestamp = get_local_timestamp()
            cursor.execute('''
                UPDATE sessions
                SET description = ?, updated_at = ?
                WHERE session_id = ?
            ''', (description, local_timestamp, session_id))

            logging.info(f"[update_session] Descrição atualizada para sessão {session_id}")

            # Atualizar o session_name dentro dos dados da sessão
            session_data_json = result[0]
            try:
                session_data_dict = json.loads(session_data_json)
                session_data_dict['session_name'] = session_name
                updated_session_data_json = json.dumps(session_data_dict)

                cursor.execute('''
                    UPDATE sessions
                    SET session_data = ?
                    WHERE session_id = ?
                ''', (updated_session_data_json, session_id))

                logging.info(f"[update_session] Nome da sessão atualizado para '{session_name}' na sessão {session_id}")

            except json.JSONDecodeError as e:
                logging.error(f"[update_session] Erro ao decodificar JSON da sessão {session_id}: {e}")
                # Se não conseguir decodificar, apenas atualiza a descrição
                pass

            conn.commit()
            logging.info(f"[update_session] Commit realizado para sessão {session_id}")

        return {
            "message": f"Sessão {session_id} atualizada com sucesso",
            "session_id": session_id,
            "session_name": session_name,
            "description": description
        }
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"[update_session] Erro inesperado ao atualizar sessão {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro ao atualizar sessão: {str(e)}")

@router.post("/sessions/{session_id}/update")
async def update_session_state(session_id: str):
    """Atualiza uma sessão existente com o estado atual (preserva data de criação)."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        import sqlite3

        with sqlite3.connect(mcp_data_manager.db_path) as conn:
            cursor = conn.cursor()

            # Adicionar coluna updated_at se não existir
            try:
                cursor.execute('ALTER TABLE sessions ADD COLUMN updated_at TIMESTAMP')
            except:
                pass  # Coluna já existe

            # Verificar se a sessão existe e obter dados atuais
            cursor.execute('SELECT session_data, description FROM sessions WHERE session_id = ?', (session_id,))
            result = cursor.fetchone()
            if not result:
                raise HTTPException(status_code=404, detail=f"Sessão {session_id} não encontrada")

            old_session_data_json, _ = result  # description não usado

            # Obter session_name dos dados antigos
            import json
            old_session_data = {}
            try:
                old_session_data = json.loads(old_session_data_json)
                session_name = old_session_data.get('session_name', session_id)
            except json.JSONDecodeError:
                session_name = session_id

            # Criar novos dados da sessão com estado atual
            new_session_data = {
                'stores': mcp_data_manager.get_all_stores(),
                'timestamp': old_session_data.get('timestamp'),  # Preserva timestamp original
                'session_name': session_name  # Preserva nome original
            }
            new_session_json = json.dumps(new_session_data)

            # Atualizar apenas os dados da sessão e updated_at
            local_timestamp = get_local_timestamp()
            cursor.execute('''
                UPDATE sessions
                SET session_data = ?, updated_at = ?
                WHERE session_id = ?
            ''', (new_session_json, local_timestamp, session_id))

            conn.commit()

        return {
            "message": f"Estado da sessão {session_id} atualizado com sucesso",
            "session_id": session_id,
            "session_name": session_name
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao atualizar estado da sessão: {str(e)}")

@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Deleta uma sessão específica."""
    if not mcp_data_manager:
        raise HTTPException(status_code=500, detail="Data manager não inicializado")

    try:
        import sqlite3
        with sqlite3.connect(mcp_data_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM sessions WHERE session_id = ?', (session_id,))
            if cursor.rowcount == 0:
                raise HTTPException(status_code=404, detail=f"Sessão {session_id} não encontrada")
            conn.commit()

        return {
            "message": f"Sessão {session_id} deletada com sucesso",
            "session_id": session_id
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao deletar sessão: {str(e)}")


@router.post("/download-all-history")
async def download_all_history(request_data: Dict[str, Any] = Body(...)):
    """
    Baixa todos os dados históricos em formato Excel com autenticação por senha.
    Separa dados em planilhas para transformadores trifásicos e monofásicos.
    """
    try:
        import os
        from dotenv import load_dotenv

        # Carregar variáveis de ambiente
        load_dotenv()

        # Verificar senha
        provided_password = request_data.get('password', '')
        correct_password = os.getenv('HISTORY_DOWNLOAD_PASSWORD', '754210')

        if provided_password != correct_password:
            raise HTTPException(status_code=401, detail="Senha incorreta")

        if not mcp_data_manager:
            raise HTTPException(status_code=500, detail="Data manager não inicializado")

        print("[download_all_history] Iniciando download de dados históricos completos")

        # Coletar todas as sessões
        sessions = mcp_data_manager.list_sessions()

        if not sessions:
            raise HTTPException(status_code=404, detail="Nenhum dado histórico encontrado")

        # Gerar Excel com dados históricos
        from backend.reports.history_excel_generator import generate_history_excel_report
        excel_buffer = generate_history_excel_report(sessions)

        # Preparar resposta
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"historico_completo_tts_{timestamp}.xlsx"

        # Retornar Excel como bytes
        from fastapi.responses import Response

        return Response(
            content=excel_buffer.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[download_all_history] Erro inesperado: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao gerar arquivo de histórico: {str(e)}")