<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simuladores Elétricos</title>
    <!-- Links CSS (Unificados) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card-header {
            font-weight: 600;
        }
        .display-value {
            font-family: 'Courier New', Courier, monospace;
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            background-color: #e9ecef;
            display: inline-block;
            text-align: center;
        }

        /* --- Estilos para a Aba "Vazio" --- */
        #vazio-tab-pane .display-value {
             font-size: 1.2rem;
             min-width: 80px;
        }
        .vazio-wire-container {
            position: absolute;
            top: 50%;
            left: 15%; /* Inicia depois do primeiro card */
            width: 70%; /* Estende-se entre os cards */
            height: 8px;
            margin-top: -4px;
            z-index: -1;
        }
        .vazio-wire {
            width: 100%;
            height: 100%;
            background-color: #adb5bd; /* Cor padrão quando desligado */
        }
        .vazio-animated-wire {
            background-repeat: repeat-x;
            background-size: 40px 40px;
            animation: vazio-flow 1.2s linear infinite;
        }
        .vazio-wire-color-normal { background-image: linear-gradient(45deg, #198754 25%, #20c997 25%, #20c997 50%, #198754 50%, #198754 75%, #20c997 75%, #20c997 100%); }
        .vazio-wire-color-critical { background-image: linear-gradient(45deg, #dc3545 25%, #fd7e14 25%, #fd7e14 50%, #dc3545 50%, #dc3545 75%, #fd7e14 75%, #fd7e14 100%); }
        @keyframes vazio-flow {
            from { background-position: 0 0; }
            to { background-position: 40px 0; }
        }
        @keyframes vazio-flash-border {
            0%, 100% { border-color: #dc3545; box-shadow: 0 0 10px rgba(220, 53, 69, 0.5); }
            50% { border-color: #212529; box-shadow: none; }
        }
        .vazio-critical-flash {
            animation: vazio-flash-border 1s infinite;
        }

        /* --- Estilos para a Aba "Carga" --- */
        #carga-tab-pane .display-value {
             font-size: 1.5rem;
             min-width: 100px;
        }
        .carga-wire {
            position: absolute;
            background-color: #adb5bd;
            z-index: -1;
        }
        .carga-animated-wire {
            background-repeat: repeat-x;
            background-size: 40px 40px;
            animation: carga-flow 1.2s linear infinite;
        }
        .carga-animated-wire.reverse {
            animation-direction: reverse;
        }
        .carga-wire-color-source { background-image: linear-gradient(45deg, #198754 25%, #20c997 25%, #20c997 50%, #198754 50%, #198754 75%, #20c997 75%, #20c997 100%); }
        .carga-wire-color-load { background-image: linear-gradient(45deg, #fd7e14 25%, #ffc107 25%, #ffc107 50%, #fd7e14 50%, #fd7e14 75%, #ffc107 75%, #ffc107 100%); }
        .carga-wire-color-cap { background-image: linear-gradient(45deg, #0d6efd 25%, #0dcaf0 25%, #0dcaf0 50%, #0d6efd 50%, #0d6efd 75%, #0dcaf0 75%, #0dcaf0 100%); }
        @keyframes carga-flow {
            from { background-position: 0 0; }
            to { background-position: 40px 0; }
        }
        #carga-wire-main { top: 50%; left: 0; width: 100%; height: 8px; margin-top: -4px; }
        #carga-wire-t-junction-cap { top: 25%; left: 75%; width: 8px; height: 25%; margin-left: -4px; }
        #carga-wire-t-junction-dut { bottom: 25%; left: 75%; width: 8px; height: 25%; margin-left: -4px; }

    </style>
</head>
<body>
    <div class="container-fluid p-4">

        <!-- Navegação das Abas -->
        <ul class="nav nav-tabs" id="simuladorTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="vazio-tab" data-bs-toggle="tab" data-bs-target="#vazio-tab-pane" type="button" role="tab" aria-controls="vazio-tab-pane" aria-selected="true">Vazio</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="carga-tab" data-bs-toggle="tab" data-bs-target="#carga-tab-pane" type="button" role="tab" aria-controls="carga-tab-pane" aria-selected="false">Carga</button>
            </li>
        </ul>

        <!-- Conteúdo das Abas -->
        <div class="tab-content" id="simuladorTabsContent">

            <!-- #################################### -->
            <!-- ### INÍCIO DO PAINEL DA ABA "VAZIO" ### -->
            <!-- #################################### -->
            <div class="tab-pane fade show active" id="vazio-tab-pane" role="tabpanel" aria-labelledby="vazio-tab" tabindex="0">
                <div class="container-fluid p-4">
                    <h2 class="text-center mb-4">Simulador de Perdas em Vazio</h2>

                    <!-- Linha do Diagrama Principal -->
                    <div class="row align-items-center justify-content-center text-center position-relative mb-4" style="height: 350px;">
                        <!-- Fio de Conexão (Visualização) -->
                        <div class="vazio-wire-container">
                            <div id="vazio-main-wire" class="vazio-wire"></div>
                        </div>

                        <!-- Coluna da Fonte (EPS) -->
                        <div class="col-md-3">
                            <div id="vazio-eps-card" class="card shadow-sm">
                                <div class="card-header bg-dark text-white"><i class="fas fa-bolt me-2"></i>EPS SYSTEM</div>
                                <div class="card-body">
                                    <p class="mb-2">Tensão (V): <span id="vazio-eps-voltage" class="display-value text-primary">0.0</span></p>
                                    <p class="mb-2">Corrente (A): <span id="vazio-eps-current" class="display-value text-success">0.0</span></p>
                                    <p class="mb-0">Potência (kVA): <span id="vazio-eps-kva" class="display-value text-danger">0.0</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Coluna do SUT -->
                        <div class="col-md-3">
                             <div id="vazio-sut-card" class="card shadow-sm">
                                <div class="card-header bg-secondary text-white"><i class="fas fa-random me-2"></i>SUT</div>
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">ENTRADA</h6>
                                    <p class="mb-2">V: <span id="vazio-sut-input-voltage" class="display-value">0.0</span></p>
                                    <p class="mb-2">I: <span id="vazio-sut-input-current" class="display-value">0.0</span></p>
                                    <hr>
                                    <h6 class="card-subtitle mb-2 text-muted">SAÍDA</h6>
                                    <p class="mb-2">V (kV): <span id="vazio-sut-output-voltage" class="display-value">0.0</span></p>
                                    <p class="mb-0">I (A): <span id="vazio-sut-output-current" class="display-value">0.0</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Coluna da Carga (DUT) -->
                        <div class="col-md-3">
                            <div id="vazio-dut-card" class="card shadow-sm">
                                <div class="card-header" style="background-color: #fd7e14; color: white;"><i class="fas fa-industry me-2"></i>CARGA (DUT)</div>
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">ENTRADA</h6>
                                    <p class="mb-2">V (kV): <span id="vazio-dut-input-voltage" class="display-value">0.0</span></p>
                                    <p class="mb-0">I (A): <span id="vazio-dut-input-current" class="display-value">0.0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Linha de Controles e Status -->
                    <div class="row justify-content-center g-4">
                        <!-- Controles -->
                        <div class="col-md-7">
                            <div class="card shadow-sm">
                                <div class="card-header"><i class="fas fa-sliders-h me-2"></i>Painel de Controle</div>
                                <div class="card-body d-flex justify-content-around align-items-center">
                                    <!-- Controle de Potência (EPS) -->
                                    <div class="text-center">
                                        <h5>Potência da Fonte</h5>
                                        <div class="d-flex align-items-center gap-3">
                                            <button id="vazio-btn-down" class="btn btn-danger btn-lg"><i class="fas fa-caret-down"></i></button>
                                            <span id="vazio-power-level-display" class="display-value" style="font-size: 2rem;">0</span>
                                            <button id="vazio-btn-up" class="btn btn-success btn-lg"><i class="fas fa-caret-up"></i></button>
                                        </div>
                                        <div class="mt-2">%</div>
                                    </div>
                                    <!-- Controle do Núcleo -->
                                    <div class="text-center">
                                        <h5>Condição do Núcleo (DUT)</h5>
                                         <select class="form-select form-select-lg" id="vazio-core-selector">
                                            <option value="good" selected>Núcleo Bom</option>
                                            <option value="bad_current">Falha: Limite de Corrente</option>
                                            <option value="bad_power">Falha: Limite de Potência</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Status Resumido -->
                        <div class="col-md-5">
                            <div class="card shadow-sm h-100">
                                <div class="card-header"><i class="fas fa-info-circle me-2"></i>Status do Sistema</div>
                                <div class="card-body d-flex flex-column justify-content-center text-center">
                                    <div id="vazio-system-status-card" class="alert alert-secondary mb-0">
                                        <h5 class="alert-heading" id="vazio-status-label">SISTEMA DESLIGADO</h5>
                                        <p class="mb-0" id="vazio-status-message">Ajuste a potência para iniciar a simulação.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ################################## -->
            <!-- ### FIM DO PAINEL DA ABA "VAZIO" ### -->
            <!-- ################################## -->


            <!-- ################################### -->
            <!-- ### INÍCIO DO PAINEL DA ABA "CARGA" ### -->
            <!-- ################################### -->
            <div class="tab-pane fade" id="carga-tab-pane" role="tabpanel" aria-labelledby="carga-tab" tabindex="0">
                <div class="container-fluid p-4">
                    <h2 class="text-center mb-4">Simulador de Perdas em Carga</h2>
            
                    <!-- Linha do Diagrama Principal -->
                    <!-- ===================== LINHA CORRIGIDA ABAIXO ===================== -->
                    <div class="row align-items-stretch justify-content-center text-center position-relative mb-4" style="height: 400px;">
                        <!-- Fios de Conexão (Visualização) -->
                        <div id="carga-wire-main" class="carga-wire"></div>
                        <div id="carga-wire-t-junction-cap" class="carga-wire"></div>
                        <div id="carga-wire-t-junction-dut" class="carga-wire"></div>
            
                        <!-- Coluna da Fonte -->
                        <div class="col-md-5 d-flex justify-content-around align-items-center">
                            <div class="card shadow-sm" style="width: 240px;">
                                <div class="card-header bg-dark text-white"><i class="fas fa-bolt me-2"></i>EPS SYSTEM</div>
                                <div class="card-body">
                                    <p class="mb-1">Tensão (V): <span id="carga-eps-voltage" class="display-value text-primary">0.0</span></p>
                                    <p class="mb-0">Corrente (A): <span id="carga-eps-current" class="display-value text-success">0.0</span></p>
                                </div>
                            </div>
                            <div class="card shadow-sm" style="width: 240px;">
                                <div class="card-header bg-secondary text-white"><i class="fas fa-random me-2"></i>SUT</div>
                                <div class="card-body">
                                    <p class="mb-1">Tensão (kV): <span id="carga-sut-output-voltage" class="display-value text-primary">0.0</span></p>
                                    <p class="mb-0">Corrente (A): <span id="carga-sut-output-current" class="display-value text-success">0.0</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Coluna de Junção (invisível, para espaçamento) -->
                        <div class="col-md-2"></div>
                        
                        <!-- Coluna da Carga e Capacitor -->
                        <!-- ===================== LINHA CORRIGIDA ABAIXO ===================== -->
                        <div class="col-md-5 d-flex flex-column align-items-center justify-content-between py-4">
                            <div class="card shadow-sm" style="width: 240px;">
                                <div class="card-header" style="background-color: #0d6efd; color: white;"><i class="fas fa-copyright me-2"></i>CAP</div>
                                <div class="card-body">
                                    <p class="mb-0">Corrente (A): <span id="carga-capacitor-current-display" class="display-value text-primary">0.0</span></p>
                                </div>
                            </div>
                            <div class="card shadow-sm" style="width: 240px;">
                                <div class="card-header" style="background-color: #fd7e14; color: white;"><i class="fas fa-industry me-2"></i>CARGA (DUT)</div>
                                <div class="card-body">
                                    <p class="mb-0">Demanda (A): <span class="display-value text-danger">640.0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
            
                    <!-- Linha de Controles e Status -->
                    <div class="row justify-content-center g-4">
                        <!-- Controles -->
                        <div class="col-md-8">
                            <div class="card shadow-sm">
                                <div class="card-header"><i class="fas fa-sliders-h me-2"></i>Painel de Controle</div>
                                <div class="card-body d-flex justify-content-around align-items-center">
                                    <!-- Controle de Potência (EPS) -->
                                    <div class="text-center">
                                        <h5>Potência da Fonte</h5>
                                        <div class="d-flex align-items-center gap-3">
                                            <button id="carga-btn-down" class="btn btn-danger btn-lg"><i class="fas fa-caret-down"></i></button>
                                            <span id="carga-power-level-display" class="display-value" style="font-size: 2rem;">0</span>
                                            <button id="carga-btn-up" class="btn btn-success btn-lg"><i class="fas fa-caret-up"></i></button>
                                        </div>
                                        <div class="mt-2">%</div>
                                    </div>
                                    <!-- Controles do Capacitor -->
                                    <div class="text-center">
                                        <h5>Tensão Nominal (V_cap)</h5>
                                         <div class="btn-group">
                                            <button class="btn btn-outline-primary" id="carga-btn-v-cap-down"><i class="fas fa-minus"></i></button>
                                            <button class="btn btn-primary" id="carga-v-cap-display" style="width: 120px;">14.0 kV</button>
                                            <button class="btn btn-outline-primary" id="carga-btn-v-cap-up"><i class="fas fa-plus"></i></button>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <h5>Potência Reativa (Q_nom)</h5>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-primary" id="carga-btn-q-cap-down"><i class="fas fa-minus"></i></button>
                                            <button class="btn btn-primary" id="carga-q-cap-display" style="width: 120px;">0 kVAR</button>
                                            <button class="btn btn-outline-primary" id="carga-btn-q-cap-up"><i class="fas fa-plus"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Status Resumido -->
                        <div class="col-md-4">
                            <div class="card shadow-sm h-100">
                                <div class="card-header"><i class="fas fa-info-circle me-2"></i>Status Resumido</div>
                                <div class="card-body d-flex flex-column justify-content-center text-center">
                                    <div id="carga-eps-status-card" class="alert alert-secondary">
                                        <h5 class="alert-heading" id="carga-eps-status-label">FONTE DESLIGADA</h5>
                                        <p class="mb-0" id="carga-eps-status-message">Ajuste a potência para iniciar.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ################################# -->
            <!-- ### FIM DO PAINEL DA ABA "CARGA" ### -->
            <!-- ################################# -->

        </div> <!-- Fim de .tab-content -->
    </div> <!-- Fim de .container-fluid -->


    <!-- Bootstrap JS Bundle (Necessário para a funcionalidade das abas) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script para o Simulador "Vazio" -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const elements = {
                btnUp: document.getElementById('vazio-btn-up'), btnDown: document.getElementById('vazio-btn-down'),
                powerLevelDisplay: document.getElementById('vazio-power-level-display'),
                epsVoltage: document.getElementById('vazio-eps-voltage'), epsCurrent: document.getElementById('vazio-eps-current'),
                epsKva: document.getElementById('vazio-eps-kva'),
                sutInputVoltage: document.getElementById('vazio-sut-input-voltage'), sutInputCurrent: document.getElementById('vazio-sut-input-current'),
                sutOutputVoltage: document.getElementById('vazio-sut-output-voltage'), sutOutputCurrent: document.getElementById('vazio-sut-output-current'),
                dutInputVoltage: document.getElementById('vazio-dut-input-voltage'), dutInputCurrent: document.getElementById('vazio-dut-input-current'),
                coreSelector: document.getElementById('vazio-core-selector'),
                systemStatusCard: document.getElementById('vazio-system-status-card'),
                statusLabel: document.getElementById('vazio-status-label'),
                statusMessage: document.getElementById('vazio-status-message'),
                mainWire: document.getElementById('vazio-main-wire'),
                epsCard: document.getElementById('vazio-eps-card'),
                sutCard: document.getElementById('vazio-sut-card'),
                dutCard: document.getElementById('vazio-dut-card'),
            };

            const state = {
                powerLevel: 0,
                coreState: 'good',
                powerIntervalId: null
            };

            const CONFIG = {
                MAX_EPS_V: 480,
                MAX_EPS_I: 2000,
                MAX_EPS_KVA: 960,
                SUT_V_RATIO: 15.0 / 480,
                SUT_I_RATIO: 2000 / 64.0,
            };

            function calculateValues(level, coreState) {
                const p = level / 100;
                let epsV = 0, epsI = 0, sutOutV_kV = 0, sutOutI = 0;

                switch (coreState) {
                    case 'good':
                        epsV = CONFIG.MAX_EPS_V * p;
                        epsI = CONFIG.MAX_EPS_I * p;
                        sutOutV_kV = epsV * CONFIG.SUT_V_RATIO;
                        sutOutI = epsI / CONFIG.SUT_I_RATIO;
                        break;
                    case 'bad_current':
                        epsV = CONFIG.MAX_EPS_V * p * (1 - p * 0.8);
                        epsI = CONFIG.MAX_EPS_I * Math.pow(p, 0.3);
                        sutOutV_kV = epsV * CONFIG.SUT_V_RATIO * 0.1;
                        sutOutI = epsI / CONFIG.SUT_I_RATIO * 8;
                        break;
                    case 'bad_power':
                        epsV = CONFIG.MAX_EPS_V * p;
                        epsI = CONFIG.MAX_EPS_I * Math.pow(p, 1.8);
                        sutOutV_kV = (epsV * CONFIG.SUT_V_RATIO) * (1 - p * 0.5);
                        sutOutI = (epsI / CONFIG.SUT_I_RATIO) * (1 - p * 0.4);
                        break;
                }

                epsI = Math.min(epsI, CONFIG.MAX_EPS_I);
                const kva = Math.min((epsV * epsI) / 1000, CONFIG.MAX_EPS_KVA);

                return { epsV, epsI, kva, sutOutV_kV, sutOutI };
            }

            function updateSystem() {
                const values = calculateValues(state.powerLevel, state.coreState);
                
                elements.powerLevelDisplay.textContent = state.powerLevel;
                elements.epsVoltage.textContent = values.epsV.toFixed(1);
                elements.epsCurrent.textContent = values.epsI.toFixed(1);
                elements.epsKva.textContent = values.kva.toFixed(1);
                
                elements.sutInputVoltage.textContent = values.epsV.toFixed(1);
                elements.sutInputCurrent.textContent = values.epsI.toFixed(1);
                elements.sutOutputVoltage.textContent = values.sutOutV_kV.toFixed(1);
                elements.sutOutputCurrent.textContent = values.sutOutI.toFixed(1);

                elements.dutInputVoltage.textContent = values.sutOutV_kV.toFixed(1);
                elements.dutInputCurrent.textContent = values.sutOutI.toFixed(1);

                updateStatus(values);
                updateWire(values);
            }

            function updateWire(values) {
                elements.mainWire.className = 'vazio-wire'; // Reset
                if (state.powerLevel > 0) {
                    elements.mainWire.classList.add('vazio-animated-wire');
                    const isCritical = values.kva >= CONFIG.MAX_EPS_KVA || values.epsI >= CONFIG.MAX_EPS_I;
                    if (isCritical && state.coreState !== 'good') {
                        elements.mainWire.classList.add('vazio-wire-color-critical');
                    } else {
                        elements.mainWire.classList.add('vazio-wire-color-normal');
                    }
                }
            }

            function updateStatus(values) {
                elements.systemStatusCard.className = 'alert mb-0'; // Reset
                [elements.epsCard, elements.sutCard, elements.dutCard].forEach(card => card.classList.remove('vazio-critical-flash'));
                
                const isFailureMode = state.coreState !== 'good';
                const isCritical = values.kva >= CONFIG.MAX_EPS_KVA || values.epsI >= CONFIG.MAX_EPS_I;
                const isOverload = values.kva > CONFIG.MAX_EPS_KVA * 0.85;

                if (state.powerLevel === 0) {
                    elements.systemStatusCard.classList.add('alert-secondary');
                    elements.statusLabel.textContent = 'SISTEMA DESLIGADO';
                    elements.statusMessage.textContent = 'Ajuste a potência para iniciar a simulação.';
                } else if (isFailureMode && isCritical) {
                    elements.systemStatusCard.classList.add('alert-danger');
                    elements.statusLabel.textContent = 'FALHA CRÍTICA IMINENTE';
                    elements.statusMessage.textContent = `Limites da fonte excedidos! Potência: ${values.kva.toFixed(0)}kVA, Corrente: ${values.epsI.toFixed(0)}A.`;
                    [elements.epsCard, elements.sutCard, elements.dutCard].forEach(card => card.classList.add('vazio-critical-flash'));
                } else if (isFailureMode) {
                     elements.systemStatusCard.classList.add('alert-danger');
                    elements.statusLabel.textContent = 'NÚCLEO EM FALHA';
                    elements.statusMessage.textContent = 'O DUT está operando em modo de falha, demandando corrente/potência anormal.';
                } else if (isOverload) {
                    elements.systemStatusCard.classList.add('alert-warning');
                    elements.statusLabel.textContent = 'SOBRECARGA NA FONTE';
                    elements.statusMessage.textContent = `A potência da fonte (${values.kva.toFixed(0)} kVA) está acima de 85% da capacidade.`;
                } else {
                    elements.systemStatusCard.classList.add('alert-success');
                    elements.statusLabel.textContent = 'OPERAÇÃO NOMINAL';
                    elements.statusMessage.textContent = 'O sistema está operando dentro dos parâmetros esperados.';
                }
            }
            
            function changePower(amount) {
                state.powerLevel = Math.max(0, Math.min(100, state.powerLevel + amount));
                updateSystem();
            }

            function stopInterval() {
                clearInterval(state.powerIntervalId);
                state.powerIntervalId = null;
            }

            elements.btnUp.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(1), 50); });
            elements.btnDown.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(-1), 50); });
            document.addEventListener('mouseup', stopInterval);
            document.addEventListener('mouseleave', stopInterval);

            elements.coreSelector.addEventListener('change', (e) => {
                state.coreState = e.target.value;
                updateSystem();
            });
            
            updateSystem(); // Initial call
        });
    </script>
    
    <!-- Script para o Simulador "Carga" -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const elements = {
                btnUp: document.getElementById('carga-btn-up'), btnDown: document.getElementById('carga-btn-down'),
                powerLevelDisplay: document.getElementById('carga-power-level-display'),
                epsVoltage: document.getElementById('carga-eps-voltage'), epsCurrent: document.getElementById('carga-eps-current'),
                sutOutputVoltage: document.getElementById('carga-sut-output-voltage'), sutOutputCurrent: document.getElementById('carga-sut-output-current'),
                btnVCapUp: document.getElementById('carga-btn-v-cap-up'), btnVCapDown: document.getElementById('carga-btn-v-cap-down'),
                btnQCapUp: document.getElementById('carga-btn-q-cap-up'), btnQCapDown: document.getElementById('carga-btn-q-cap-down'),
                vCapDisplay: document.getElementById('carga-v-cap-display'), qCapDisplay: document.getElementById('carga-q-cap-display'),
                capacitorCurrentDisplay: document.getElementById('carga-capacitor-current-display'),
                epsStatusCard: document.getElementById('carga-eps-status-card'),
                epsStatusLabel: document.getElementById('carga-eps-status-label'),
                epsStatusMessage: document.getElementById('carga-eps-status-message'),
                wireMain: document.getElementById('carga-wire-main'),
                wireCap: document.getElementById('carga-wire-t-junction-cap'),
                wireDut: document.getElementById('carga-wire-t-junction-dut'),
            };

            const MAX_EPS_V = 480;
            const MAX_EPS_I_RATING = 2000;
            const SUT_VOLTAGE_RATIO = 14000 / 480;
            const I_DUT = 640.0;
            const capVoltageLevels = [3500, 7000, 14000];
            const capPowerLevels = [0, 2400, 4800, 7200, 9600, 12000, 14400];

            const state = {
                powerLevel: 0,
                capVIndex: 2,
                capQIndex: 0,
                powerIntervalId: null
            };

            function updateSimulation() {
                const V_eps = MAX_EPS_V * (state.powerLevel / 100);
                const V_sut_out = V_eps * SUT_VOLTAGE_RATIO;
                const V_nominal_cap = capVoltageLevels[state.capVIndex];
                const Q_nominal = capPowerLevels[state.capQIndex] * 1000;

                let I_capacitor = 0;
                if (V_sut_out > 0 && V_nominal_cap > 0) {
                    const Q_real = Q_nominal * Math.pow(V_sut_out / V_nominal_cap, 2);
                    I_capacitor = Q_real / V_sut_out;
                }
                
                const I_saida_SUT = (state.powerLevel > 0) ? (I_DUT - I_capacitor) : 0;
                const I_eps = I_saida_SUT * SUT_VOLTAGE_RATIO;
                
                elements.powerLevelDisplay.textContent = state.powerLevel;
                elements.epsVoltage.textContent = V_eps.toFixed(1);
                elements.epsCurrent.textContent = I_eps.toFixed(1);
                elements.sutOutputVoltage.textContent = (V_sut_out / 1000).toFixed(1);
                elements.sutOutputCurrent.textContent = I_saida_SUT.toFixed(1);
                elements.capacitorCurrentDisplay.textContent = I_capacitor.toFixed(1);
                elements.vCapDisplay.textContent = (V_nominal_cap / 1000).toFixed(1) + ' kV';
                elements.qCapDisplay.textContent = capPowerLevels[state.capQIndex] + ' kVAR';

                updateWireVisualization(I_eps, I_capacitor, I_saida_SUT);
                updateStatusCard(I_eps, I_saida_SUT);
            }

            function updateWireVisualization(I_eps, I_capacitor, I_saida_SUT) {
                [elements.wireMain, elements.wireCap, elements.wireDut].forEach(el => {
                    el.className = 'carga-wire';
                });

                if(state.powerLevel > 0) {
                    elements.wireMain.classList.add('carga-wire-color-source', 'carga-animated-wire');
                    if (I_saida_SUT < 0) {
                        elements.wireMain.classList.add('reverse');
                    }
                    if(I_capacitor > 0.1) {
                         elements.wireCap.classList.add('carga-wire-color-cap', 'carga-animated-wire');
                    }
                    elements.wireDut.classList.add('carga-wire-color-load', 'carga-animated-wire');
                }
            }

            function updateStatusCard(I_eps, I_saida_SUT) {
                elements.epsStatusCard.className = 'alert';
                const abs_I_eps = Math.abs(I_eps);

                if (state.powerLevel === 0) {
                    elements.epsStatusCard.classList.add('alert-secondary');
                    elements.epsStatusLabel.textContent = 'FONTE DESLIGADA';
                    elements.epsStatusMessage.textContent = 'Ajuste a potência para iniciar.';
                } else if (abs_I_eps > MAX_EPS_I_RATING) {
                    elements.epsStatusCard.classList.add('alert-danger');
                    elements.epsStatusLabel.textContent = 'SOBRECARGA CRÍTICA';
                    elements.epsStatusMessage.textContent = `Corrente da fonte (${abs_I_eps.toFixed(0)} A) excedeu o limite de ${MAX_EPS_I_RATING} A.`;
                } else if (I_saida_SUT < 0) {
                    elements.epsStatusCard.classList.add('alert-warning');
                    elements.epsStatusLabel.textContent = 'FLUXO REVERSO';
                    elements.epsStatusMessage.textContent = 'Capacitor está injetando mais corrente que a carga consome.';
                } else {
                     elements.epsStatusCard.classList.add('alert-success');
                    elements.epsStatusLabel.textContent = 'OPERAÇÃO NOMINAL';
                    const percentComp = (I_capacitor / I_DUT * 100).toFixed(0);
                    elements.epsStatusMessage.textContent = `Carga compensada em ${percentComp}%. Corrente da fonte: ${abs_I_eps.toFixed(0)} A.`;
                }
            }
            
            function changePower(amount) {
                state.powerLevel = Math.max(0, Math.min(100, state.powerLevel + amount));
                updateSimulation();
            }
            function createIndexChanger(key, levelsArray) {
                return function(change) {
                    let newIndex = state[key] + change;
                    if (newIndex >= 0 && newIndex < levelsArray.length) {
                        state[key] = newIndex;
                        updateSimulation();
                    }
                };
            }
            const changeCapV = createIndexChanger('capVIndex', capVoltageLevels);
            const changeCapQ = createIndexChanger('capQIndex', capPowerLevels);
            function stopInterval() { clearInterval(state.powerIntervalId); }

            elements.btnUp.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(1), 50); });
            elements.btnDown.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(-1), 50); });
            document.addEventListener('mouseup', stopInterval);
            document.addEventListener('mouseleave', stopInterval);
            elements.btnVCapUp.addEventListener('click', () => changeCapV(1));
            elements.btnVCapDown.addEventListener('click', () => changeCapV(-1));
            elements.btnQCapUp.addEventListener('click', () => changeCapQ(1));
            elements.btnQCapDown.addEventListener('click', () => changeCapQ(-1));
            
            updateSimulation();
        });
    </script>

</body>
</html>