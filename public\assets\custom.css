/* public/assets/custom.css - Versão Completa e Detalhada */

/* Cores Base - Variáveis globais que não mudam entre temas */
:root {
    /* Cores Principais (fixas) */
    --primary-color: #26427A; /* Azul Escuro do Dash */
    --secondary-color: #6c757d; /* Cinza Escuro */
    --success-color: #28a745; /* Verde */
    --info-color: #17a2b8; /* Azul Claro/Ciano */
    --warning-color: #ffc107; /* Amar<PERSON> */
    --danger-color: #dc3545; /* Vermelho */
    --light-color: #f8f9fa; /* Branco Sujo */
    --dark-color: #343a40; /* Preto Quase */
    --accent-color: var(--info-color); /* Usado como info para destaque */
    --fail-color: var(--danger-color); /* Cor para indicar falha */

    /* <PERSON><PERSON><PERSON> de Fonte Base - Adapte conforme a necessidade de densidade */
    --font-size-base: 0.875rem; /* ~14px */
    --font-size-sm: 0.75rem;    /* ~12px */
    --font-size-xs: 0.65rem;    /* ~10.4px */

    /* Alturas de Componentes */
    --input-height: 32px;
    --input-sm-height: 28px; /* Para inputs pequenos em Impulse */
}

/* Tema Escuro (padrão) - aplicado quando data-bs-theme="dark" ou não especificado */
:root,
[data-bs-theme="dark"] {
    /* Cores de Fundo e Bordas - Tema Escuro */
    --background-main: #1a1a1a; /* Fundo principal da aplicação */
    --background-card: #2c2c2c; /* Fundo dos cards e painéis */
    --background-card-header: #2a2a2a; /* Fundo do cabeçalho dos cards e sidebar */
    --background-input: #3d3d3d; /* Fundo dos campos de input e dropdowns */
    --background-card-light: #4F4F4F; /* Fundo mais claro para certas seções/tabelas (ex: Applied Voltage) */
    --background-table-row-even: #3D3D3D; /* Para linhas pares de tabelas */

    --border-color: #666666; /* Cor da borda geral */
    --border-strong: #999999; /* Cor da borda mais forte (HR) */

    /* Cores de Texto - Tema Escuro */
    --text-light: #f0f0f0; /* Texto principal claro */
    --text-dark: #333333; /* Texto escuro (para temas claros ou elementos específicos) */
    --text-header: #ffffff; /* Texto em cabeçalhos (branco puro) */
    --text-muted: #adb5bd; /* Texto secundário, atenuado */
    --text-placeholder: #adb5bd; /* Cor para placeholders de input */

    /* Variáveis RGB para uso com rgba() - Tema Escuro */
    --background-card-rgb: 44, 44, 44; /* #2c2c2c */
    --background-card-header-rgb: 42, 42, 42; /* #2a2a2a */
    --background-input-rgb: 61, 61, 61; /* #3d3d3d */
    --border-color-rgb: 102, 102, 102; /* #666666 */
    --text-light-rgb: 240, 240, 240; /* #f0f0f0 */
    --text-header-rgb: 255, 255, 255; /* #ffffff */
    --primary-rgb: 38, 66, 122; /* #26427A */
    --secondary-rgb: 108, 117, 125; /* #6c757d */
    --success-rgb: 40, 167, 69; /* #28a745 */
    --info-rgb: 23, 162, 184; /* #17a2b8 */
    --warning-rgb: 255, 193, 7; /* #ffc107 */
    --danger-rgb: 220, 53, 69; /* #dc3545 */

    /* Modificações para fundos de linhas de tabela (Applied Voltage Results) */
    .table-success {
        --bs-table-bg: rgba(var(--success-rgb), 0.2) !important; /* Fundo verde mais escuro e sutil */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
    }

    .table-warning {
        --bs-table-bg: rgba(var(--warning-rgb), 0.2) !important; /* Fundo amarelo/laranja mais escuro e sutil */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
    }

    /* Garante que as células individuais também herdem a cor do texto */
    .table-success td, .table-warning td {
        color: inherit !important;
    }

    /* INÍCIO DA MODIFICAÇÃO: Alertas de recomendação mais suaves para tema escuro */
    .alert.alert-info {
        background-color: rgba(var(--info-rgb), 0.2) !important; /* Fundo info mais suave */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
        border-color: var(--info-color) !important; /* Mantém a cor da borda original */
    }
    .alert.alert-warning {
        background-color: rgba(var(--warning-rgb), 0.2) !important; /* Fundo warning mais suave */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
        border-color: var(--warning-color) !important; /* Mantém a cor da borda original */
    }
    .alert.alert-danger {
        background-color: rgba(var(--danger-rgb), 0.2) !important; /* Fundo danger mais suave */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
        border-color: var(--danger-color) !important; /* Mantém a cor da borda original */
    }
    .alert.alert-success {
        background-color: rgba(var(--success-rgb), 0.2) !important; /* Fundo success mais suave */
        color: var(--text-light) !important; /* Garante que o texto seja claro */
        border-color: var(--success-color) !important; /* Mantém a cor da borda original */
    }
    /* FIM DA MODIFICAÇÃO */
}

/* Tema Claro - aplicado quando data-bs-theme="light" */
[data-bs-theme="light"] {
    /* Cores de Fundo e Bordas - Tema Claro */
    --background-main: #f8f9fa; /* Fundo principal da aplicação */
    --background-card: #ffffff; /* Fundo dos cards e painéis */
    --background-card-header: #26427A; /* Fundo do cabeçalho dos cards e sidebar */
    --background-input: #ffffff; /* Fundo dos campos de input e dropdowns */
    --background-card-light: #f1f3f4; /* Fundo mais claro para certas seções/tabelas */
    --background-table-row-even: #f8f9fa; /* Para linhas pares de tabelas */

    --border-color: #dee2e6; /* Cor da borda geral */
    --border-strong: #adb5bd; /* Cor da borda mais forte (HR) */

    /* Cores de Texto - Tema Claro */
    --text-light: #212529; /* Texto principal escuro */
    --text-dark: #212529; /* Texto escuro */
    --text-header: #ffffff; /* Texto em cabeçalhos (branco para contraste com fundo azul) */
    --text-muted: #6c757d; /* Texto secundário, atenuado */
    --text-placeholder: #6c757d; /* Cor para placeholders de input */

    /* Variáveis RGB para uso com rgba() - Tema Claro */
    --background-card-rgb: 255, 255, 255; /* #ffffff */
    --background-card-header-rgb: 38, 66, 122; /* #26427A */
    --background-input-rgb: 255, 255, 255; /* #ffffff */
    --border-color-rgb: 222, 226, 230; /* #dee2e6 */
    --text-light-rgb: 33, 37, 41; /* #212529 */
    --text-header-rgb: 255, 255, 255; /* #ffffff */
    --primary-rgb: 38, 66, 122; /* #26427A */
    --secondary-rgb: 108, 117, 125; /* #6c757d */
    --success-rgb: 40, 167, 69; /* #28a745 */
    --info-rgb: 23, 162, 184; /* #17a2b8 */
    --warning-rgb: 255, 193, 7; /* #ffc107 */
    --danger-rgb: 220, 53, 69; /* #dc3545 */
}

/* Base Body Styles */
html, body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-main);
    color: var(--text-light);
    margin: 0;
    padding: 0;
    font-size: var(--font-size-base);
    overflow-x: hidden; /* Previne scroll horizontal */
    width: 100%;
    max-width: 100vw; /* Garante que não exceda a viewport */
    transition: background-color 0.3s ease, color 0.3s ease; /* Transição suave entre temas */
}

/* Ajustes específicos para o tema claro */
[data-bs-theme="light"] .navbar-dark {
    background-color: var(--primary-color) !important;
}

[data-bs-theme="light"] .form-control,
[data-bs-theme="light"] .form-select {
    border: 1px solid var(--border-color);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.075);
}

[data-bs-theme="light"] .form-control:focus,
[data-bs-theme="light"] .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(38, 66, 122, 0.25);
}

/* Global Layout Fixes - Previne overflow horizontal */
* {
    box-sizing: border-box;
}

.container-fluid {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

.container-full-height {
    min-height: calc(100vh - 56px); /* Assumindo 56px para a altura da navbar */
    display: flex;
    flex-direction: column;
}

.row {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
}

.col, .col-*, [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    max-width: 100%;
}

/* Navbar Brand (Logo e Título) */
.navbar-brand {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.navbar-brand img {
    filter: drop-shadow(0px 2px 2px rgba(0,0,0,0.3));
    margin-right: 15px;
    vertical-align: middle;
    border-radius: 4px;
}

.navbar-brand h4 {
    font-size: 1.1rem;
    letter-spacing: 0.05rem;
    text-shadow: 0px 1px 2px rgba(0,0,0,0.3);
    line-height: 1.2;
    color: var(--text-light);
    margin: 0;
}

.navbar-brand div {
    font-size: 0.7rem;
    color: rgba(255,255,255,0.85);
    letter-spacing: 0.03rem;
}

.navbar-brand div span {
    color: var(--accent-color); /* Usa a cor de destaque para o EPS 1500 */
}

/* Navbar responsiva - ajustes para telas pequenas */
@media (max-width: 991.98px) {
    .navbar-brand h4 {
        font-size: 0.95rem;
    }
    
    .navbar-brand div {
        font-size: 0.6rem;
    }
    
    .navbar .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .navbar-brand img {
        height: 35px;
        margin-right: 10px;
    }
}

@media (max-width: 767.98px) {
    .navbar-brand h4 {
        font-size: 0.85rem;
    }
}

/* Container de ações da navbar */
.navbar-actions-container {
    max-width: 100%;
    overflow: hidden;
    flex-shrink: 1;
}

@media (max-width: 991.98px) {
    .navbar-actions-container {
        width: 100%;
        justify-content: center;
        margin-top: 0.5rem;
        border-top: 1px solid rgba(255,255,255,0.1);
        padding-top: 0.5rem;
    }
}

/* General Link Styles */
a {
    color: var(--primary-color);
    text-decoration: none;
}
a:hover {
    color: lighten(var(--primary-color), 10%);
    text-decoration: underline;
}

/* --- Card Styles --- */
.card {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25); /* Mais pronunciado */
    border-radius: 8px; /* Mais arredondado */
    overflow: hidden; /* Garante que o header arredondado funcione bem */
}

.card-header {
    background-color: var(--background-card-header);
    color: var(--text-header);
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    border-radius: 7px 7px 0 0; /* Arredondado só no topo */
    font-weight: bold;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.card-body {
    padding: 1rem;
    color: var(--text-light);
}

/* Specific Card Headers for modules */
.card-header h5, .card-header h6 {
    margin: 0;
    color: inherit; /* Garante que use a cor do header */
    font-size: 1rem; /* Padrão para cabeçalhos de card */
}
/* Específico para Transformer Inputs, Impulse (title) */
.card-header.bg-primary-custom { /* Adicionar esta classe no card-header do HTML */
    background-color: var(--primary-color);
    color: var(--text-header);
}
.card-header.bg-info-custom {
    background-color: var(--info-color);
    color: var(--text-header);
}
.card-header.bg-secondary-custom {
    background-color: var(--secondary-color);
    color: var(--text-header);
}

/* --- Input and Select Styles --- */
.form-control,
.form-select,
.input-group-text {
    background-color: var(--background-input);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    font-size: var(--font-size-base);
    height: var(--input-height);
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border-radius: 4px;
}

/* Wider input fields for losses module */
.form-control.losses-input-wide,
.form-select.losses-input-wide {
    width: 50% !important; /* Make input fields take 50% width of their container */
    min-width: 120px; /* Ensure minimum width for better usability */
}

/* Pequenos inputs (para Impulse) */
.form-control.form-control-sm,
.form-select.form-select-sm,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
    height: var(--input-sm-height);
    font-size: var(--font-size-sm);
    padding: 0.25rem 0.5rem;
}


.form-control::placeholder,
.form-select::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-control:focus,
.form-select:focus {
    background-color: var(--background-input);
    color: var(--text-light);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(38, 66, 122, 0.25); /* Cor primária do Dash com 25% de opacidade */
}

/* Readonly / Disabled Inputs */
.form-control:disabled,
.form-control[readonly],
.form-select:disabled,
.form-select[readonly] {
    background-color: rgba(var(--background-input-rgb), 0.5); /* Usar RGB se disponível, senão direto */
    background-color: #333333; /* Fallback */
    color: var(--text-muted);
    opacity: 0.8;
}

/* Dark Dropdown Style */
.form-select.dark-dropdown {
    background-color: var(--background-input);
    color: var(--text-light);
    border-color: var(--border-color);
}
.form-select.dark-dropdown option {
    background-color: var(--background-input);
    color: var(--text-light);
}

/* Input Group Spinners */
.input-group-text .fas {
    cursor: pointer;
    padding: 0 5px;
    color: var(--text-muted);
    transition: color 0.15s ease-in-out;
}
.input-group-text .fas:hover {
    color: var(--accent-color);
}

/* --- Labels --- */
label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

/* --- SUT/EPS Tap Ideal Styles --- */
.sut-tap-ideal {
    background-color: rgba(255, 193, 7, 0.15) !important; /* Amarelo suave para destaque */
    border-left: 3px solid var(--warning-color) !important; /* Borda amarela para indicar tap ideal */
}

[data-bs-theme="light"] .sut-tap-ideal {
    background-color: rgba(255, 193, 7, 0.1) !important; /* Amarelo mais suave no tema claro */
    color: var(--text-dark) !important; /* Texto escuro no tema claro */
}

.sut-tap-ideal .sut-tap-voltage {
    font-weight: bold !important;
    color: var(--warning-color) !important; /* Cor amarela para o valor do tap */
}

/* --- Button Styles (replicated from Dash) --- */
.btn {
    font-size: var(--font-size-sm);
    padding: 0.4rem 0.8rem; /* Ajustado para ser mais compacto */
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    line-height: 1.5; /* Para centralizar texto em botões menores */
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-header);
}
.btn-primary:hover {
    filter: brightness(0.9); /* Escurece ligeiramente no hover */
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-header);
}
.btn-secondary:hover {
    filter: brightness(0.9);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-header);
}
.btn-info:hover {
    filter: brightness(0.9);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-header);
}
.btn-success:hover {
    filter: brightness(0.9);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-header);
}
.btn-danger:hover {
    filter: brightness(0.9);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color); /* Texto escuro para contraste no amarelo */
}
.btn-warning:hover {
    filter: brightness(0.9);
}

.btn-outline-info { /* Para "Sugerir Resistores" */
    border-color: var(--info-color);
    color: var(--info-color);
    background-color: transparent;
}
.btn-outline-info:hover {
    background-color: var(--info-color);
    color: var(--text-header);
    filter: none; /* Remove filter para hover de outline */
}

.btn-sm {
    font-size: var(--font-size-sm);
    padding: 0.25rem 0.5rem;
}


/* --- Tipografia --- */
.section-title {
    font-size: var(--font-size-base); /* Ajustado para ser mais compacto */
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 0.8rem; /* Reduzido */
    padding-bottom: 0.4rem; /* Reduzido */
    border-bottom: 1px solid rgba(var(--border-color-rgb), 0.3); /* Usando RGB variable */
}

.subsection-title {
    font-size: var(--font-size-sm); /* Reduzido */
    font-weight: bold;
    color: var(--info-color);
    text-align: center;
    margin-top: 0.8rem;
    margin-bottom: 0.6rem;
    padding-bottom: 0.2rem;
    border-bottom: 1px dotted rgba(var(--border-color-rgb), 0.2); /* Usando RGB variable */
}

small.text-muted {
    font-size: var(--font-size-xs);
    color: var(--text-muted) !important;
}

/* Adicionado para o template transformer_info_panel.html */
.transformer-table {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Adicionado para o template transformer_info_panel.html */
.info-card-header span,
.transformer-table th,
.transformer-table td {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* --- Alertas e Mensagens --- */
.alert {
    font-size: var(--font-size-sm);
    padding: 0.6rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.75rem; /* Padrão Bootstrap */
}
/* Estilos padrão dos alertas, que serão sobrescritos no tema escuro */
.alert.alert-info {
    background-color: var(--info-color);
    color: var(--text-header);
    border-color: var(--info-color);
}
.alert.alert-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border-color: var(--warning-color);
}
.alert.alert-danger {
    background-color: var(--danger-color);
    color: var(--text-header);
    border-color: var(--danger-color);
}
.alert.alert-success {
    background-color: var(--success-color);
    color: var(--text-header);
    border-color: var(--success-color);
}

.alert.alert-light-custom { /* Para notas informativas */
    background-color: var(--background-card-header); /* Cor do card-header para notas */
    color: var(--text-light);
    border-color: var(--border-color);
}
.alert.alert-light-custom .small {
    font-size: var(--font-size-sm);
}

/* Ajustes específicos para alertas no tema claro */
[data-bs-theme="light"] .alert.alert-light-custom {
    background-color: #e9ecef;
    color: var(--text-light);
    border-color: var(--border-color);
}

/* Ajustes para botões no tema claro */
[data-bs-theme="light"] .btn-outline-light {
    border-color: var(--border-color);
    color: var(--text-light);
}

[data-bs-theme="light"] .btn-outline-light:hover {
    background-color: var(--border-color);
    border-color: var(--border-color);
    color: var(--text-light);
}

/* Ajustes para tabelas no tema claro */
[data-bs-theme="light"] .table {
    --bs-table-color: var(--text-light);
    --bs-table-bg: var(--background-card);
    --bs-table-border-color: var(--border-color);
    --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
    --bs-table-active-bg: rgba(0, 0, 0, 0.1);
    --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
}

/* Ajustes para cards no tema claro */
[data-bs-theme="light"] .card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ajustes para inputs no tema claro - melhor contraste */
[data-bs-theme="light"] .form-control::placeholder,
[data-bs-theme="light"] .form-select option {
    color: var(--text-placeholder);
}

[data-bs-theme="light"] .results-placeholder,
[data-bs-theme="light"] #legend-observations-no-load-area,
[data-bs-theme="light"] #resultados-perdas-carga,
[data-bs-theme="light"] .section-subtitle,
[data-bs-theme="light"] .alert-light {
    color: var(--text-light); /* Ensure text in these areas is readable in light mode */
}

[data-bs-theme="light"] .alert-light {
    background-color: #f8f9fa; /* Light background for alerts in light theme */
    border-color: #dee2e6;
}

/* --- Tables --- */
.table {
    --bs-table-color: var(--text-light);
    --bs-table-bg: var(--background-card);
    --bs-table-border-color: var(--border-color);
    --bs-table-striped-bg: rgba(var(--text-light-rgb), 0.05); /* Usando RGB variable */
    --bs-table-active-bg: rgba(var(--text-light-rgb), 0.1);
    --bs-table-hover-bg: rgba(var(--text-light-rgb), 0.075);
    color: var(--text-light); /* Garante que o texto da tabela use a variável */
}

.table thead th {
    background-color: var(--background-card-header);
    color: var(--text-header);
    border-color: var(--border-color);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 0.5rem 0.75rem;
}

.table tbody td {
    font-size: var(--font-size-sm);
    padding: 0.4rem 0.75rem;
    vertical-align: middle;
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--background-table-row-even); /* Cor alternativa */
}


/* ========== IMPULSE PAGE LAYOUT IMPROVEMENTS ========== */

/* Impulse page specific container fixes */
.impulse-tab-content {
    min-height: 300px; /* Ensure minimum height for tab content */
    overflow-y: auto; /* Allow scrolling if content exceeds height */
}

.impulse-tab-content .tab-pane {
    height: 100%; /* Ensure tab panes fill their container */
}

/* Graph container improvements */
.graph-container-current {
    min-height: 400px; /* Minimum height for graph area */
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 1rem;
}

/* Better spacing for impulse form elements */
.impulse-form-row {
    margin-bottom: 0.75rem;
}

/* Fix for nested card layouts in impulse page */
.card .card {
    margin-bottom: 0.5rem;
}

.card .card:last-child {
    margin-bottom: 0;
}

/* Improved responsive behavior for impulse columns */
@media (max-width: 1200px) {
    .col-md-3, .col-md-5, .col-md-4 {
        margin-bottom: 1rem;
    }
}

/* Enhanced tab navigation for impulse results */
.custom-tabs .nav-link {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

.custom-tabs .nav-link.active {
    background-color: var(--background-card-header);
    border-color: var(--border-color);
    color: var(--text-header);
}

/* Better alert styling in tab content */
.tab-info-alert {
    margin-bottom: 0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

/* Improve form label spacing in impulse page */
.impulse-form-label {
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-light);
}

/* Fix for input group button spacing */
.input-group-text {
    display: flex;
    flex-direction: column;
    padding: 0.25rem;
}

.input-group-text i {
    font-size: 0.7rem;
    line-height: 1;
    margin: 1px 0;
    cursor: pointer;
}

.input-group-text i:hover {
    color: var(--accent-color);
}

/* Light mode specific fixes for impulse page */
[data-bs-theme="light"] .graph-container-current {
    background-color: var(--background-card);
    border-color: var(--border-color);
}

[data-bs-theme="light"] .impulse-form-label,
[data-bs-theme="light"] .form-label {
    color: var(--text-light);
}

[data-bs-theme="light"] .custom-tabs .nav-link {
    color: var(--text-light);
    border-color: var(--border-color);
}

[data-bs-theme="light"] .custom-tabs .nav-link.active {
    background-color: var(--background-card);
    color: var(--text-header);
}

[data-bs-theme="light"] .tab-info-alert {
    background-color: rgba(var(--info-rgb), 0.1);
    border-color: var(--info-color);
    color: var(--text-light);
}

/* Fix for input group text in light mode */
[data-bs-theme="light"] .input-group-text {
    background-color: var(--background-input);
    border-color: var(--border-color);
    color: var(--text-light);
}

[data-bs-theme="light"] .input-group-text i:hover {
    color: var(--accent-color);
}

/* Better visibility for help text in light mode */
[data-bs-theme="light"] .form-text.text-muted,
[data-bs-theme="light"] .gap-help-text,
[data-bs-theme="light"] .si-help-text {
    color: var(--text-muted) !important;
}

/* --- Utilitários de Espaçamento (Bootstrap-like) --- */
.g-0 { --bs-gutter-x: 0; --bs-gutter-y: 0; }
.g-1 { --bs-gutter-x: 0.25rem; --bs-gutter-y: 0.25rem; }
.g-2 { --bs-gutter-x: 0.5rem; --bs-gutter-y: 0.5rem; }
.g-3 { --bs-gutter-x: 1rem; --bs-gutter-y: 1rem; }

.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }

.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }

.pe-1 { padding-right: 0.25rem !important; } /* Padding End */
.ps-1 { padding-left: 0.25rem !important; }  /* Padding Start */

/* Margens */
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-05 { margin-bottom: 0.5rem; } /* Nova classe utilitária */

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }

/* --- Outros Utilitários --- */
.h-100 { height: 100% !important; }
.w-100 { width: 100% !important; }
.d-flex { display: flex !important; }
.d-inline-block { display: inline-block !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; } /* Para botões alinhados ao final da coluna */
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }
.flex-column { flex-direction: column !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.float-end { float: right !important; } /* Cuidado com floats em Flexbox/Grid - preferir flexbox */

.text-center { text-align: center !important; }
.text-end { text-align: right !important; }
.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--primary-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }

.fw-bold { font-weight: bold !important; }
.fs-6 { font-size: 1rem !important; } /* Bootstrap 5 default */

.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-end { border-right: 1px solid var(--border-color) !important; }

/* --- Classes Utilitárias para Padronização de Estilos Inline --- */

/* Tamanhos de fonte específicos */
.font-size-1rem { font-size: 1rem !important; }
.font-size-11rem { font-size: 1.1rem !important; }
.font-size-095rem { font-size: 0.95rem !important; }
.font-size-09rem { font-size: 0.9rem !important; }
.font-size-085rem { font-size: 0.85rem !important; }
.font-size-08rem { font-size: 0.8rem !important; }
.font-size-075rem { font-size: 0.75rem !important; }
.font-size-07rem { font-size: 0.7rem !important; }
.font-size-065rem { font-size: 0.65rem !important; }

/* Pesos de fonte */
.fw-500 { font-weight: 500 !important; }
.fw-600 { font-weight: 600 !important; }

/* Classes para cores de texto específicas */
.text-header-custom { color: var(--text-header) !important; }
.text-light-custom { color: var(--text-light) !important; }

/* Classes para espaçamento personalizado */
.padding-8px { padding: 8px !important; }
.padding-12px { padding: 12px !important; }
.min-height-150px { min-height: 150px !important; }
.min-height-200px { min-height: 200px !important; }

/* Classes para overflow */
.overflow-y-auto { overflow-y: auto !important; }
.overflow-visible { overflow: visible !important; }

/* Classes para max-height específicas */
.max-height-unset { max-height: unset !important; }
.max-height-400px { max-height: 400px !important; }

/* Classes para box-shadow específicas */
.box-shadow-light { box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important; }
.box-shadow-medium { box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important; }

/* Classes para border-radius específicas */
.border-radius-3px { border-radius: 3px !important; }
.border-radius-bottom-3px { border-radius: 0 0 3px 3px !important; }

/* Classes para largura específicas */
.width-75-percent { width: 75% !important; }
.width-100-percent { width: 100% !important; }

/* Classes para altura de linha */
.line-height-12 { line-height: 1.2 !important; }
.line-height-15 { line-height: 1.5 !important; }

/* Classes para espaçamento de letras */
.letter-spacing-005rem { letter-spacing: 0.05rem !important; }
.letter-spacing-003rem { letter-spacing: 0.03rem !important; }

/* Classes para text-shadow */
.text-shadow-subtle { text-shadow: 0px 1px 2px rgba(0,0,0,0.3) !important; }

/* Classes para transparência de cores */
.text-opacity-85 { color: rgba(var(--text-header-rgb), 0.85) !important; }

/* Classes para flex direction */
.flex-direction-column { flex-direction: column !important; }

/* Classes para margin específicas */
.margin-right-15px { margin-right: 15px !important; }
.margin-right-10px { margin-right: 10px !important; }
.margin-bottom-0 { margin-bottom: 0 !important; }

/* Classes para altura específicas */
.height-40px { height: 40px !important; }
.height-35px { height: 35px !important; }
.height-15px { height: 15px; }

/* Classes para display e alinhamento */
.display-flex-align-center {
    display: flex !important;
    align-items: center !important;
}

/* Classes específicas para módulos */
.navbar-brand-text {
    font-size: 1.1rem;
    letter-spacing: 0.05rem;
    text-shadow: 0px 1px 2px rgba(0,0,0,0.3);
    line-height: 1.2;
}

/* Estilos para o painel de informações do transformador */
.transformer-info-panel .card-body {
    font-size: 0.72rem;
    line-height: 1.4; /* Aumenta um pouco o line-height para melhor legibilidade */
    padding: 0.5rem !important; /* Ajusta padding do body do card */
}

.transformer-info-panel .info-group {
    padding: 4px 8px;
    margin-bottom: 8px;
    border-radius: 4px;
}

.transformer-info-panel .info-group-general {
    background-color: rgba(var(--info-rgb), 0.08);
    border-left: 3px solid var(--info-color);
}

.transformer-info-panel .info-group-pesos {
    background-color: rgba(var(--secondary-rgb), 0.08);
    border-left: 3px solid var(--secondary-color);
}

.transformer-info-panel .info-group-at {
    background-color: rgba(var(--primary-rgb), 0.08);
    border-left: 3px solid var(--primary-color);
}

.transformer-info-panel .info-group-bt {
    background-color: rgba(var(--success-rgb), 0.08);
    border-left: 3px solid var(--success-color);
}

.transformer-info-panel .info-group-terciario {
    background-color: rgba(var(--warning-rgb), 0.08);
    border-left: 3px solid var(--warning-color);
}

.transformer-info-panel .info-group-ensaios {
    background-color: rgba(var(--info-rgb), 0.08);
    border-left: 3px solid var(--info-color);
}

.transformer-info-panel .info-group-title {
    font-weight: bold;
    margin-bottom: 4px;
    display: block; /* Garante que o título ocupe a linha */
    font-size: 0.8rem; /* Título do grupo um pouco maior */
}

.transformer-info-panel .info-item {
    margin-bottom: 4px; /* Espaçamento entre os itens de informação */
}

.transformer-info-panel .info-item strong {
    margin-right: 4px; /* Espaçamento entre o label e o valor */
    color: var(--text-muted); /* Cor mais suave para os labels */
}

.transformer-info-panel .info-divider {
    border-top: 1px solid var(--border-color);
    margin: 8px 0;
}

.transformer-info-panel .info-subgroup-title {
    font-weight: bold;
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 4px;
    margin-bottom: 2px;
    padding-bottom: 2px; /* Adiciona padding para separar da linha pontilhada */
    border-bottom: 1px dotted rgba(var(--border-color-rgb), 0.2);
}

/* Ajustes para o card header do painel de info */
.transformer-info-panel .card-header {
    padding: 0.3rem 0.75rem !important; /* Ajusta padding */
    font-size: 0.8rem !important; /* Ajusta tamanho da fonte */
}

.transformer-info-panel .card-header span {
    flex-grow: 1; /* Permite que o texto do título ocupe o espaço */
    text-align: center;
}

/* Estilo para os valores exibidos */
.transformer-info-panel .info-value {
    font-weight: normal; /* Garante que o valor não seja negrito */
    color: var(--text-light); /* Cor normal do texto */
}

/* Utility classes for transformer info panel */
.info-card-main {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
}

.info-card-header {
    background-color: var(--background-card-header);
    color: var(--text-header);
    font-size: 0.84rem;
    font-weight: bold;
}

.info-card-body {
    font-size: 0.72rem;
    line-height: 1.2;
}

/* Tabela simples para o painel de informações do transformador */
.transformer-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Cabeçalho */
.transformer-table thead th {
    background-color: var(--background-card-header);
    color: var(--text-header);
    padding: 4px 6px;
    border: 1px solid var(--border-color);
    text-align: center;
    font-size: 0.68rem;
    font-weight: bold;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header-row th {
    background-color: var(--background-card-header);
}

.subheader-row th {
    background-color: rgba(var(--background-card-header-rgb), 0.7);
    color: var(--text-muted);
    font-size: 0.64rem;
    padding: 2px 4px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Divisórias grossas entre seções principais */
.divider-thick {
    background-color: var(--border-color);
    width: 3px !important;
    min-width: 3px !important;
    max-width: 3px !important;
    padding: 0 !important;
    border-left: 2px solid var(--border-color);
    border-right: 2px solid var(--border-color);
}

/* Colunas específicas */
.col-general { background-color: rgba(var(--info-rgb), 0.1); }
.col-at { background-color: rgba(var(--primary-rgb), 0.1); }
.col-bt { background-color: rgba(var(--success-rgb), 0.1); }
.col-terc { background-color: rgba(var(--warning-rgb), 0.1); }
.col-pesos { background-color: rgba(var(--secondary-rgb), 0.1); }

/* Sub-cabeçalhos */
.sub-at { background-color: rgba(var(--primary-rgb), 0.05); }
.sub-bt { background-color: rgba(var(--success-rgb), 0.05); }
.sub-terc { background-color: rgba(var(--warning-rgb), 0.05); }

/* Linhas de dados */
.transformer-table tbody td {
    padding: 4px 6px;
    border: 1px solid rgba(var(--border-color-rgb), 0.3);
    text-align: left;
    font-size: var(--font-size-sm);
    vertical-align: middle;
    white-space: nowrap;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.label-cell {
    background-color: rgba(var(--info-rgb), 0.05);
    text-align: left !important;
    font-weight: 500;
    color: var(--text-light);
    white-space: nowrap;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-size-sm);
}

.value-cell {
    color: var(--text-light);
    min-width: 60px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-size-sm);
}

.value-cell span {
    font-weight: normal;
    color: var(--text-light);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Alternância de cores nas linhas */
.data-row:nth-child(odd) {
    background-color: rgba(var(--background-card-rgb), 0.02);
}

.data-row:nth-child(even) {
    background-color: rgba(var(--background-card-rgb), 0.05);
}

/* Responsividade */
@media (max-width: 1200px) {
    .transformer-table {
        font-size: 0.65rem;
    }

    .transformer-table thead th {
        padding: 3px 4px;
        font-size: 0.64rem;
    }

    .transformer-table tbody td {
        padding: 2px 4px;
        font-size: 0.62rem;
    }
}

/* Nova tabela limpa */
.transformer-table-clean {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.7rem;
    border: 2px solid var(--border-color);
    margin: 0;
}

.transformer-table-clean thead th {
    background-color: var(--background-card-header);
    color: var(--text-header);
    padding: 6px 4px;
    border: 1px solid var(--border-color);
    text-align: center;
    font-size: 0.68rem;
    font-weight: bold;
    vertical-align: middle;
}

.transformer-table-clean .subheader th {
    background-color: rgba(var(--background-card-header-rgb), 0.7);
    color: var(--text-muted);
    font-size: 0.64rem;
    padding: 3px 2px;
    font-weight: normal;
}

.transformer-table-clean tbody td {
    padding: 4px 3px;
    border: 1px solid var(--border-color);
    text-align: center;
    font-size: 0.64rem;
    vertical-align: top;
    line-height: 1.2;
}

.transformer-table-clean .label {
    background-color: rgba(var(--info-rgb), 0.08);
    text-align: left;
    font-weight: bold;
    color: var(--text-muted);
    padding: 4px 6px;
}

.transformer-table-clean .data {
    background-color: rgba(var(--background-card-rgb), 0.02);
    font-family: 'Courier New', monospace;
    color: var(--text-light);
    min-width: 60px;
}

.transformer-table-clean .data span {
    font-weight: bold;
    color: var(--text-primary);
}

/* Cores das colunas */
.col-general { background-color: rgba(var(--info-rgb), 0.15); }
.col-at { background-color: rgba(var(--primary-rgb), 0.15); }
.col-bt { background-color: rgba(var(--success-rgb), 0.15); }
.col-terc { background-color: rgba(var(--warning-rgb), 0.15); }
.col-pesos { background-color: rgba(var(--secondary-rgb), 0.15); }

/* Alternância de linhas */
.transformer-table-clean tbody tr:nth-child(odd) {
    background-color: rgba(var(--background-card-rgb), 0.03);
}

.transformer-table-clean tbody tr:nth-child(even) {
    background-color: rgba(var(--background-card-rgb), 0.06);
}

.info-divider-vertical {
    width: 1px;
    background-color: var(--border-color);
    margin: 0 4px;
    min-height: 100px;
}

/* Estilos para dados herdados no painel de informações */
.inherited-data-highlight {
    background-color: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 3px !important;
    padding: 1px 3px !important;
    font-weight: 600 !important;
    color: #856404 !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

/* Modo escuro para dados herdados */
[data-bs-theme="dark"] .inherited-data-highlight {
    background-color: #664d03 !important;
    border: 1px solid #997404 !important;
    color: #ffda6a !important;
    box-shadow: 0 1px 2px rgba(255,255,255,0.1) !important;
}

.inherited-field-container {
    cursor: help;
    position: relative;
}

.inherited-field-container:hover .inherited-data-highlight {
    background-color: #ffeaa7 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.15) !important;
    transform: scale(1.02);
    transition: all 0.2s ease;
}

/* Hover no modo escuro */
[data-bs-theme="dark"] .inherited-field-container:hover .inherited-data-highlight {
    background-color: #997404 !important;
    box-shadow: 0 2px 4px rgba(255,255,255,0.15) !important;
}

.inheritance-indicator {
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.inheritance-indicator.text-success {
    color: #198754 !important;
}

.inheritance-indicator.text-warning {
    color: #fd7e14 !important;
}

/* Modo escuro para indicadores */
[data-bs-theme="dark"] .inheritance-indicator.text-success {
    color: #75b798 !important;
}

[data-bs-theme="dark"] .inheritance-indicator.text-warning {
    color: #fd7e14 !important;
}

.inheritance-indicator i {
    font-size: 0.7rem;
}

.info-divider-horizontal {
    height: 1px;
    background-color: rgba(var(--border-color-rgb), 0.3);
    margin: 2px 0;
    width: 100%;
}

.info-section-title {
    font-size: 0.75rem;
    font-weight: bold;
    margin-bottom: 4px;
    padding-bottom: 2px;
    border-bottom: 1px dotted rgba(var(--border-color-rgb), 0.3);
}

.info-section-subtitle {
    font-size: 0.7rem;
    font-weight: bold;
    margin-bottom: 2px;
    color: var(--text-muted);
}

.info-item {
    font-size: 0.68rem;
    line-height: 1.2;
    margin-bottom: 1px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1px 0;
}

.info-item strong {
    margin-right: 4px;
    color: var(--text-muted);
    white-space: nowrap;
    min-width: fit-content;
}

/* Grids internos para AT e Terciário */
.at-content-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 8px;
    align-items: start;
    width: 100%;
}

.at-main-data {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.at-taps-data {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding-left: 8px;
    border-left: 1px dotted rgba(var(--primary-rgb), 0.3);
}

.terciario-content-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 8px;
    align-items: start;
    width: 100%;
}

.terciario-main-data {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.pesos-data {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding-left: 8px;
    border-left: 1px dotted rgba(var(--warning-rgb), 0.3);
}

.info-group-general {
    background-color: rgba(var(--info-rgb), 0.08);
    border-left: 3px solid var(--info);
    padding: 2px 4px;
}

.info-group-pesos {
    background-color: rgba(var(--secondary-rgb), 0.08);
    border-left: 3px solid var(--secondary);
    padding: 2px 4px;
}

.info-group-at {
    background-color: rgba(var(--primary-rgb), 0.08);
    border-left: 3px solid var(--primary);
    padding: 2px 4px;
}

.info-group-at-taps {
    background-color: rgba(var(--primary-rgb), 0.04);
    border-left: 3px solid rgba(var(--primary-rgb), 0.5);
    padding: 2px 4px;
    margin-left: 8px;
    margin-bottom: 1px;
}

.info-group-bt {
    background-color: rgba(var(--success-rgb), 0.08);
    border-left: 3px solid var(--success);
    padding: 2px 4px;
}

.info-group-terciario {
    background-color: rgba(var(--warning-rgb), 0.08);
    border-left: 3px solid var(--warning);
    padding: 2px 4px;
}

.info-group-ensaios {
    background-color: rgba(var(--info-rgb), 0.08);
    border-left: 3px solid var(--info);
    padding: 2px 4px;
}

.info-divider-main {
    border-top: 1px solid var(--border-color);
    margin: 2px 0;
}

.info-divider-primary {
    border-top: 1px solid rgba(var(--primary-rgb), 0.3);
    margin: 2px 0;
}

.info-divider-warning {
    border-top: 1px solid rgba(var(--warning-rgb), 0.3);
    margin: 2px 0;
}

.info-divider-info {
    border-top: 1px solid rgba(var(--info-rgb), 0.3);
    margin: 2px 0;
}

.info-title-general {
    color: var(--info);
}

.info-title-pesos {
    color: var(--secondary);
}

.info-title-at {
    color: var(--primary);
}

.info-title-at-taps {
    color: var(--primary);
    font-size: 0.68rem;
}

.info-title-bt {
    color: var(--success);
}

.info-title-terciario {
    color: var(--warning);
}

.info-title-ensaios {
    color: var(--info);
}

/* Ajustes de layout para o módulo de Perdas */
.losses-tab-content-custom {
    display: flex; /* Garante que o container das abas seja flex */
    flex-direction: column; /* Organiza os filhos (tab-panes) em coluna */
    flex-grow: 1; /* Faz este container crescer para preencher o card-body */
    /* IMPORTANTE: Adicionar overflow: hidden pode ajudar se o conteúdo interno estiver causando problemas,
       mas pode cortar conteúdo se não for bem gerenciado. Teste se necessário. */
    /* overflow: hidden; */
}

/* Estilo padrão para todas as tab-panes DENTRO de .losses-tab-content-custom */
.losses-tab-content-custom > .tab-pane {
    /* Por padrão, Bootstrap esconde abas inativas (geralmente com display: none).
       Não precisamos definir display: flex ou flex-grow aqui para todas as abas,
       pois isso conflitaria com o Bootstrap. */
    /* As classes d-flex flex-column flex-grow-1 já estão no HTML das tab-panes.
       A regra CSS abaixo para .show.active irá garantir que essas propriedades
       sejam aplicadas de forma robusta quando a aba estiver ativa. */
    position: relative;
}

/* Estilo específico para a tab-pane ATIVA e VISÍVEL */
.losses-tab-content-custom > .tab-pane.show.active {
    /* Garante que a aba ativa se comporte como um container flex que cresce.
       O !important é usado para garantir prioridade sobre outros estilos do Bootstrap
       ou classes de utilidade que possam estar sendo aplicadas de forma conflitante. */
    display: flex !important;
    flex-direction: column !important;
    flex-grow: 1 !important;
    /* height: 100%; */ /* Em geral, flex-grow: 1 é suficiente. Height: 100% pode ser
                           tentado se flex-grow sozinho não estiver funcionando como esperado,
                           mas certifique-se de que os pais têm alturas definidas ou também crescem. */
}


/* Ajustes para o card de Perdas */
.losses-card {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15); /* Sombra mais sutil */
    border-radius: 8px; /* Arredondamento mais suave */
}   

/* Utility class for card body minimum height in losses.html */
.card-body-min-height { min-height: 200px; }

/* Additional utility classes for remaining inline styles */
.border-right-main {
    border-right: 1px solid var(--border-color);
}

.border-right-main-px {
    border-right: 1px solid var(--border-color);
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}

.input-readonly {
    background-color: var(--background-card-header);
    cursor: default;
}

.hr-strong {
    margin: 0.8rem 0;
    border-top: 2px solid var(--border-strong-color);
}

.dummy-output {
    display: none;
}

.section-subtitle-spaced {
    margin-top: 1rem;
}

.formula-text-compact {
    margin-bottom: 0;
}

.display-none {
    display: none;
}

.upload-area-standards {
    width: 100%;
    height: 60px;
    line-height: 60px;
    border-width: 1px;
    border-style: dashed;
    border-radius: 5px;
    text-align: center;
    margin: 10px 0;
    background-color: var(--background-card);
    border-color: var(--border-color);
}

/* Utility classes for short_circuit.html */
.hidden { display: none; }
.mb-05 { margin-bottom: 0.5rem; }
/* .text-primary { color: var(--primary-color); font-size: 1rem; }  <- Removido para evitar conflito com utilitário global */
.text-fail { color: var(--fail-color); font-size: 1rem; }

/* Utility classes for induced_voltage.html */
.alert-info-custom { font-size: 0.75rem; display: flex; align-items: center; }
.label-nowrap { white-space: nowrap; }
.header-bold { font-size: 1rem; font-weight: bold; color: var(--text-header); }

/* Utility classes for impulse.html */
.container-min-height { min-height: calc(100vh - 120px); }
.text-ellipsis { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

/* Estilos para dados herdados no painel de informações */
.inherited-data-highlight {
    color: var(--success) !important;
    font-weight: bold !important;
    text-shadow: 0 0 2px rgba(40, 167, 69, 0.3);
}

.inherited-field-container {
    position: relative;
    cursor: help;
}

.inherited-field-container:hover {
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 3px;
    padding: 1px 3px;
    margin: -1px -3px;
}

.inheritance-indicator {
    font-size: 0.7rem;
    opacity: 0.8;
    animation: inheritanceGlow 2s ease-in-out infinite alternate;
}

@keyframes inheritanceGlow {
    from { opacity: 0.6; }
    to { opacity: 1; }
}

/* --- Estilos para Módulo de Perdas (Losses) --- */

/* Classes para legendas e badges de status */
.legend-badge {
    font-size: 0.65rem !important;
    padding: 0.3em 0.5em !important;
    border-radius: 0.2rem !important;
    margin-bottom: 0.25rem !important;
    display: inline-block !important;
}

/* Classes para estilos de status - Fonte reduzida em 10% */
.status-badge-base {
    font-weight: normal;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    display: inline-block;
    font-size: 0.63rem !important; /* 10% menor que 0.7rem */
}

.status-ok {
    color: #0a3622;
    background-color: #d1e7dd;
    border: 1px solid #a3cfbb;
    font-size: 0.63rem !important;
}

.status-danger {
    color: #58151c;
    background-color: #f8d7da;
    border: 1px solid #f1aeb5;
    font-size: 0.63rem !important;
}

.status-warning {
    color: #664d03;
    background-color: #fff3cd;
    border: 1px solid #ffe69c;
    font-size: 0.63rem !important;
}

.status-default {
    color: #41464b;
    background-color: #e2e3e5;
    border: 1px solid #d3d6d8;
    font-size: 0.63rem !important;
}

/* Classes para tema escuro - status com fonte escura */
[data-bs-theme="dark"] .status-ok {
    color: #000000 !important;
}

[data-bs-theme="dark"] .status-danger {
    color: #000000 !important;
}

[data-bs-theme="dark"] .status-warning {
    color: #000000 !important;
}

[data-bs-theme="dark"] .status-default {
    color: #000000 !important;
}

/* Força cor preta para status em tema escuro na tabela de resultados */
[data-bs-theme="dark"] .status-dark-theme {
    color: #000000 !important;
}

/* Classes específicas para status de cenários com melhor visibilidade */
.status-critical-red {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
    font-weight: bold !important;
    padding: 0.2rem 0.4rem !important;
    border-radius: 0.25rem !important;
}

.status-alert-orange {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
    font-weight: bold !important;
    padding: 0.2rem 0.4rem !important;
    border-radius: 0.25rem !important;
}

.status-ok-green {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border: 1px solid #a3cfbb !important;
    font-weight: bold !important;
    padding: 0.2rem 0.4rem !important;
    border-radius: 0.25rem !important;
}

/* Tema escuro - status com cores mais visíveis */
[data-bs-theme="dark"] .status-critical-red {
    background-color: #842029 !important;
    color: #ffffff !important;
    border: 1px solid #a02834 !important;
}

[data-bs-theme="dark"] .status-alert-orange {
    background-color: #664d03 !important;
    color: #ffffff !important;
    border: 1px solid #7a5f0a !important;
}

[data-bs-theme="dark"] .status-ok-green {
    background-color: #0f5132 !important;
    color: #ffffff !important;
    border: 1px solid #146c43 !important;
}

/* Força cor preta para TODOS os elementos de status em tema escuro - máxima especificidade */
[data-bs-theme="dark"] td.status-ok,
[data-bs-theme="dark"] td.status-danger,
[data-bs-theme="dark"] td.status-warning,
[data-bs-theme="dark"] td.status-default,
[data-bs-theme="dark"] .status-ok,
[data-bs-theme="dark"] .status-danger,
[data-bs-theme="dark"] .status-warning,
[data-bs-theme="dark"] .status-default {
    color: #000000 !important;
}

/* Classes para estilos SUT/EPS */
.sut-eps-base {
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    display: inline-block;
}

.sut-eps-excessive {
    color: #052c65;
    background-color: #cfe2ff;
    border: 1px solid #9ec5fe;
    font-style: italic;
}

.sut-eps-normal {
    color: #0a3622;
    background-color: #d1e7dd;
    border: 1px solid #a3cfbb;
}

.sut-eps-alert {
    color: #664d03;
    background-color: #fff3cd;
    border: 1px solid #ffe69c;
}

.sut-eps-high {
    color: #613900;
    background-color: #feedcf;
    border: 1px solid #fcd396;
}

.sut-eps-critical {
    color: #58151c;
    background-color: #f8d7da;
    border: 1px solid #f1aeb5;
    font-weight: bold;
}

.sut-eps-na {
    color: #41464b;
    background-color: #e2e3e5;
    border: 1px solid #d3d6d8;
}

/* Classes para cabeçalhos de cards */
.card-header-dark {
    background-color: var(--background-card-header) !important;
    color: var(--text-header) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

/* Classe adaptativa para cabeçalhos de cards que se adapta ao tema */
.card-header-adaptive {
    background-color: var(--background-card-header);
    color: var(--text-header);
    border-bottom: 1px solid var(--border-color);
}

/* Classes para tabelas com caption no topo */
.table-caption-top {
    caption-side: top;
}

/* Classes para success light (tabelas) */
.table-success-light {
    background-color: #e6ffed !important;
}

/* Classes para bordas específicas */
.border-bottom-gray {
    border-bottom: 1px solid #dee2e6;
}

/* Classes para alinhamento vertical */
.vertical-align-middle {
    vertical-align: middle;
}

/* Classes para larguras mínimas */
.min-width-60px {
    min-width: 60px;
}

.min-width-100px {
    min-width: 100px;
}

.min-width-130px {
    min-width: 130px;
}

/* Select Editável - permite digitação em select */
.editable-select {
    position: relative;
}

.editable-select option[data-custom="true"] {
    font-style: italic;
    color: var(--text-muted);
}

/* === MELHORIAS PARA TABELA SUT/EPS === */

/* Classes SUT/EPS melhoradas com cores mais visíveis */
.sut-eps-base {
    font-weight: bold;
    padding: 0.3rem 0.4rem;
    border-radius: 0.3rem;
    display: inline-block;
    border: 2px solid;
    font-size: 0.8rem;
}

.sut-eps-normal {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
    box-shadow: 0 1px 3px rgba(15, 81, 50, 0.2);
}

.sut-eps-alert {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
    box-shadow: 0 1px 3px rgba(102, 77, 3, 0.2);
}

.sut-eps-high {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
    box-shadow: 0 1px 3px rgba(132, 32, 41, 0.2);
}

.sut-eps-critical {
    color: #721c24;
    background-color: #f5c2c7;
    border-color: #dea7ac;
    box-shadow: 0 1px 3px rgba(114, 28, 36, 0.3);
    animation: pulse-critical 2s infinite;
}

.sut-eps-excessive {
    color: #495057;
    background-color: #e9ecef;
    border-color: #ced4da;
    box-shadow: 0 1px 3px rgba(73, 80, 87, 0.2);
}

.sut-eps-na {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Animação para valores críticos */
@keyframes pulse-critical {
    0% { box-shadow: 0 1px 3px rgba(114, 28, 36, 0.3); }
    50% { box-shadow: 0 2px 8px rgba(114, 28, 36, 0.6); }
    100% { box-shadow: 0 1px 3px rgba(114, 28, 36, 0.3); }
}

/* Estilo para a tabela SUT/EPS melhorada */
.sut-eps-improved-table {
    font-size: 0.8rem;
    margin-bottom: 0;
}

.sut-eps-improved-table th {
    padding: 0.5rem 0.3rem;
    font-weight: bold;
    border: 1px solid var(--border-color);
}

.sut-eps-improved-table td {
    padding: 0.4rem 0.3rem;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

/* === CORREÇÃO PARA CABEÇALHOS DE TABELAS NO TEMA ESCURO === */

/* Garantir que cabeçalhos de tabelas tenham cor adequada no tema escuro */
[data-bs-theme="dark"] .table-light th {
    color: var(--text-color) !important;
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
}

/* Cabeçalhos específicos das tabelas SUT/EPS */
[data-bs-theme="dark"] .sut-eps-header-cell,
[data-bs-theme="dark"] .sut-eps-subheader-cell {
    color: var(--text-color) !important;
    background-color: var(--background-card) !important;
    border-color: var(--border-color) !important;
}

/* Garantir que todos os cabeçalhos de tabelas sejam visíveis no tema escuro */
[data-bs-theme="dark"] .table thead th {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

/* Theme improvements for induced voltage tables and alerts */
[data-bs-theme="light"] .induced-tab-content .table {
    color: var(--text-light);
    background-color: var(--background-card);
}

[data-bs-theme="light"] .induced-tab-content .table td,
[data-bs-theme="light"] .induced-tab-content .table th {
    border-color: var(--border-color);
    color: var(--text-light);
}

.induced-tab-content .alert {
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

/* Induced Voltage Page Tab Fixes */
.induced-tab-content {
    min-height: auto !important;
    height: auto !important;
}

        body { background-color: #212529; color: #f8f9fa; }
        .card { background-color: #2b3035; border-color: #454d55; }
        .card-header, .card-header-sm { background-color: #343a40; color: #f8f9fa; border-bottom: 1px solid #454d55; }
        .table-light { background-color: #343a40 !important; color: #f8f9fa !important; }
        .table-striped>tbody>tr:nth-of-type(odd)>* { --bs-table-accent-bg: rgba(255, 255, 255, 0.025); color: #f8f9fa; }
        .table-striped>tbody>tr:nth-of-type(even)>* { color: #f8f9fa; }
        .table-hover > tbody > tr:hover > * { --bs-table-accent-bg: rgba(255, 255, 255, 0.05); color: #f8f9fa; }
        .table-bordered { border-color: #454d55; }
        .text-muted { color: #adb5bd !important; }
        .form-control, .form-select { background-color: #343a40; color: #f8f9fa; border-color: #6c757d; }
        .form-control::placeholder { color: #adb5bd; }
        .form-check-input { background-color: #495057; border-color: #6c757d; } 
        .form-check-input:checked { background-color: #0d6efd; border-color: #0d6efd; }
        .form-check-label { color: #ced4da; } 
        .nav-tabs .nav-link { color: #adb5bd; border-color: #454d55 #454d55 #343a40; }
        .nav-tabs .nav-link.active { background-color: #343a40 !important; color: white !important; border-color: #454d55 #454d55 #343a40 !important; }
        .alert-warning { background-color: #332701; color: #ffda6a; border-color: #4d3802; }
        .alert-danger { background-color: #2c0b0e; color: #f5c2c7; border-color: #842029; }

        .card-header-title-sm { font-size: 0.8rem; font-weight: bold; }
        .card-sm .card-body { font-size: 0.75rem; }
        .table-sm th, .table-sm td { padding: 0.2rem 0.3rem; font-size: 0.7rem; vertical-align: middle;}
        .results-placeholder { min-height: 100px; }
        .card-header-dark { background-color: #212529 !important; color: white !important; }
        
        /* Larguras de Coluna para Tabela Principal de Perdas em Carga - Reduzidas para melhor apresentação */
        .losses-main-table .col-tap-dut { width: 6%; min-width: 60px; }
        .losses-main-table .col-vtest, .losses-main-table .col-itest { width: 4%; min-width: 40px; }
        .losses-main-table .col-icap { width: 4%; min-width: 45px; }
        .losses-main-table .col-pativa { width: 5%; min-width: 45px; }
        .losses-main-table .col-pteste { width: 5%; min-width: 50px; }
        .losses-main-table .col-qef { width: 5%; min-width: 50px; }
        .losses-main-table .col-banco { width: 22%; min-width: 220px; }
        .losses-main-table .col-status { width: 7%; min-width: 70px; font-size: 0.63rem !important; } /* Fonte 10% menor */
        
        .sut-eps-excessive-bg { background-color: #cae9f1 !important; color: #003c4d !important; }
        .sut-eps-normal-bg { background-color: #d1e7dd !important; color: #0f5132 !important; }
        .sut-eps-alert-bg { background-color: #fff3cd !important; color: #664d03 !important; }
        .sut-eps-high-bg { background-color: #ffe0b2 !important; color: #663d00 !important; }
        .sut-eps-critical-bg, .table-danger { background-color: #f8d7da !important; color: #842029 !important; }
        .sut-eps-na-bg { background-color: #e9ecef !important; color: #212529 !important; }

        [data-bs-theme="dark"] .sut-eps-excessive-bg { background-color: #003c4d !important; color: #cae9f1 !important; }
        [data-bs-theme="dark"] .sut-eps-normal-bg { background-color: #0f5132 !important; color: #d1e7dd !important; }
        [data-bs-theme="dark"] .sut-eps-alert-bg { background-color: #664d03 !important; color: #fff3cd !important; }
        [data-bs-theme="dark"] .sut-eps-high-bg { background-color: #663d00 !important; color: #ffe0b2 !important; }
        [data-bs-theme="dark"] .sut-eps-critical-bg, [data-bs-theme="dark"] .table-danger { background-color: #842029 !important; color: #f8d7da !important; }
        [data-bs-theme="dark"] .sut-eps-na-bg { background-color: #495057 !important; color: #e9ecef !important; }

        .status-ok-green { background-color: #d1e7dd !important; color: #0f5132 !important; font-size: 0.63rem !important; }
        .status-alert-orange { background-color: #fff3cd !important; color: #664d03 !important; font-size: 0.63rem !important; }
        .status-critical-red { background-color: #f8d7da !important; color: #842029 !important; font-size: 0.63rem !important; }

        [data-bs-theme="dark"] .status-ok-green { background-color: #0f5132 !important; color: #d1e7dd !important; }
        [data-bs-theme="dark"] .status-alert-orange { background-color: #664d03 !important; color: #fff3cd !important; }
        [data-bs-theme="dark"] .status-critical-red { background-color: #842029 !important; color: #f8d7da !important; }

        /* Redução de 10% no tamanho da fonte dos status */
        .col-status, .status-badge-base, .status-ok, .status-danger, .status-warning, .status-default {
            font-size: 0.63rem !important;
        }

        .bank-icon { cursor: help; margin: 0 1px; color: #f8f9fa; font-size: 0.8em; } 
        .bank-icon-na { color: #adb5bd; margin: 0 1px; font-size: 0.8em; }
        
        /* CSS para a NOVA Tabela SUT/EPS Horizontal */
        .sut-eps-horizontal-table {
            table-layout: fixed; 
            width: 100%;       
        }
        .sut-eps-horizontal-table th,
        .sut-eps-horizontal-table td {
            white-space: normal; 
            word-wrap: break-word; 
            overflow: hidden;         
            text-overflow: ellipsis;  
            padding: 0.2rem 0.2rem !important; 
            font-size: 0.65rem !important;     
            vertical-align: middle;
            text-align: center; 
        }
        .sut-eps-horizontal-table .sut-eps-header-cell { 
            background-color: var(--background-card-header);
            color: var(--text-header);
            font-weight: bold;
        }
        .sut-eps-horizontal-table .sut-eps-subheader-cell { 
            .sut-eps-horizontal-table .sut-eps-subheader-cell { 
            background-color: var(--secondary-color) !important; 
            font-size: 0.7rem !important; 
            padding: 0.3rem 0.2rem !important;
        }
        [data-bs-theme="light"] .sut-eps-horizontal-table .sut-eps-subheader-cell {
            color: #212529 !important; /* Dark color for light background */
        }
        [data-bs-theme="dark"] .sut-eps-horizontal-table .sut-eps-subheader-cell {
            color: #f8f9fa !important; /* Light color for dark background */
        }
        .sut-eps-horizontal-table .col-sut-tap-in-group { 
            width: 12%; 
            min-width: 60px;
            font-weight: bold;
            background-color: #e9ecef !important; 
            color: #212529 !important; 
        }
        .sut-eps-horizontal-table .col-ieps-in-group { 
            width: 12%; 
            min-width: 50px;
        }
        .sut-eps-horizontal-table .sut-tap-value-cell.sut-tap-ideal-highlight {
            background-color: #fff9c4 !important; 
            color: #3e2723 !important; 
            font-weight: bold;
        }
        /* Fim CSS para a NOVA Tabela SUT/EPS Horizontal */

        .table-caption-top { caption-side: top; color: #adb5bd;}
        .font-size-07rem { font-size: 0.7rem !important; }
        .alert-sm { padding: 0.4rem 0.8rem; font-size: 0.75rem; }

        #status-geral-card-body p { margin-bottom: 0.25rem; } #status-geral-card-body strong { color: #e9ecef; }
        #status-geral-card-body .text-danger strong, #status-geral-card-body .text-danger { color: #ff9a9a !important; }
        #status-geral-card-body .text-warning strong, #status-geral-card-body .text-warning { color: #ffd76a !important; }
        #status-geral-card-body .text-success strong, #status-geral-card-body .text-success { color: #80ffaa !important; }
        
        .q-efetiva-excede { color: #dc3545 !important; font-weight: bold !important; cursor: help; }

        .bank-options-table-container { overflow-y: auto; padding: 2px;}
        .bank-options-inner-table th, .bank-options-inner-table td {
            padding: 0.1rem 0.2rem !important; 
            font-size: 0.65rem !important; 
            vertical-align: middle;
        }
        .bank-options-inner-table .form-check-input {
            margin-top: 0.1rem; 
        }