<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Testes de Transformadores</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Seu CSS Customizado -->
    <link rel="stylesheet" href="assets/custom.css">
    <style>
        /* Estilos aprimorados para a navegação parecer abas */
        .navbar-nav .nav-item {
            margin-right: 10px; /* Espaçamento aumentado entre as "abas" */
        }
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.75); /* Cor padrão para links */
            padding: 0.5rem 1rem;
            transition: all 0.2s ease-in-out;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex; /* Para alinhar ícone e texto */
            align-items: center;
            border-bottom: 3px solid transparent; /* Para simular a borda de aba ativa */
            border-radius: 4px 4px 0 0; /* Arredondar bordas superiores */
        }

        .navbar-nav .nav-link.active {
            color: var(--info-color) !important; /* Cor de destaque para link ativo */
            font-weight: bold;
            text-shadow: 0 0 5px rgba(23, 162, 184, 0.5); /* Um leve brilho para o ativo, usando cor info */
            border-color: var(--info-color) !important; /* Borda da aba ativa */
            background-color: rgba(23, 162, 184, 0.1); /* Fundo sutil para a aba ativa, usando cor info */
        }

        .navbar-nav .nav-link:hover:not(.active) {
            color: rgba(255, 255, 255, 0.9);
            background-color: rgba(255, 255, 255, 0.1); /* Fundo sutil ao passar o mouse */
        }

        /* Ajustes para centralizar os nav-items e manter os utilitários à direita */
        #navbarContent {
            display: flex;
            justify-content: space-between; /* Espaça os grupos de elementos */
            align-items: center;
            width: 100%; /* Ocupa a largura total do container */
        }
        #navbarContent .navbar-nav {
            /* flex-grow: 1; Removido para controle mais fino com justify-content */
            margin-left: auto; /* Empurra para o centro/direita do espaço disponível */
            margin-right: 0; /* Reinicia margem para centralização */
            /* Para centralizar as abas, podemos usar mais flexbox ou wrap os links */
            /* Se quiser centralizar perfeitamente as abas, o mais simples é: */
            /* width: auto; e remover justify-content: center do #navbarContent */
            /* E adicionar justify-content: center a um div wrapper ao redor do ul.navbar-nav */
        }
        /* Para telas pequenas, os itens ficam empilhados */
        @media (max-width: 991.98px) { /* breakpoint do lg */
            #navbarContent {
                flex-direction: column;
                align-items: flex-start;
            }
            #navbarContent .navbar-nav {
                margin: 0 !important;
                width: 100%;
                justify-content: flex-start;
            }
            #navbarContent .d-flex.align-items-center.ms-auto {
                margin-top: 1rem; /* Espaçamento após links em mobile */
                width: 100%;
                justify-content: flex-start; /* Alinha os utilitários à esquerda em mobile */
            }
        }


        /* Ajustes para a área de conteúdo principal */
        #main-content-area {
            background-color: var(--background-main);
            border-radius: 4px;
            padding: 1rem;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
            flex-grow: 1; /* Permite que ocupe o espaço restante */
            margin: 0 1rem; /* Margem lateral */
            margin-top: 0.5rem; /* Margem superior para separar do cabeçalho */
            min-height: calc(100vh - 180px); /* Ajustado para o novo layout */
        }

        /* Estilo para dropdown no tema escuro */
        .dark-dropdown .form-select {
            background-color: var(--background-card);
            color: var(--text-light);
            border-color: var(--border-color);
        }
        .dark-dropdown .form-select option {
            background-color: var(--background-card);
            color: var(--text-light);
        }
    </style>
</head>
<body style="padding-top: 60px;"> <!-- Adiciona padding para compensar o cabeçalho fixo -->
    <div class="container-fluid p-0 d-flex flex-column" style="background-color: var(--background-main); min-height: 100vh;">
        <!-- Navbar Superior (Cabeçalho) -->
        <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background-color: var(--primary-color); padding: 0.5rem 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.2); border-bottom: 1px solid var(--border-color);">
            <div class="container-fluid">
                <a class="navbar-brand d-flex align-items-center" href="#" id="home-link">
                    <img src="assets/DataLogo.jpg" alt="Logo" height="40px">
                    <div>
                        <h4 class="m-0">TTS</h4>
                        <div>
                            <span><strong>IEC/IEEE/ABNT</strong></span> | <span>EPS 1500</span>
                        </div>
                    </div>
                </a>

                <!-- Botão de Toggler para mobile -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Conteúdo que será colapsável (links de navegação e utilitários) -->
                <div class="collapse navbar-collapse" id="navbarContent">
                    <!-- Links de Navegação Principal (AGORA SÓ data-module E href) -->
                    <ul class="navbar-nav mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-module="transformer_inputs">
                                <i class="fas fa-cogs me-2"></i> Dados Básicos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="losses">
                                <i class="fas fa-bolt me-2"></i> Perdas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="applied_voltage">
                                <i class="fas fa-plug me-2"></i> Tensão Aplicada
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="induced_voltage">
                                <i class="fas fa-charging-station me-2"></i> Tensão Induzida
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="temperature_rise">
                                <i class="fas fa-temperature-high me-2"></i> Elevação de Temperatura
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="short_circuit">
                                <i class="fas fa-exclamation-triangle me-2"></i> Curto-Circuito
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="dielectric_analysis">
                                <i class="fas fa-microscope me-2"></i> Análise Dielétrica
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="impulse">
                                <i class="fas fa-wave-square me-2"></i> Impulso
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="history">
                                <i class="fas fa-history me-2"></i> Histórico
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-module="standards">
                                <i class="fas fa-book me-2"></i> Normas
                            </a>
                        </li>
                    </ul>

                    <!-- Utilitários da Direita (uso, botões, tema) -->
                    <div class="d-flex align-items-center ms-auto">
                        <div class="d-flex align-items-center ms-auto">
                            <div id="usage-counter-display" class="me-2 py-1 px-2 usage-counter-style">
                                <i class="fas fa-chart-line me-1"></i>
                                <span>Uso:</span>
                                <span id="usage-value">0</span>
                                <div class="usage-bar-container">
                                    <div id="usage-bar" class="usage-bar"></div>
                                </div>
                            </div>
                            <div id="limit-alert-div" class="me-2 d-none">
                                <div class="alert alert-danger d-flex align-items-center p-2 mb-0 limit-alert-style">
                                    <i class="fas fa-exclamation-triangle me-1"></i> Limite Atingido!
                                </div>
                            </div>
                            <button id="generate-report-btn" class="btn btn-primary btn-sm me-2 navbar-button-style">
                                <i class="fas fa-file-pdf me-1"></i> Gerar Relatório PDF
                            </button>

                            <button id="theme-toggle" class="btn btn-outline-light btn-sm navbar-button-style theme-toggle-style">
                                <i id="theme-toggle-icon" class="fas fa-sun me-1"></i>
                                <span id="theme-toggle-text">Tema Claro</span>
                            </button>
                        </div>
            </div>
        </nav>

        <!-- Conteúdo Principal: Área de Módulos (Única) -->
        <div id="main-content-area">
            <div class="alert alert-info m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--info-color);">
                <i class="fas fa-info-circle me-2"></i> Selecione um módulo no menu acima para começar.
            </div>
            <!-- O conteúdo HTML dos módulos será carregado aqui dinamicamente pelo main.js -->
        </div>

    <!-- Rodapé (Footer) -->
        <footer class="mt-auto py-1" style="background-color: var(--background-card-header); border-top: 1px solid var(--border-color); box-shadow: 0 -1px 3px rgba(0,0,0,0.1);">
            <div class="container-fluid text-center">
                <span class="text-muted" style="font-size: 0.7rem;">&copy; <span id="current-year"></span> Transformer Test Simulator </span>
                <span class="text-muted" style="font-size: 0.7rem; opacity: 0.7;">v5.4</span>
            </div>
        </footer>
    </div>

    <!-- Modals Globais -->


    <!-- Modal de Inicialização - Carregar ou Novo Projeto -->
    <div class="modal fade" id="startupModal" tabindex="-1" aria-labelledby="startupModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: var(--background-card); color: var(--text-light);">
                <div class="modal-header" style="background-color: var(--primary); color: white;">
                    <h5 class="modal-title" id="startupModalLabel">
                        <i class="fas fa-database me-2"></i>Dados Encontrados
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-question-circle" style="font-size: 3rem; color: var(--primary);"></i>
                    </div>
                    <p class="text-center mb-4" style="color: var(--text-light);">
                        Foram encontrados dados salvos de um projeto anterior.
                        O que você gostaria de fazer?
                    </p>
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="card" style="border-color: var(--success); background-color: var(--background-input);">
                                <div class="card-body text-center">
                                    <i class="fas fa-folder-open mb-2" style="font-size: 2rem; color: var(--success);"></i>
                                    <h6 class="card-title" style="color: var(--text-light);">Continuar Projeto</h6>
                                    <p class="card-text small" style="color: var(--text-muted);">
                                        Carregar todos os dados salvos e continuar de onde parou
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card" style="border-color: var(--warning); background-color: var(--background-input);">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle mb-2" style="font-size: 2rem; color: var(--warning);"></i>
                                    <h6 class="card-title" style="color: var(--text-light);">Novo Projeto</h6>
                                    <p class="card-text small" style="color: var(--text-muted);">
                                        Limpar todos os dados e começar um novo projeto
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background-color: var(--background-card); border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-success btn-lg flex-fill me-2" id="continueProjectButton">
                        <i class="fas fa-folder-open me-2"></i>Continuar Projeto
                    </button>
                    <button type="button" class="btn btn-warning btn-lg flex-fill" id="newProjectButton">
                        <i class="fas fa-plus-circle me-2"></i>Novo Projeto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Seleção de Módulos para Relatório -->
    <div class="modal fade" id="reportModulesModal" tabindex="-1" aria-labelledby="reportModulesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--primary-color); color: white;">
                    <h5 class="modal-title" id="reportModulesModalLabel">
                        <i class="fas fa-file-pdf me-2"></i>Gerar Relatório PDF
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reportTitle" class="form-label"><strong>Nome do Relatório:</strong></label>
                        <input type="text" class="form-control" id="reportTitle" placeholder="Ex: Relatório Transformador 138kV - 50MVA" value="Relatório de Simulação de Testes de Transformadores">
                        <small class="form-text text-muted">Este nome aparecerá na primeira página do relatório</small>
                    </div>

                    <hr class="my-3">

                    <p class="mb-3">Selecione os módulos que deseja incluir no relatório:</p>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="transformerInputs" id="module-transformerInputs" checked>
                                <label class="form-check-label" for="module-transformerInputs">
                                    <i class="fas fa-cog me-2 text-primary"></i><strong>Dados Básicos</strong>
                                    <small class="d-block text-muted">Informações gerais do transformador</small>
                                </label>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="losses" id="module-losses" checked>
                                <label class="form-check-label" for="module-losses">
                                    <i class="fas fa-chart-line me-2 text-warning"></i><strong>Perdas</strong>
                                    <small class="d-block text-muted">Ensaios de perdas em vazio e carga</small>
                                </label>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="appliedVoltage" id="module-appliedVoltage" checked>
                                <label class="form-check-label" for="module-appliedVoltage">
                                    <i class="fas fa-bolt me-2 text-danger"></i><strong>Tensão Aplicada</strong>
                                    <small class="d-block text-muted">Ensaio de tensão aplicada</small>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="inducedVoltage" id="module-inducedVoltage" checked>
                                <label class="form-check-label" for="module-inducedVoltage">
                                    <i class="fas fa-wave-square me-2 text-info"></i><strong>Tensão Induzida</strong>
                                    <small class="d-block text-muted">Ensaio de tensão induzida</small>
                                </label>
                            </div>

                            <!-- Módulos que serão incluídos no futuro -->
                            <div class="alert alert-info mt-3 mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Módulos em desenvolvimento:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Elevação de Temperatura</li>
                                    <li>Curto-Circuito</li>
                                    <li>Impulso</li>
                                    <li>Análise Dielétrica</li>
                                </ul>
                                <small class="text-muted">Estes módulos serão incluídos em versões futuras do relatório PDF.</small>
                            </div>

                        </div>
                    </div>

                    <hr class="my-3">

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="selectAllModules">
                                <i class="fas fa-check-double me-1"></i>Selecionar Todos
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2" id="deselectAllModules">
                                <i class="fas fa-times me-1"></i>Desmarcar Todos
                            </button>
                        </div>
                        <div class="text-end">
                            <small class="text-muted d-block">
                                <i class="fas fa-check-circle text-success me-1"></i>Com dados
                                <i class="fas fa-exclamation-triangle text-warning me-1 ms-2"></i>Sem dados
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-success me-2" id="downloadExcelBtn">
                        <i class="fas fa-file-excel me-1"></i>Baixar Dados Excel
                        <span class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true" id="excelSpinner"></span>
                    </button>
                    <button type="button" class="btn btn-primary" id="generateReportBtn">
                        <i class="fas fa-file-pdf me-1"></i>Gerar Relatório
                        <span class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true" id="reportSpinner"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle (JQuery não é mais necessário com Bootstrap 5) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Chart.js para gráficos de linha e barras (versão 3.x estável) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Plotly.js para gráficos avançados -->
    <script src="https://cdn.plot.ly/plotly-2.26.0.min.js"></script>
    
    <!-- Inicialização do sistema de persistência -->
    <script src="scripts/api_persistence.js" type="module"></script>    <!-- Script principal para gerenciar o carregamento das páginas dos módulos -->
    <script src="scripts/main.js" type="module"></script>
</body>
</html>