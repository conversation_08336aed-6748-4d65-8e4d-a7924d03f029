<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Compensação Reativa Avançado</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card-header {
            font-weight: 600;
        }

        .display-value {
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.5rem;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            background-color: #e9ecef;
            min-width: 100px;
            display: inline-block;
            text-align: center;
        }
        
        /* --- ESTILOS PARA OS FIOS E ANIMAÇÃO --- */
        .wire {
            position: absolute;
            background-color: #adb5bd; /* Cor padrão quando desligado */
            z-index: -1;
        }

        .animated-wire {
            background-repeat: repeat-x;
            background-size: 40px 40px;
            animation: flow 1.2s linear infinite;
        }
        .animated-wire.reverse {
            animation-direction: reverse;
        }
        
        /* Cores dos Fios */
        .wire-color-source { background-image: linear-gradient(45deg, #198754 25%, #20c997 25%, #20c997 50%, #198754 50%, #198754 75%, #20c997 75%, #20c997 100%); }
        .wire-color-load { background-image: linear-gradient(45deg, #fd7e14 25%, #ffc107 25%, #ffc107 50%, #fd7e14 50%, #fd7e14 75%, #ffc107 75%, #ffc107 100%); }
        .wire-color-cap { background-image: linear-gradient(45deg, #0d6efd 25%, #0dcaf0 25%, #0dcaf0 50%, #0d6efd 50%, #0d6efd 75%, #0dcaf0 75%, #0dcaf0 100%); }

        /* Animação de Fluxo */
        @keyframes flow {
            from { background-position: 0 0; }
            to { background-position: 40px 0; }
        }

        /* Posicionamento dos Fios */
        #wire-main { top: 50%; left: 0; width: 100%; height: 8px; margin-top: -4px; }
        #wire-t-junction-cap { top: 0; left: 75%; width: 8px; height: 50%; margin-left: -4px; }
        #wire-t-junction-dut { bottom: 0; left: 75%; width: 8px; height: 50%; margin-left: -4px; }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="text-center mb-4">Simulador de Compensação Reativa</h2>

        <!-- Linha do Diagrama Principal -->
        <div class="row align-items-center justify-content-center text-center position-relative mb-4" style="height: 350px;">
            <!-- Fios de Conexão (Visualização) -->
            <div id="wire-main" class="wire"></div>
            <div id="wire-t-junction-cap" class="wire"></div>
            <div id="wire-t-junction-dut" class="wire"></div>

            <!-- Coluna da Fonte -->
            <div class="col-md-5 d-flex justify-content-around">
                <div class="card shadow-sm" style="width: 240px;">
                    <div class="card-header bg-dark text-white"><i class="fas fa-bolt me-2"></i>EPS SYSTEM</div>
                    <div class="card-body">
                        <p class="mb-1">Tensão (V): <span id="eps-voltage" class="display-value text-primary">0.0</span></p>
                        <p class="mb-0">Corrente (A): <span id="eps-current" class="display-value text-success">0.0</span></p>
                    </div>
                </div>
                <div class="card shadow-sm" style="width: 240px;">
                    <div class="card-header bg-secondary text-white"><i class="fas fa-random me-2"></i>SUT</div>
                    <div class="card-body">
                        <p class="mb-1">Tensão (kV): <span id="sut-output-voltage" class="display-value text-primary">0.0</span></p>
                        <p class="mb-0">Corrente (A): <span id="sut-output-current" class="display-value text-success">0.0</span></p>
                    </div>
                </div>
            </div>
            
            <!-- Coluna de Junção (invisível, para espaçamento) -->
            <div class="col-md-2"></div>
            
            <!-- Coluna da Carga e Capacitor -->
            <div class="col-md-5 d-flex flex-column align-items-center" style="gap: 170px;">
                <div class="card shadow-sm" style="width: 240px;">
                    <div class="card-header" style="background-color: #0d6efd; color: white;"><i class="fas fa-copyright me-2"></i>CAPACITOR</div>
                    <div class="card-body">
                        <p class="mb-0">Corrente (A): <span id="capacitor-current-display" class="display-value text-primary">0.0</span></p>
                    </div>
                </div>
                <div class="card shadow-sm" style="width: 240px;">
                    <div class="card-header" style="background-color: #fd7e14; color: white;"><i class="fas fa-industry me-2"></i>CARGA (DUT)</div>
                    <div class="card-body">
                        <p class="mb-0">Demanda (A): <span class="display-value text-danger">640.0</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Linha de Controles e Status -->
        <div class="row justify-content-center g-4">
            <!-- Controles -->
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header"><i class="fas fa-sliders-h me-2"></i>Painel de Controle</div>
                    <div class="card-body d-flex justify-content-around align-items-center">
                        <!-- Controle de Potência (EPS) -->
                        <div class="text-center">
                            <h5>Potência da Fonte</h5>
                            <div class="d-flex align-items-center gap-3">
                                <button id="btn-down" class="btn btn-danger btn-lg"><i class="fas fa-caret-down"></i></button>
                                <span id="power-level-display" class="display-value" style="font-size: 2rem;">0</span>
                                <button id="btn-up" class="btn btn-success btn-lg"><i class="fas fa-caret-up"></i></button>
                            </div>
                            <div class="mt-2">%</div>
                        </div>
                        <!-- Controles do Capacitor -->
                        <div class="text-center">
                            <h5>Tensão Nominal (V_cap)</h5>
                             <div class="btn-group">
                                <button class="btn btn-outline-primary" id="btn-v-cap-down"><i class="fas fa-minus"></i></button>
                                <button class="btn btn-primary" id="v-cap-display" style="width: 120px;">14.0 kV</button>
                                <button class="btn btn-outline-primary" id="btn-v-cap-up"><i class="fas fa-plus"></i></button>
                            </div>
                        </div>
                        <div class="text-center">
                            <h5>Potência Reativa (Q_nom)</h5>
                            <div class="btn-group">
                                <button class="btn btn-outline-primary" id="btn-q-cap-down"><i class="fas fa-minus"></i></button>
                                <button class="btn btn-primary" id="q-cap-display" style="width: 120px;">0 kVAR</button>
                                <button class="btn btn-outline-primary" id="btn-q-cap-up"><i class="fas fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Status Resumido -->
            <div class="col-md-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header"><i class="fas fa-info-circle me-2"></i>Status Resumido</div>
                    <div class="card-body d-flex flex-column justify-content-center text-center">
                        <div id="eps-status-card" class="alert alert-secondary">
                            <h5 class="alert-heading" id="eps-status-label">FONTE DESLIGADA</h5>
                            <p class="mb-0" id="eps-status-message">Ajuste a potência para iniciar.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const elements = {
                btnUp: document.getElementById('btn-up'), btnDown: document.getElementById('btn-down'),
                powerLevelDisplay: document.getElementById('power-level-display'),
                epsVoltage: document.getElementById('eps-voltage'), epsCurrent: document.getElementById('eps-current'),
                sutOutputVoltage: document.getElementById('sut-output-voltage'), sutOutputCurrent: document.getElementById('sut-output-current'),
                btnVCapUp: document.getElementById('btn-v-cap-up'), btnVCapDown: document.getElementById('btn-v-cap-down'),
                btnQCapUp: document.getElementById('btn-q-cap-up'), btnQCapDown: document.getElementById('btn-q-cap-down'),
                vCapDisplay: document.getElementById('v-cap-display'), qCapDisplay: document.getElementById('q-cap-display'),
                capacitorCurrentDisplay: document.getElementById('capacitor-current-display'),
                epsStatusCard: document.getElementById('eps-status-card'),
                epsStatusLabel: document.getElementById('eps-status-label'),
                epsStatusMessage: document.getElementById('eps-status-message'),
                wireMain: document.getElementById('wire-main'),
                wireCap: document.getElementById('wire-t-junction-cap'),
                wireDut: document.getElementById('wire-t-junction-dut'),
            };

            const MAX_EPS_V = 480;
            const MAX_EPS_I_RATING = 2000;
            const SUT_VOLTAGE_RATIO = 14000 / 480;
            const I_DUT = 640.0;
            const capVoltageLevels = [3500, 7000, 14000];
            const capPowerLevels = [0, 2400, 4800, 7200, 9600, 12000, 14400];

            const state = {
                powerLevel: 0,
                capVIndex: 2,
                capQIndex: 0,
                powerIntervalId: null
            };

            function updateSimulation() {
                const V_eps = MAX_EPS_V * (state.powerLevel / 100);
                const V_sut_out = V_eps * SUT_VOLTAGE_RATIO;
                const V_nominal_cap = capVoltageLevels[state.capVIndex];
                const Q_nominal = capPowerLevels[state.capQIndex] * 1000;

                let I_capacitor = 0;
                if (V_sut_out > 0 && V_nominal_cap > 0) {
                    const Q_real = Q_nominal * Math.pow(V_sut_out / V_nominal_cap, 2);
                    I_capacitor = Q_real / V_sut_out;
                }
                
                const I_saida_SUT = (state.powerLevel > 0) ? (I_DUT - I_capacitor) : 0;
                const I_eps = I_saida_SUT * SUT_VOLTAGE_RATIO;
                
                // ATUALIZAR DISPLAYS NUMÉRICOS
                elements.powerLevelDisplay.textContent = state.powerLevel;
                elements.epsVoltage.textContent = V_eps.toFixed(1);
                elements.epsCurrent.textContent = I_eps.toFixed(1);
                elements.sutOutputVoltage.textContent = (V_sut_out / 1000).toFixed(1);
                elements.sutOutputCurrent.textContent = I_saida_SUT.toFixed(1);
                elements.capacitorCurrentDisplay.textContent = I_capacitor.toFixed(1);
                elements.vCapDisplay.textContent = (V_nominal_cap / 1000).toFixed(1) + ' kV';
                elements.qCapDisplay.textContent = capPowerLevels[state.capQIndex] + ' kVAR';

                // ATUALIZAR VISUALIZAÇÃO DOS FIOS
                updateWireVisualization(I_eps, I_capacitor, I_saida_SUT);
                
                // ATUALIZAR STATUS
                updateStatusCard(I_eps, I_saida_SUT);
            }

            function updateWireVisualization(I_eps, I_capacitor, I_saida_SUT) {
                // Reset classes
                [elements.wireMain, elements.wireCap, elements.wireDut].forEach(el => {
                    el.className = 'wire';
                });

                if(state.powerLevel > 0) {
                    // Fio Principal (Fonte)
                    elements.wireMain.classList.add('wire-color-source', 'animated-wire');
                    if (I_saida_SUT < 0) {
                        elements.wireMain.classList.add('reverse');
                    }
                    // Fio do Capacitor
                    if(I_capacitor > 0.1) {
                         elements.wireCap.classList.add('wire-color-cap', 'animated-wire');
                    }
                    // Fio da Carga
                    elements.wireDut.classList.add('wire-color-load', 'animated-wire');
                }
            }

            function updateStatusCard(I_eps, I_saida_SUT) {
                elements.epsStatusCard.className = 'alert'; // Reset
                const abs_I_eps = Math.abs(I_eps);

                if (state.powerLevel === 0) {
                    elements.epsStatusCard.classList.add('alert-secondary');
                    elements.epsStatusLabel.textContent = 'FONTE DESLIGADA';
                    elements.epsStatusMessage.textContent = 'Ajuste a potência para iniciar.';
                } else if (abs_I_eps > MAX_EPS_I_RATING) {
                    elements.epsStatusCard.classList.add('alert-danger');
                    elements.epsStatusLabel.textContent = 'SOBRECARGA CRÍTICA';
                    elements.epsStatusMessage.textContent = `Corrente da fonte (${abs_I_eps.toFixed(0)} A) excedeu o limite de ${MAX_EPS_I_RATING} A.`;
                } else if (I_saida_SUT < 0) {
                    elements.epsStatusCard.classList.add('alert-warning');
                    elements.epsStatusLabel.textContent = 'FLUXO REVERSO';
                    elements.epsStatusMessage.textContent = 'Capacitor está injetando mais corrente que a carga consome.';
                } else {
                     elements.epsStatusCard.classList.add('alert-success');
                    elements.epsStatusLabel.textContent = 'OPERAÇÃO NOMINAL';
                    const percentComp = (I_capacitor / I_DUT * 100).toFixed(0);
                    elements.epsStatusMessage.textContent = `Carga compensada em ${percentComp}%. Corrente da fonte: ${abs_I_eps.toFixed(0)} A.`;
                }
            }
            
            function changePower(amount) {
                state.powerLevel = Math.max(0, Math.min(100, state.powerLevel + amount));
                updateSimulation();
            }
            function createIndexChanger(key, levelsArray) {
                return function(change) {
                    let newIndex = state[key] + change;
                    if (newIndex >= 0 && newIndex < levelsArray.length) {
                        state[key] = newIndex;
                        updateSimulation();
                    }
                };
            }
            const changeCapV = createIndexChanger('capVIndex', capVoltageLevels);
            const changeCapQ = createIndexChanger('capQIndex', capPowerLevels);
            function stopInterval() { clearInterval(state.powerIntervalId); }

            elements.btnUp.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(1), 50); });
            elements.btnDown.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(-1), 50); });
            document.addEventListener('mouseup', stopInterval);
            document.addEventListener('mouseleave', stopInterval);
            elements.btnVCapUp.addEventListener('click', () => changeCapV(1));
            elements.btnVCapDown.addEventListener('click', () => changeCapV(-1));
            elements.btnQCapUp.addEventListener('click', () => changeCapQ(1));
            elements.btnQCapDown.addEventListener('click', () => changeCapQ(-1));
            
            updateSimulation();
        });
    </script>
</body>
</html>