from typing import Optional
"""
Serviço para cálculos de ensaios de tensão aplicada
Implementa os algoritmos descritos em docs/instrucoes_aplicada.md
"""

from typing import Dict, Any, Tuple
import math
import logging
import datetime
import numpy as np
from ..utils.helpers import setup_path_for_imports

setup_path_for_imports()

from ..utils import constants as const
from ..models.test_data import (
    AppliedVoltageResult, AppliedVoltageTestParams, AppliedVoltageWindingResult,
    AppliedVoltageResonantConfig, StatusValidation, LimitsInfo, TestDataSerializer
)

# Configurar logging
log = logging.getLogger(__name__)


def safe_float(value, default=0.0):
    """Safely convert value to float, return default on error."""
    if value is None or value == "":
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def calculate_capacitive_load(
    capacitance_pf: float, voltage_v: float, frequency_hz: float
) -> <PERSON>ple[Optional[float], Optional[float], Optional[float]]:
    """Calcula Zc (Ohm), Corrente (mA) e Potência Reativa (VAr) para um enrolamento."""
    zc_ohm, current_ma, power_kvar = None, None, None
    if capacitance_pf is None or voltage_v is None or frequency_hz is None:
        log.warning("Dados insuficientes para calcular carga capacitiva.")
        return zc_ohm, current_ma, power_kvar
    try:
        cap_f = capacitance_pf * 1e-12
        v_v = voltage_v
        f_hz = frequency_hz

        if cap_f <= 0 or v_v < 0 or f_hz <= 0:
            log.error(
                f"Valores inválidos para cálculo capacitivo: C={cap_f}F, V={v_v}V, f={f_hz}Hz"
            )
            return None, None, None

        omega = 2 * np.pi * f_hz
        zc_ohm = 1.0 / (omega * cap_f) if omega * cap_f > 1e-15 else float("inf")
        current_a = v_v / zc_ohm if abs(zc_ohm) > 1e-9 else 0.0
        current_ma = current_a * 1000
        power_var = v_v * current_a  # Potência em VAr (unidade base)

        log.debug(
            f"Carga Capacitiva: C={capacitance_pf:.1f}pF, V={v_v/1000:.2f}kV, f={f_hz:.1f}Hz => Zc={zc_ohm:.1f}Ω, I={current_ma:.2f}mA, Q={power_var:.0f}VAr"
        )
        return zc_ohm, current_ma, power_var

    except (ValueError, TypeError, ZeroDivisionError) as e:
        log.error(f"Erro ao calcular carga capacitiva: {e}")
        return None, None, None
    except Exception as e:
        log.exception(f"Erro inesperado ao calcular carga capacitiva: {e}")
        return None, None, None


def analyze_resonant_system_viability(capacitance_nf, voltage_kv, enrolamento=""):
    """
    Analisa qual configuração do sistema ressonante é adequada e se o ensaio é viável.
    Prioriza a configuração "Módulos 1||2||3 (3 Par.)" sempre que possível.

    Args:
        capacitance_nf (float): Capacitância de ensaio em nF.
        voltage_kv (float): Tensão de ensaio em kV.
        enrolamento (str, optional): Nome do enrolamento (AT, BT, Terciário). Defaults to "".

    Returns:
        dict: Contendo 'resonant_config', 'viabilidade', 'recommendation', 'cor_alerta', 'enrolamento'.
              Retorna Nones se inputs inválidos.
    """
    if capacitance_nf is None or voltage_kv is None or capacitance_nf < 0 or voltage_kv < 0:
        return {
            "resonant_config": "Inválido",
            "viabilidade": "Erro",
            "recommendation": "Capacitância ou Tensão de ensaio inválida.",
            "cor_alerta": "danger",
            "enrolamento": enrolamento,
        }

    # Primeiro, verificar se podemos usar Módulos 1||2||3 (3 Par.)
    modulos_3par_450kv = None
    modulos_3par_270kv = None

    # Encontrar as configurações de Módulos 1||2||3 (3 Par.)
    for name, config in const.RESONANT_SYSTEM_CONFIGS.items():
        if "Módulos 1||2||3 (3 Par.) 450kV" in name:
            modulos_3par_450kv = (name, config)
        elif "Módulos 1||2||3 (3 Par.) 270kV" in name:
            modulos_3par_270kv = (name, config)

    # Verificar se podemos usar Módulos 1||2||3 (3 Par.) 450kV
    if (
        modulos_3par_450kv
        and voltage_kv <= modulos_3par_450kv[1]["tensao_max"]
        and capacitance_nf >= modulos_3par_450kv[1]["cap_min"]
        and capacitance_nf <= modulos_3par_450kv[1]["cap_max"]
    ):
        viable_config = modulos_3par_450kv[1]
        viable_config["nome"] = modulos_3par_450kv[0]

    # Verificar se podemos usar Módulos 1||2||3 (3 Par.) 270kV
    elif (
        modulos_3par_270kv
        and voltage_kv <= modulos_3par_270kv[1]["tensao_max"]
        and capacitance_nf >= modulos_3par_270kv[1]["cap_min"]
        and capacitance_nf <= modulos_3par_270kv[1]["cap_max"]
    ):
        viable_config = modulos_3par_270kv[1]
        viable_config["nome"] = modulos_3par_270kv[0]

    # Se não for possível usar Módulos 1||2||3 (3 Par.), tentar outras configurações
    else:
        # Ordenar as configurações por tensão máxima (decrescente) e capacitância máxima (crescente)
        # para priorizar configurações com menor capacitância para uma dada tensão
        sorted_configs = sorted(
            [(name, config) for name, config in const.RESONANT_SYSTEM_CONFIGS.items()],
            key=lambda x: (-x[1]["tensao_max"], x[1]["cap_max"]),
        )

        viable_config = None
        for name, config in sorted_configs:
            if (
                voltage_kv <= config["tensao_max"]
                and capacitance_nf >= config["cap_min"]
                and capacitance_nf <= config["cap_max"]
            ):
                viable_config = config
                viable_config["nome"] = name  # Adiciona o nome ao dicionário
                break  # Encontrou a primeira configuração viável

    if viable_config:
        # Verificar se é uma configuração de Módulos 1||2||3 (3 Par.)
        is_modulos_3par = "Módulos 1||2||3 (3 Par.)" in viable_config["nome"]

        # Calcular a capacitância original (sem o divisor de tensão)
        divisor_capacitancia = 0.33 if voltage_kv > 450 else 0.66  # Valor em nF (330pF ou 660pF)
        capacitancia_original_nf = capacitance_nf - divisor_capacitancia

        # Mensagem de recomendação personalizada
        if is_modulos_3par:
            recommendation = (
                f"Configuração ideal: {viable_config['nome']}. "
                f"Limites: {viable_config['tensao_max']}kV / "
                f"{viable_config['cap_min']:.1f}-{viable_config['cap_max']:.1f}nF. "
                f"Nota: A capacitância de {capacitance_nf:.2f} nF inclui {divisor_capacitancia:.2f} nF do divisor de tensão."
            )
        else:
            recommendation = (
                f"Configuração alternativa: {viable_config['nome']}. "
                f"Limites: {viable_config['tensao_max']}kV / "
                f"{viable_config['cap_min']:.1f}-{viable_config['cap_max']:.1f}nF. "
                f"Nota: A capacitância de {capacitance_nf:.2f} nF inclui {divisor_capacitancia:.2f} nF do divisor de tensão."
            )

        # Determina a cor baseada na configuração
        cor_alerta = "success" if is_modulos_3par else "warning"

        return {
            "resonant_config": viable_config["nome"],
            "viabilidade": "Viável",
            "recommendation": recommendation,
            "cor_alerta": cor_alerta,
            "enrolamento": enrolamento,
        }
    else:
        # Determinar a razão da inviabilidade
        reason = ""
        configs_tensao_ok = [
            c for _, c in const.RESONANT_SYSTEM_CONFIGS.items() if voltage_kv <= c["tensao_max"]
        ]

        # Verificar especificamente os limites dos Módulos 1||2||3 (3 Par.)
        modulos_3par_min_cap = None
        modulos_3par_max_cap = None
        modulos_3par_max_volt = None

        if modulos_3par_450kv and modulos_3par_270kv:
            modulos_3par_min_cap = min(
                modulos_3par_450kv[1]["cap_min"], modulos_3par_270kv[1]["cap_min"]
            )
            modulos_3par_max_cap = max(
                modulos_3par_450kv[1]["cap_max"], modulos_3par_270kv[1]["cap_max"]
            )
            modulos_3par_max_volt = max(
                modulos_3par_450kv[1]["tensao_max"], modulos_3par_270kv[1]["tensao_max"]
            )

        if not configs_tensao_ok:
            max_sys_volt = max(
                c["tensao_max"] for _, c in const.RESONANT_SYSTEM_CONFIGS.items()
            )
            reason = f"Tensão ({voltage_kv:.1f} kV) excede o máximo do sistema ({max_sys_volt} kV)."

            if modulos_3par_max_volt:
                reason += (
                    f" Para Módulos 1||2||3 (3 Par.), a tensão máxima é {modulos_3par_max_volt} kV."
                )
        else:
            # Tensão OK, problema é capacitância
            min_cap_needed = min(c["cap_min"] for c in configs_tensao_ok)
            max_cap_allowed = max(c["cap_max"] for c in configs_tensao_ok)

            if capacitance_nf < min_cap_needed:
                # Calcular a capacitância original (sem o divisor de tensão)
                divisor_capacitancia = (
                    0.33 if voltage_kv > 450 else 0.66
                )  # Valor em nF (330pF ou 660pF)
                capacitancia_original_nf = capacitance_nf - divisor_capacitancia

                reason = f"Capacitância ({capacitance_nf:.2f} nF) abaixo do mínimo ({min_cap_needed:.2f} nF) para esta tensão."
                reason += f" Nota: Inclui {divisor_capacitancia:.2f} nF do divisor de tensão."

                if capacitancia_original_nf < min_cap_needed - divisor_capacitancia:
                    reason += f" Mesmo sem o divisor, a capacitância informada ({capacitancia_original_nf:.2f} nF) estaria abaixo do limite."

                if modulos_3par_min_cap and capacitance_nf < modulos_3par_min_cap:
                    reason += f" Para Módulos 1||2||3 (3 Par.), a capacitância mínima é {modulos_3par_min_cap:.2f} nF."
            elif capacitance_nf > max_cap_allowed:
                # Calcular a capacitância original (sem o divisor de tensão)
                divisor_capacitancia = (
                    0.33 if voltage_kv > 450 else 0.66
                )  # Valor em nF (330pF ou 660pF)
                capacitancia_original_nf = capacitance_nf - divisor_capacitancia

                reason = f"Capacitância ({capacitance_nf:.2f} nF) acima do máximo ({max_cap_allowed:.1f} nF) para esta tensão."
                reason += f" Nota: Inclui {divisor_capacitancia:.2f} nF do divisor de tensão."

                if capacitancia_original_nf <= max_cap_allowed:
                    reason += f" A capacitância informada ({capacitancia_original_nf:.2f} nF) estaria dentro do limite sem o divisor."

                if modulos_3par_max_cap and capacitance_nf > modulos_3par_max_cap:
                    reason += f" Para Módulos 1||2||3 (3 Par.), a capacitância máxima é {modulos_3par_max_cap:.1f} nF."
            else:
                # Caso raro onde a tensão está OK, mas a capacitância cai num 'gap' entre configs
                reason = "Nenhuma configuração cobre esta combinação Tensão/Capacitância."

                if modulos_3par_min_cap and modulos_3par_max_cap:
                    reason += f" Para Módulos 1||2||3 (3 Par.), o range de capacitância é {modulos_3par_min_cap:.2f}-{modulos_3par_max_cap:.1f} nF."

        return {
            "resonant_config": "Nenhuma",
            "viabilidade": "Não Viável",
            "recommendation": f"Inválido: {reason}",
            "cor_alerta": "danger",
            "enrolamento": enrolamento,
        }


def calculate_applied_voltage_test(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula os parâmetros do teste de tensão aplicada com base nos dados do transformador.
    
    Args:
        data: Dicionário com os parâmetros básicos do transformador
        
    Returns:
        Dicionário com os resultados calculados para o teste de tensão aplicada
    """
    # Extrai parâmetros básicos necessários
    frequencia = data.get("frequencia", 60)  # Hz

    # Obtém tensões de ensaio aplicada diretamente dos dados (já definidas no transformer inputs)
    tensao_teste_at = safe_float(data.get("teste_tensao_aplicada_at", 0))
    tensao_teste_bt = safe_float(data.get("teste_tensao_aplicada_bt", 0))
    tensao_teste_terciario = safe_float(data.get("teste_tensao_aplicada_terciario", 0))

    # Obtém capacitâncias dos inputs do usuário (em pF) - seguindo lógica do SCRATCH
    cap_at_pf = safe_float(data.get("cap_at", 0), 0.0)
    cap_bt_pf = safe_float(data.get("cap_bt", 0), 0.0)
    cap_ter_pf = safe_float(data.get("cap_ter", 0), 0.0)

    print(f"[applied_voltage_service] Tensões de ensaio: AT={tensao_teste_at}, BT={tensao_teste_bt}, TER={tensao_teste_terciario}")
    print(f"[applied_voltage_service] Capacitâncias informadas: AT={cap_at_pf}pF, BT={cap_bt_pf}pF, TER={cap_ter_pf}pF")

    # Adiciona capacitância fixa dependendo da tensão (divisor de tensão) - seguindo lógica do SCRATCH
    # Para tensão > 450 kV: adicionar 330 pF
    # Para tensão ≤ 450 kV: adicionar 660 pF
    cap_at_adicional = 330 if tensao_teste_at and tensao_teste_at > 450 else 660
    cap_bt_adicional = 330 if tensao_teste_bt and tensao_teste_bt > 450 else 660
    cap_ter_adicional = 330 if tensao_teste_terciario and tensao_teste_terciario > 450 else 660

    cap_at_ajustado = cap_at_pf + cap_at_adicional
    cap_bt_ajustado = cap_bt_pf + cap_bt_adicional
    cap_ter_ajustado = cap_ter_pf + cap_ter_adicional if cap_ter_pf > 0 else 0

    print(f"[applied_voltage_service] Capacitâncias ajustadas: AT={cap_at_ajustado}pF, BT={cap_bt_ajustado}pF, TER={cap_ter_ajustado}pF")

    # Cálculos usando calculate_capacitive_load - seguindo lógica do SCRATCH
    tensao_at_v = tensao_teste_at * 1000 if tensao_teste_at else 0
    tensao_bt_v = tensao_teste_bt * 1000 if tensao_teste_bt else 0
    tensao_ter_v = tensao_teste_terciario * 1000 if tensao_teste_terciario else 0

    zc_at, i_at_ma, p_at_kvar = calculate_capacitive_load(cap_at_ajustado, tensao_at_v, frequencia)
    zc_bt, i_bt_ma, p_bt_kvar = calculate_capacitive_load(cap_bt_ajustado, tensao_bt_v, frequencia)

    if cap_ter_ajustado > 0:
        zc_ter, i_ter_ma, p_ter_kvar = calculate_capacitive_load(cap_ter_ajustado, tensao_ter_v, frequencia)
    else:
        zc_ter, i_ter_ma, p_ter_kvar = None, None, None

    # Prepara resultados seguindo a lógica do SCRATCH
    results = {
        # Tensões de teste
        "tensao_teste_at": tensao_teste_at,
        "tensao_teste_bt": tensao_teste_bt,
        "tensao_teste_terciario": tensao_teste_terciario,

        # Capacitâncias ajustadas
        "capacitancia_at": cap_at_ajustado,
        "capacitancia_bt": cap_bt_ajustado,
        "capacitancia_terciario": cap_ter_ajustado,

        # Correntes de teste (convertidas de mA para A)
        "corrente_teste_at": i_at_ma / 1000 if i_at_ma else 0,
        "corrente_teste_bt": i_bt_ma / 1000 if i_bt_ma else 0,
        "corrente_teste_terciario": i_ter_ma / 1000 if i_ter_ma else 0,

        # Potências Reativas (em kVAr)
        "potencia_reativa_at": p_at_kvar if p_at_kvar else 0,
        "potencia_reativa_bt": p_bt_kvar if p_bt_kvar else 0,
        "potencia_reativa_terciario": p_ter_kvar if p_ter_kvar else 0,

        # Frequência
        "frequencia": frequencia,

        # Dados detalhados seguindo estrutura do SCRATCH
        "inputs": {
            "cap_at_pf": cap_at_pf,
            "cap_bt_pf": cap_bt_pf,
            "cap_ter_pf": cap_ter_pf,
            "cap_at_ajustado_pf": cap_at_ajustado,
            "cap_bt_ajustado_pf": cap_bt_ajustado,
            "cap_ter_ajustado_pf": cap_ter_ajustado,
            "tensao_at_kv": tensao_teste_at,
            "tensao_bt_kv": tensao_teste_bt,
            "tensao_ter_kv": tensao_teste_terciario,
            "frequencia_hz": frequencia,
        },
        "resultados": {
            "dados_calculo": {
                "zc_at": zc_at,
                "i_at": i_at_ma,
                "p_at": p_at_kvar,
                "zc_bt": zc_bt,
                "i_bt": i_bt_ma,
                "p_bt": p_bt_kvar,
                "zc_ter": zc_ter,
                "i_ter": i_ter_ma,
                "p_ter": p_ter_kvar,
            }
        },
    }

    # 4. Análise de Viabilidade do Sistema Ressonante
    # Baseado nas especificações do equipamento High Volt WRM 1800/1350-900-450
    # Converte capacitâncias ajustadas de pF para nF para análise
    cap_at_nf = cap_at_ajustado / 1000.0
    cap_bt_nf = cap_bt_ajustado / 1000.0
    cap_ter_nf = cap_ter_ajustado / 1000.0 if cap_ter_ajustado > 0 else 0.0

    analise_at = analyze_resonant_system_viability(cap_at_nf, tensao_teste_at, "AT")
    analise_bt = analyze_resonant_system_viability(cap_bt_nf, tensao_teste_bt, "BT")
    analise_ter = analyze_resonant_system_viability(cap_ter_nf, tensao_teste_terciario, "Terciário") if cap_ter_ajustado > 0 else None


    # Adiciona análise de viabilidade aos resultados no formato esperado pelo JavaScript
    # O JavaScript espera strings simples para determinar as cores das linhas
    results["analise_viabilidade_ressonante"] = {
        "at": f"{analise_at['viabilidade']}: {analise_at['recommendation']}" if analise_at else "Não analisado",
        "bt": f"{analise_bt['viabilidade']}: {analise_bt['recommendation']}" if analise_bt else "Não analisado",
        "terciario": f"{analise_ter['viabilidade']}: {analise_ter['recommendation']}" if analise_ter else "Não analisado",
    }

    # Adiciona análise detalhada para uso em callbacks
    results["analise_detalhada"] = {
        "at": analise_at,
        "bt": analise_bt,
        "terciario": analise_ter,
    }

    # Adiciona análise de enrolamentos seguindo estrutura do SCRATCH
    results["resultados"]["dados_calculo"]["analise_enrolamentos"] = [analise_at, analise_bt] + ([analise_ter] if analise_ter else [])

    # Adiciona timestamp seguindo estrutura do SCRATCH
    results["timestamp"] = datetime.datetime.now().isoformat()

    print(f"[applied_voltage_service] Resultados calculados: {results}")
    return results


def process_applied_voltage(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Função principal para processar dados de tensão aplicada.

    Args:
        data: Dicionário com os dados do transformador e parâmetros específicos

    Returns:
        Dicionário com os resultados processados
    """
    try:
        # Calcula os parâmetros de tensão aplicada
        results = calculate_applied_voltage_test(data)

        # Adiciona timestamp
        results["timestamp"] = "calculated"
        results["calculation_type"] = "applied_voltage"

        return results

    except Exception as e:
        print(f"[applied_voltage_service] Erro no processamento: {e}")
        raise e


def serialize_applied_voltage_results(results: Dict[str, Any], form_data: Dict[str, Any]) -> AppliedVoltageResult:
    """
    Serialize applied voltage results into standardized format for database storage and reports.

    Args:
        results: Raw calculation results from calculate_applied_voltage_test
        form_data: Original form input data

    Returns:
        AppliedVoltageResult: Serialized applied voltage data
    """
    try:
        # Extract test parameters
        inputs = results.get("inputs", {})
        test_params = AppliedVoltageTestParams(
            cap_at_pf=inputs.get("cap_at_pf"),
            cap_bt_pf=inputs.get("cap_bt_pf"),
            cap_ter_pf=inputs.get("cap_ter_pf"),
            tensao_at_kv=inputs.get("tensao_at_kv"),
            tensao_bt_kv=inputs.get("tensao_bt_kv"),
            tensao_ter_kv=inputs.get("tensao_ter_kv"),
            frequencia_hz=inputs.get("frequencia_hz", 60.0)
        )

        # Create limits info
        transformer_data = {
            "tipo_transformador": form_data.get("tipo_transformador", "Trifásico"),
            "grupo_ligacao": form_data.get("grupo_ligacao", "")
        }

        dut_power_limit = const.get_dut_power_limit(transformer_data)
        eps_limits = const.get_eps_limits(transformer_data)
        sut_eps_type = const.determine_sut_eps_type(transformer_data)

        if sut_eps_type == "Monofásico":
            sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000 / const.SQRT_3
        else:
            sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000

        limits_info = LimitsInfo(
            eps_current_limit_positive_a=eps_limits.get("current_limit_positive_a", 2000.0),
            eps_current_limit_negative_a=eps_limits.get("current_limit_negative_a", -2000.0),
            dut_power_limit_kw=dut_power_limit,
            sut_at_max_voltage_kv=sut_max_voltage_kv,
            ct_current_limit_a=2000.0
        )

        # Process winding results
        winding_results = []
        windings = ["AT", "BT", "TER"]

        for winding in windings:
            winding_lower = winding.lower()

            # Check if this winding has data
            cap_original = inputs.get(f"cap_{winding_lower}_pf")
            cap_ajustado = inputs.get(f"cap_{winding_lower}_ajustado_pf")
            tensao = inputs.get(f"tensao_{winding_lower}_kv")

            if cap_original is not None and tensao is not None:
                # Extract calculated values
                corrente_ma = results.get(f"corrente_{winding_lower}_ma", 0.0)
                potencia_kvar = results.get(f"potencia_reativa_{winding_lower}", 0.0)
                impedancia_ohm = results.get(f"impedancia_{winding_lower}_ohm", 0.0)

                # Check for resonant configuration
                resonant_config = None
                config_info = results.get(f"config_{winding_lower}")
                if config_info:
                    resonant_config = AppliedVoltageResonantConfig(
                        nome=config_info.get("nome", ""),
                        tensao_max=config_info.get("tensao_max", 0.0),
                        cap_min=config_info.get("cap_min", 0.0),
                        cap_max=config_info.get("cap_max", 0.0),
                        frequencia=config_info.get("frequencia", 60.0),
                        viable=config_info.get("viable", True),
                        selected=config_info.get("selected", False)
                    )

                # Check for critical conditions
                critical_status = False
                status_message = "OK"

                if tensao > sut_max_voltage_kv:
                    critical_status = True
                    status_message = f"Voltage exceeds SUT limit ({sut_max_voltage_kv:.1f}kV)"
                elif cap_ajustado and (cap_ajustado < 0 or cap_ajustado > 10000):
                    critical_status = True
                    status_message = "Capacitance out of viable range"

                winding_result = AppliedVoltageWindingResult(
                    winding_name=winding,
                    capacitancia_original_pf=cap_original,
                    capacitancia_ajustada_pf=cap_ajustado or cap_original,
                    tensao_teste_kv=tensao,
                    corrente_ma=corrente_ma,
                    potencia_reativa_kvar=potencia_kvar,
                    impedancia_ohm=impedancia_ohm,
                    resonant_config=resonant_config,
                    critical_status=critical_status,
                    status_message=status_message
                )

                winding_results.append(winding_result)

        # Extract available configurations
        available_configs = []
        for config_name, config_data in const.RESONANT_SYSTEM_CONFIGS.items():
            config = AppliedVoltageResonantConfig(
                nome=config_name,
                tensao_max=config_data.get("tensao_max", 0.0),
                cap_min=config_data.get("cap_min", 0.0),
                cap_max=config_data.get("cap_max", 0.0),
                frequencia=config_data.get("frequencia", 60.0),
                viable=True,  # Will be determined during calculation
                selected=False
            )
            available_configs.append(config)

        # Determine critical validations
        voltage_exceeded = any(
            result.critical_status and "voltage" in result.status_message.lower()
            for result in winding_results
        )

        capacitance_out_of_range = any(
            result.critical_status and "capacitance" in result.status_message.lower()
            for result in winding_results
        )

        no_viable_config = len(winding_results) == 0 or all(
            result.resonant_config is None for result in winding_results
        )

        # Extract alerts and critical issues
        critical_issues = []
        alerts = []

        if voltage_exceeded:
            critical_issues.append("Test voltage exceeds SUT limits")
        if capacitance_out_of_range:
            critical_issues.append("Capacitance values out of viable range")
        if no_viable_config:
            alerts.append("No viable resonant configuration found")

        # Create serialized result
        serialized_result = AppliedVoltageResult(
            form_data=test_params,
            winding_results=winding_results,
            available_configs=available_configs,
            summary=results.get("summary", {}),
            limites_info=limits_info,
            voltage_exceeded=voltage_exceeded,
            capacitance_out_of_range=capacitance_out_of_range,
            no_viable_config=no_viable_config,
            alerts=alerts,
            critical_issues=critical_issues,
            status="completed" if not critical_issues else "partial"
        )

        return serialized_result

    except Exception as e:
        log.error(f"Error serializing applied voltage results: {e}")
        # Return minimal valid result on error
        return AppliedVoltageResult(
            form_data=AppliedVoltageTestParams(
                frequencia_hz=60.0
            ),
            limites_info=LimitsInfo(
                eps_current_limit_positive_a=2000.0,
                eps_current_limit_negative_a=-2000.0,
                dut_power_limit_kw=1350.0
            ),
            status="error",
            critical_issues=[f"Serialization error: {str(e)}"]
        )