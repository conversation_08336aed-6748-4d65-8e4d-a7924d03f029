# Helper functions

import sys
import pathlib

def setup_path_for_imports():
    """
    Ajusta o sys.path para permitir importações corretas de módulos do backend e do diretório raiz.
    Deve ser chamado no início de cada script que precisa dessas importações.
    """
    current_file = pathlib.Path(__file__).absolute()
    current_dir = current_file.parent
    backend_dir = current_dir.parent
    root_dir = backend_dir.parent

    if str(root_dir) not in sys.path:
        sys.path.insert(0, str(root_dir))
    if str(backend_dir) not in sys.path:
        sys.path.insert(0, str(backend_dir))
