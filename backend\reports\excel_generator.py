"""
Gerador de relatórios Excel para o sistema TTS.
Cria arquivos Excel com os dados dos módulos selecionados.
"""

import io
import pandas as pd
from typing import Dict, Any, List
from datetime import datetime
import json

try:
    from ..models.test_data import (
        TestDataSerializer, LossesResult, InducedVoltageResult,
        AppliedVoltageResult
    )
except ImportError:
    TestDataSerializer = None


def generate_excel_report(modules_data: Dict[str, Any], selected_modules: List[str]) -> io.BytesIO:
    """
    Gera um arquivo Excel com os dados dos módulos selecionados.
    
    Args:
        modules_data: Dicionário com os dados de cada módulo
        selected_modules: Lista dos módulos selecionados
        
    Returns:
        BytesIO: Buffer com o arquivo Excel gerado
    """
    buffer = io.BytesIO()
    
    # Criar um ExcelWriter
    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
        
        # Adicionar uma planilha de resumo
        create_summary_sheet(writer, modules_data, selected_modules)
        
        # Processar cada módulo selecionado
        for module_name in selected_modules:
            if module_name in modules_data:
                module_data_content = modules_data[module_name]
                if not isinstance(module_data_content, dict): # Ensure it's a dict
                    module_data_content = {}

                try:
                    if module_name == 'transformerInputs':
                        create_transformer_inputs_sheet(writer, module_data_content)
                    elif module_name == 'losses':
                        create_losses_sheets(writer, module_data_content)
                    elif module_name == 'appliedVoltage':
                        create_applied_voltage_sheet(writer, module_data_content)
                    elif module_name == 'inducedVoltage':
                        create_induced_voltage_sheet(writer, module_data_content)
                    elif module_name in ['impulse', 'dielectricAnalysis', 'temperatureRise', 'shortCircuit']:
                        # Placeholder for not-yet-implemented modules
                        df_not_implemented = pd.DataFrame([['Módulo não implementado nesta versão do relatório', '']], columns=['Status', 'Detalhes'])
                        df_not_implemented.to_excel(writer, sheet_name=f'{module_name[:20]} (NI)', index=False) # Truncate name if too long
                    else:
                        print(f"Módulo desconhecido '{module_name}' não será incluído no Excel.")

                except Exception as e:
                    print(f"Erro ao processar módulo {module_name} para Excel: {e}")
                    # Criar planilha de erro para o módulo
                    error_data = [['Erro ao processar dados do módulo', str(e)]]
                    df_error = pd.DataFrame(error_data, columns=['Erro', 'Detalhes'])
                    # Truncate sheet name if module_name is too long
                    error_sheet_name = f'Erro_{module_name[:25]}'
                    df_error.to_excel(writer, sheet_name=error_sheet_name, index=False)
    
    buffer.seek(0)
    return buffer


def create_serialized_losses_sheet(writer, serialized_data: LossesResult):
    """Create enhanced losses sheet from serialized data."""
    try:
        # Summary information
        summary_data = [
            ['Módulo', 'Perdas em Carga'],
            ['Status', serialized_data.status],
            ['Timestamp', serialized_data.calculation_timestamp.isoformat()],
            ['Versão', serialized_data.version],
            ['', ''],
            ['--- ALERTAS CRÍTICOS ---', '']
        ]

        for issue in serialized_data.critical_issues:
            summary_data.append(['🚨 CRÍTICO', issue])

        for alert in serialized_data.alerts:
            summary_data.append(['⚠️ ALERTA', alert])

        if not serialized_data.critical_issues and not serialized_data.alerts:
            summary_data.append(['✅ STATUS', 'Todos os cenários OK'])

        summary_data.append(['', ''])
        summary_data.append(['--- LIMITES APLICADOS ---', ''])
        summary_data.append(['Limite EPS Positivo (A)', f"{serialized_data.limites_info.eps_current_limit_positive_a:.0f}"])
        summary_data.append(['Limite EPS Negativo (A)', f"{serialized_data.limites_info.eps_current_limit_negative_a:.0f}"])
        summary_data.append(['Limite DUT (kW)', f"{serialized_data.limites_info.dut_power_limit_kw:.0f}"])
        summary_data.append(['Limite SUT (kV)', f"{serialized_data.limites_info.sut_at_max_voltage_kv:.0f}"])
        summary_data.append(['Limite CT (A)', f"{serialized_data.limites_info.ct_current_limit_a:.0f}"])

        df_summary = pd.DataFrame(summary_data, columns=['Parâmetro', 'Valor'])
        df_summary.to_excel(writer, sheet_name='Perdas - Resumo', index=False)

        # Detailed scenarios
        scenarios_data = []
        for tap_result in serialized_data.cenarios_detalhados_por_tap:
            for scenario in tap_result.cenarios_do_tap:
                row = {
                    'Tap': tap_result.nome_tap,
                    'Cenário': scenario.nome_cenario_teste,
                    'Vtest (kV)': scenario.test_params_cenario.tensao_kv,
                    'Itest (A)': scenario.test_params_cenario.corrente_a,
                    'Pativa (kW)': scenario.test_params_cenario.pativa_kw,
                    'Ptest (MVA)': scenario.test_params_cenario.pteste_mva,
                    'Q_teste (MVAr)': scenario.test_params_cenario.q_teste_mvar,
                    'Status Global': scenario.status_global,
                    'Status V≤': scenario.status_v_menor,
                    'Status V>': scenario.status_v_maior,
                    'Banco V≤ (kV)': scenario.banco_sf_data.tensao_disp_kv,
                    'Q_efetiva V≤ (MVAr)': scenario.banco_sf_data.q_efetiva_banco_mvar,
                    'Banco V> (kV)': scenario.banco_cf_data.tensao_disp_kv,
                    'Q_efetiva V> (MVAr)': scenario.banco_cf_data.q_efetiva_banco_mvar
                }
                scenarios_data.append(row)

        if scenarios_data:
            df_scenarios = pd.DataFrame(scenarios_data)
            df_scenarios.to_excel(writer, sheet_name='Perdas - Cenários', index=False)

    except Exception as e:
        print(f"Error creating serialized losses sheet: {e}")
        # Fallback to basic sheet
        create_load_losses_sheet(writer, serialized_data.dict())


def create_serialized_induced_voltage_sheet(writer, serialized_data: InducedVoltageResult):
    """Create enhanced induced voltage sheet from serialized data."""
    try:
        # Summary information
        summary_data = [
            ['Módulo', 'Tensão Induzida'],
            ['Status', serialized_data.status],
            ['Timestamp', serialized_data.calculation_timestamp.isoformat()],
            ['Versão', serialized_data.version],
            ['', ''],
            ['--- PARÂMETROS DE TESTE ---', ''],
            ['Frequência de Teste (Hz)', serialized_data.form_data.freq_teste],
            ['Capacitância (nF)', serialized_data.form_data.capacitancia],
            ['Tipo Transformador', serialized_data.form_data.tipo_transformador],
            ['Tipo de Aço', serialized_data.form_data.tipo_aco],
            ['', ''],
            ['--- VALIDAÇÕES CRÍTICAS ---', '']
        ]

        if serialized_data.sut_voltage_exceeded:
            summary_data.append(['🚨 CRÍTICO', 'Tensão SUT excedida'])
        if serialized_data.eps_estimate_exceeded:
            summary_data.append(['🚨 CRÍTICO', 'Estimativa EPS excede limite DUT'])
        if serialized_data.frequency_out_of_range:
            summary_data.append(['⚠️ ALERTA', 'Frequência fora da faixa recomendada'])

        if not any([serialized_data.sut_voltage_exceeded, serialized_data.eps_estimate_exceeded, serialized_data.frequency_out_of_range]):
            summary_data.append(['✅ STATUS', 'Todos os parâmetros OK'])

        df_summary = pd.DataFrame(summary_data, columns=['Parâmetro', 'Valor'])
        df_summary.to_excel(writer, sheet_name='Induzida - Resumo', index=False)

        # Calculation results
        calc_data = []
        for calc in serialized_data.calculation_results:
            row = {
                'Frequência (Hz)': calc.freq_hz,
                'Indução (T)': calc.inducao,
                'Pot. Magnética (kVA)': calc.pot_magnetica,
                'Pot. Ativa (kW)': calc.pot_ativa,
                'Pot. Induzida (kVAr)': calc.pot_induzida,
                'Pcap (kW)': calc.pcap,
                'Scap/Sind': calc.scap_sind_ratio,
                'SUT Tensão (kV)': calc.sut_analysis.sut_voltage_kv,
                'SUT Frequência (Hz)': calc.sut_analysis.sut_frequency_hz,
                'SUT Potência (kVA)': calc.sut_analysis.sut_power_kva,
                'SUT OK': 'Sim' if calc.sut_analysis.sut_within_limits else 'Não',
                'Status': calc.status_validation.status_global
            }
            calc_data.append(row)

        if calc_data:
            df_calc = pd.DataFrame(calc_data)
            df_calc.to_excel(writer, sheet_name='Induzida - Cálculos', index=False)

    except Exception as e:
        print(f"Error creating serialized induced voltage sheet: {e}")
        # Fallback to basic sheet
        create_induced_voltage_sheet(writer, serialized_data.dict())


def create_serialized_applied_voltage_sheet(writer, serialized_data: AppliedVoltageResult):
    """Create enhanced applied voltage sheet from serialized data."""
    try:
        # Summary information
        summary_data = [
            ['Módulo', 'Tensão Aplicada'],
            ['Status', serialized_data.status],
            ['Timestamp', serialized_data.calculation_timestamp.isoformat()],
            ['Versão', serialized_data.version],
            ['', ''],
            ['--- PARÂMETROS DE TESTE ---', ''],
            ['Frequência (Hz)', serialized_data.form_data.frequencia_hz],
            ['', ''],
            ['--- VALIDAÇÕES CRÍTICAS ---', '']
        ]

        if serialized_data.voltage_exceeded:
            summary_data.append(['🚨 CRÍTICO', 'Tensão excede limites SUT'])
        if serialized_data.capacitance_out_of_range:
            summary_data.append(['🚨 CRÍTICO', 'Capacitância fora da faixa viável'])
        if serialized_data.no_viable_config:
            summary_data.append(['⚠️ ALERTA', 'Nenhuma configuração ressonante viável'])

        if not any([serialized_data.voltage_exceeded, serialized_data.capacitance_out_of_range, serialized_data.no_viable_config]):
            summary_data.append(['✅ STATUS', 'Todos os parâmetros OK'])

        df_summary = pd.DataFrame(summary_data, columns=['Parâmetro', 'Valor'])
        df_summary.to_excel(writer, sheet_name='Aplicada - Resumo', index=False)

        # Winding results
        winding_data = []
        for winding in serialized_data.winding_results:
            row = {
                'Enrolamento': winding.winding_name,
                'Cap. Original (pF)': winding.capacitancia_original_pf,
                'Cap. Ajustada (pF)': winding.capacitancia_ajustada_pf,
                'Tensão Teste (kV)': winding.tensao_teste_kv,
                'Corrente (mA)': winding.corrente_ma,
                'Pot. Reativa (kVAr)': winding.potencia_reativa_kvar,
                'Impedância (Ω)': winding.impedancia_ohm,
                'Config. Ressonante': winding.resonant_config.nome if winding.resonant_config else 'N/A',
                'Status Crítico': 'Sim' if winding.critical_status else 'Não',
                'Mensagem': winding.status_message
            }
            winding_data.append(row)

        if winding_data:
            df_windings = pd.DataFrame(winding_data)
            df_windings.to_excel(writer, sheet_name='Aplicada - Enrolamentos', index=False)

        # Available configurations
        config_data = []
        for config in serialized_data.available_configs:
            row = {
                'Nome': config.nome,
                'Tensão Máx (kV)': config.tensao_max,
                'Cap. Mín (nF)': config.cap_min,
                'Cap. Máx (nF)': config.cap_max,
                'Frequência (Hz)': config.frequencia,
                'Viável': 'Sim' if config.viable else 'Não',
                'Selecionada': 'Sim' if config.selected else 'Não'
            }
            config_data.append(row)

        if config_data:
            df_configs = pd.DataFrame(config_data)
            df_configs.to_excel(writer, sheet_name='Aplicada - Configurações', index=False)

    except Exception as e:
        print(f"Error creating serialized applied voltage sheet: {e}")
        # Fallback to basic sheet
        create_applied_voltage_sheet(writer, serialized_data.dict())


def generate_enhanced_excel_report(modules_data: Dict[str, Any], selected_modules: List[str]) -> io.BytesIO:
    """
    Generate enhanced Excel report using serialized data when available.
    Falls back to original format for compatibility.
    """
    buffer = io.BytesIO()

    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
        create_summary_sheet(writer, modules_data, selected_modules)

        for module_name in selected_modules:
            if module_name not in modules_data:
                continue

            module_data_content = modules_data[module_name]

            try:
                # Try to use enhanced serialized format first
                if TestDataSerializer and isinstance(module_data_content, dict) and "data" in module_data_content:
                    serialized_json = module_data_content["data"]

                    if module_name == 'losses':
                        serialized_data = TestDataSerializer.deserialize_from_json(serialized_json, LossesResult)
                        create_serialized_losses_sheet(writer, serialized_data)
                    elif module_name == 'inducedVoltage':
                        serialized_data = TestDataSerializer.deserialize_from_json(serialized_json, InducedVoltageResult)
                        create_serialized_induced_voltage_sheet(writer, serialized_data)
                    elif module_name == 'appliedVoltage':
                        serialized_data = TestDataSerializer.deserialize_from_json(serialized_json, AppliedVoltageResult)
                        create_serialized_applied_voltage_sheet(writer, serialized_data)
                    else:
                        # Fallback to original format
                        create_module_sheet_original(writer, module_name, module_data_content)
                else:
                    # Fallback to original format
                    create_module_sheet_original(writer, module_name, module_data_content)

            except Exception as e:
                print(f"Error creating enhanced sheet for {module_name}: {e}")
                # Fallback to original format
                create_module_sheet_original(writer, module_name, module_data_content)

    buffer.seek(0)
    return buffer


def create_module_sheet_original(writer, module_name: str, module_data_content: Dict[str, Any]):
    """Create module sheet using original format (fallback)."""
    if module_name == 'transformerInputs':
        create_transformer_inputs_sheet(writer, module_data_content)
    elif module_name == 'losses':
        create_losses_sheets(writer, module_data_content)
    elif module_name == 'appliedVoltage':
        create_applied_voltage_sheet(writer, module_data_content)
    elif module_name == 'inducedVoltage':
        create_induced_voltage_sheet(writer, module_data_content)
    else:
        # Placeholder for not-yet-implemented modules
        df_not_implemented = pd.DataFrame([['Módulo não implementado nesta versão do relatório', '']], columns=['Status', 'Detalhes'])
        df_not_implemented.to_excel(writer, sheet_name=f'{module_name[:20]} (NI)', index=False)


def create_summary_sheet(writer, modules_data: Dict[str, Any], selected_modules: List[str]):
    """Cria a planilha de resumo com informações gerais."""
    
    summary_data = []
    summary_data.append(['Relatório de Dados TTS', ''])
    summary_data.append(['Data de Geração', datetime.now().strftime('%d/%m/%Y %H:%M:%S')])
    summary_data.append(['', ''])
    summary_data.append(['Módulos Incluídos:', ''])
    
    module_names = {
        'transformerInputs': 'Dados Básicos do Transformador',
        'losses': 'Análise de Perdas',
        'appliedVoltage': 'Ensaio de Tensão Aplicada',
        'inducedVoltage': 'Ensaio de Tensão Induzida',
        'impulse': 'Ensaio de Impulso',
        'dielectricAnalysis': 'Análise Dielétrica',
        'temperatureRise': 'Elevação de Temperatura',
        'shortCircuit': 'Curto-Circuito'
    }
    
    for module in selected_modules:
        status = 'Incluído'
        if module in ['impulse', 'dielectricAnalysis', 'temperatureRise', 'shortCircuit']:
            status = 'Não implementado nesta versão'
        elif module not in modules_data:
            status = 'Dados não encontrados'
        
        summary_data.append([f'- {module_names.get(module, module)}', status])
    
    df_summary = pd.DataFrame(summary_data, columns=['Item', 'Valor'])
    df_summary.to_excel(writer, sheet_name='Resumo', index=False)


def create_transformer_inputs_sheet(writer, module_data: Dict[str, Any]):
    """Cria a planilha com dados básicos do transformador."""

    form_data = module_data.get('formData', {}) if isinstance(module_data.get('formData'), dict) else {}
    
    if not form_data: # Prioritize formData
        df_empty = pd.DataFrame([['Nenhum dado de entrada do transformador disponível', '']], columns=['Parâmetro', 'Valor'])
        df_empty.to_excel(writer, sheet_name='Dados Básicos', index=False)
        return
    
    data_list = []
    def add_entry(label, key, unit=""):
        value = form_data.get(key)
        if value is not None and value != '':
            data_list.append([label, f"{value} {unit}".strip()])
        else:
            data_list.append([label, "-"])

    data_list.append(['=== DADOS GERAIS ===', ''])
    add_entry('Potência (MVA)', 'potencia_mva', 'MVA')
    add_entry('Frequência (Hz)', 'frequencia', 'Hz')
    add_entry('Tipo', 'tipo_transformador')
    add_entry('Grupo de Ligação', 'grupo_ligacao')
    add_entry('Líquido Isolante', 'liquido_isolante')
    add_entry('Tipo de Isolamento', 'tipo_isolamento')
    add_entry('Norma', 'norma_iso')
    add_entry('Elevação Óleo Topo (°C/K)', 'elevacao_oleo_topo', '°C/K')
    add_entry('Elevação Enrolamento (°C)', 'elevacao_enrol', '°C')
    add_entry('Peso Parte Ativa (ton)', 'peso_parte_ativa', 'ton')
    add_entry('Peso Tanque & Acessórios (ton)', 'peso_tanque_acessorios', 'ton')
    add_entry('Peso Óleo (ton)', 'peso_oleo', 'ton')
    add_entry('Peso Total (ton)', 'peso_total', 'ton')
    data_list.append(['', ''])
    
    for winding_prefix, winding_name in [('at', 'AT'), ('bt', 'BT'), ('terciario', 'Terciário')]:
        if not form_data.get(f'tensao_{winding_prefix}') and winding_prefix == 'terciario': # Skip empty tertiary
            continue
        data_list.append([f'=== ENROLAMENTO {winding_name} ===', ''])
        add_entry(f'Tensão {winding_name} (kV)', f'tensao_{winding_prefix}', 'kV')
        add_entry(f'Corrente Nominal {winding_name} (A)', f'corrente_nominal_{winding_prefix}', 'A')
        add_entry(f'Classe de Tensão {winding_name} (kV)', f'classe_tensao_{winding_prefix}', 'kV')
        add_entry(f'Conexão {winding_name}', f'conexao_{winding_prefix}')
        add_entry(f'NBI {winding_name} (kVp)', f'nbi_{winding_prefix}', 'kVp')
        add_entry(f'SIL {winding_name} (kVp)', f'sil_{winding_prefix}', 'kVp')
        if form_data.get(f'tensao_bucha_neutro_{winding_prefix}'):
             add_entry(f'Classe Neutro {winding_name} (kV)', f'tensao_bucha_neutro_{winding_prefix}', 'kV')
             add_entry(f'NBI Neutro {winding_name} (kVp)', f'nbi_neutro_{winding_prefix}', 'kVp')
             add_entry(f'SIL Neutro {winding_name} (kVp)', f'sil_neutro_{winding_prefix}', 'kVp')
        if winding_prefix == 'at':
            add_entry('Impedância (%)', 'impedancia', '%')
            data_list.append(['--- TAPs AT ---', ''])
            add_entry('Tensão AT Tap Maior (kV)', 'tensao_at_tap_maior', 'kV')
            add_entry('Corrente AT Tap Maior (A)', 'corrente_nominal_at_tap_maior', 'A')
            add_entry('Impedância Tap Maior (%)', 'impedancia_tap_maior', '%')
            add_entry('Tensão AT Tap Menor (kV)', 'tensao_at_tap_menor', 'kV')
            add_entry('Corrente AT Tap Menor (A)', 'corrente_nominal_at_tap_menor', 'A')
            add_entry('Impedância Tap Menor (%)', 'impedancia_tap_menor', '%')
        data_list.append(['--- TENSÕES DE ENSAIO ---', ''])
        add_entry(f'Tensão Aplicada {winding_name} (kVrms)', f'teste_tensao_aplicada_{winding_prefix}', 'kVrms')
        if winding_prefix == 'at': # Induzida só na AT
            add_entry(f'Tensão Induzida {winding_name} (kVrms)', f'teste_tensao_induzida_{winding_prefix}', 'kVrms')
        data_list.append(['', ''])

    df_basic = pd.DataFrame(data_list, columns=['Parâmetro', 'Valor'])
    df_basic.to_excel(writer, sheet_name='Dados Básicos', index=False)


def create_losses_sheets(writer, module_data: Dict[str, Any]):
    """Cria as planilhas com dados de perdas (vazio e carga)."""
    create_no_load_losses_sheet(writer, module_data)
    create_load_losses_sheet(writer, module_data)

def create_no_load_losses_sheet(writer, module_data: Dict[str, Any]):
    """Cria a planilha com dados de perdas em vazio."""
    form_data = module_data.get('formData', {})
    results_no_load = module_data.get('resultsNoLoad', {})
    
    data_list = []
    data_list.append(['=== PERDAS EM VAZIO - ENTRADAS ===', ''])
    data_list.append(['Perdas em Vazio (kW)', form_data.get('perdas-vazio-kw', '-')])
    data_list.append(['Peso do Núcleo (Ton)', form_data.get('peso-projeto-Ton', '-')])
    data_list.append(['Corrente Excitação (%)', form_data.get('corrente-excitacao', '-')])
    data_list.append(['Indução Núcleo (T)', form_data.get('inducao-nucleo', '-')])
    data_list.append(['Corrente Exc. 1.1pu (%)', form_data.get('corrente-excitacao-1-1', '-')])
    data_list.append(['Corrente Exc. 1.2pu (%)', form_data.get('corrente-excitacao-1-2', '-')])
    data_list.append(['Tipo de Aço', form_data.get('tipo-aco', '-')])
    data_list.append(['', ''])

    if results_no_load and isinstance(results_no_load, dict):
        params_comp = results_no_load.get('parametros_gerais_comparativo', {})
        data_list.append(['=== PERDAS EM VAZIO - COMPARATIVO GERAL ===', ''])
        data_list.append(['Fonte de Tensão Usada', params_comp.get('fonte_tensao_usada', '-')])
        data_list.append(['Status Terciário', params_comp.get('status_terciario', '-')])
        data_list.append(['Tensão Nominal Fonte (kV)', params_comp.get('tensao_nominal_bt_kv', '-')])
        data_list.append(['Frequência (Hz)', params_comp.get('frequencia_hz', '-')])
        data_list.append(['', ''])
        data_list.append(['Parâmetro', 'Projeto', f'Aço ({form_data.get("tipo-aco", "M4")})'])
        data_list.append(['Potência Mag. (kVAr)', params_comp.get('potencia_mag_kvar_projeto', '-'), params_comp.get('potencia_mag_kvar_aco_m4', '-')])
        data_list.append(['Fator Perdas Mag. (VAr/kg)', params_comp.get('fator_perdas_mag_kvar_kg_projeto', '-'), params_comp.get('fator_perdas_mag_var_kg_aco_m4', '-')])
        data_list.append(['Fator Perdas (W/kg)', params_comp.get('fator_perdas_kw_kg_projeto', '-'), params_comp.get('fator_perdas_kw_kg_aco_m4', '-')])
        data_list.append(['Peso Núcleo (Ton)', params_comp.get('peso_nucleo_ton_projeto', '-'), params_comp.get('peso_nucleo_ton_aco_m4', '-')])
        data_list.append(['Corrente Excitação (%)', params_comp.get('corrente_excitacao_perc_projeto', '-'), params_comp.get('corrente_excitacao_perc_aco_m4', '-')])
        data_list.append(['', ''])

        resultados_dut = results_no_load.get('resultados_dut_niveis_tensao', {})
        if resultados_dut:
            data_list.append(['=== RESULTADOS DUT POR NÍVEL DE TENSÃO ===', ''])
            data_list.append(['Nível PU', 'Origem', 'Tensão Teste (kV)', 'Corrente Exc. (A)', 'Corrente Exc. (%)', 'Potência Ensaio (kVA)', 'Potência Ativa (kW)'])
            for pu_level_key, pu_label in [('1_0_pu', '1.0 pu'), ('1_1_pu', '1.1 pu'), ('1_2_pu', '1.2 pu')]:
                for origem in ['projeto', 'aco_m4']:
                    if origem in resultados_dut and isinstance(resultados_dut[origem], dict):
                        data_origem = resultados_dut[origem]
                        tensao_key = f'tensao_{pu_level_key}_kv'
                        corr_a_key = f'corrente_{pu_level_key}_a'
                        corr_perc_key = f'corrente_{pu_level_key}_perc_nominal_bt'
                        pot_kva_key = f'potencia_{pu_level_key}_kva'
                        pot_kw_key = f'potencia_ativa_{pu_level_key}_kw'
                        
                        data_list.append([
                            pu_label,
                            f'{origem.capitalize()} ({form_data.get("tipo-aco","M4") if origem == "aco_m4" else ""})'.strip(),
                            data_origem.get(tensao_key, '-'),
                            data_origem.get(corr_a_key, '-'),
                            data_origem.get(corr_perc_key, '-'),
                            data_origem.get(pot_kva_key, '-'),
                            data_origem.get(pot_kw_key, '-')
                        ])
            data_list.append(['', ''])
        
        validacao_eps = results_no_load.get('validacao_eps_power', {})
        if validacao_eps:
            data_list.append(['=== VALIDAÇÃO EPS (POTÊNCIA) ===', ''])
            data_list.append(['Tem Impedimento?', validacao_eps.get('tem_impedimento', '-')])
            if validacao_eps.get('tem_impedimento'):
                 data_list.append(['Níveis Críticos', ', '.join(validacao_eps.get('niveis_criticos', []))])
            for nivel, alerta_info in validacao_eps.get('alertas_por_nivel', {}).items():
                data_list.append([f'Alerta {nivel}', f"Potência: {alerta_info.get('potencia_ensaio_kva','-')} kVA, Excede: {alerta_info.get('excede_limite','-')}"])

        validacao_ct = results_no_load.get('validacao_ct_current', {})
        if validacao_ct:
            data_list.append(['=== VALIDAÇÃO CT (CORRENTE) ===', ''])
            data_list.append(['Tem Impedimento?', validacao_ct.get('tem_impedimento', '-')])
            if validacao_ct.get('tem_impedimento'):
                 data_list.append(['Níveis Críticos', ', '.join(validacao_ct.get('niveis_criticos', []))])
            for nivel, alerta_info in validacao_ct.get('alertas_por_nivel', {}).items():
                data_list.append([f'Alerta {nivel}', f"Corrente: {alerta_info.get('corrente_teste_a','-')} A, Excede CT: {alerta_info.get('excede_limite_ct','-')}"])


    df_no_load = pd.DataFrame(data_list) # Let pandas handle columns
    df_no_load.to_excel(writer, sheet_name='Perdas em Vazio', index=False, header=False)


def create_load_losses_sheet(writer, module_data: Dict[str, Any]):
    """Cria a planilha com dados de perdas em carga."""
    form_data = module_data.get('formData', {})
    results_load = module_data.get('resultsLoad', {})

    data_list = []
    data_list.append(['=== PERDAS EM CARGA - ENTRADAS ===', ''])
    data_list.append(['Temperatura de Referência (°C)', form_data.get('temperatura-referencia', '-')])
    data_list.append(['Perdas em Carga Totais Tap- (kW)', form_data.get('perdas-carga-kw_U_min', '-')])
    data_list.append(['Perdas em Carga Totais Tap Nominal (kW)', form_data.get('perdas-carga-kw_U_nom', '-')])
    data_list.append(['Perdas em Carga Totais Tap Maior (kW)', form_data.get('perdas-carga-kw_U_max', '-')])
    data_list.append(['Fator Sobretensão Banco V>', form_data.get('factor-cap-banc-overvoltage', '-')])
    data_list.append(['', ''])

    if results_load and isinstance(results_load, dict):
        # Seção de Limites do Sistema
        limites = results_load.get('limites_info', {})
        if limites:
            data_list.append(['=== LIMITES DO SISTEMA PARA O ENSAIO ===', ''])
            data_list.append(['Limite de Corrente EPS (A)', limites.get('eps_current_limit_positive_a', '-')])
            data_list.append(['Limite de Potência DUT (kW)', limites.get('dut_power_limit_kw', '-')])
            data_list.append(['', ''])

        cond_nom = results_load.get('condicoes_nominais', {})
        data_list.append(['=== PERDAS EM CARGA - CONDIÇÕES NOMINAIS ===', ''])
        data_list.append(['Parâmetro', 'Tap Nominal', 'Tap Menor', 'Tap Maior'])
        data_list.append(['Temperatura Ref. (°C)', cond_nom.get('temperatura_referencia', '-'), cond_nom.get('temperatura_referencia', '-'), cond_nom.get('temperatura_referencia', '-')])
        data_list.append(['Tensão AT (kV)', cond_nom.get('tensao_at_kv_nominal', '-'), cond_nom.get('tensao_at_kv_menor', '-'), cond_nom.get('tensao_at_kv_maior', '-')])
        data_list.append(['Corrente AT (A)', cond_nom.get('corrente_at_a_nominal', '-'), cond_nom.get('corrente_at_a_menor', '-'), cond_nom.get('corrente_at_a_maior', '-')])
        data_list.append(['Vcc (%)', cond_nom.get('vcc_percent_nominal', '-'), cond_nom.get('vcc_percent_menor', '-'), cond_nom.get('vcc_percent_maior', '-')])
        data_list.append(['Vcc (kV)', cond_nom.get('vcc_kv_nominal', '-'), cond_nom.get('vcc_kv_menor', '-'), cond_nom.get('vcc_kv_maior', '-')])
        data_list.append([f"Perdas em Carga Totais ({cond_nom.get('temperatura_referencia', 'Tref')}°C) (kW)", cond_nom.get('perdas_totais_kw_nominal_tref', '-'), cond_nom.get('perdas_totais_kw_menor_tref', '-'), cond_nom.get('perdas_totais_kw_maior_tref', '-')])
        data_list.append([f"Perdas em Carga - Perdas em Vazio ({cond_nom.get('temperatura_referencia', 'Tref')}°C) (kW)", cond_nom.get('perdas_carga_s_vazio_kw_nominal_tref', '-'), cond_nom.get('perdas_carga_s_vazio_kw_menor_tref', '-'), cond_nom.get('perdas_carga_s_vazio_kw_maior_tref', '-')])
        data_list.append(['Perdas em Carga a (25°C) (kW)', cond_nom.get('perdas_frio_25c_kw_nominal', '-'), cond_nom.get('perdas_frio_25c_kw_menor', '-'), cond_nom.get('perdas_frio_25c_kw_maior', '-')])
        data_list.append(['Perdas em Vazio (kW)', cond_nom.get('perdas_vazio_kw_usada_calculo', '-'), cond_nom.get('perdas_vazio_kw_usada_calculo', '-'), cond_nom.get('perdas_vazio_kw_usada_calculo', '-')])
        data_list.append(['Info Geral Banco Cap.', cond_nom.get('info_geral_banco_capacitores','-')])
        data_list.append(['Alertas Gerais Status', ', '.join(cond_nom.get('alertas_gerais_status',[])) if cond_nom.get('alertas_gerais_status') else '-'])

        data_list.append(['', ''])
        cenarios = results_load.get('cenarios_detalhados_por_tap', [])
        if cenarios:
            data_list.append(['=== PERDAS EM CARGA - CENÁRIOS DETALHADOS ===', ''])
            # Cabeçalhos expandidos
            headers = [
                'Tap DUT', 'Cenário Teste', 'Alerta Global', 'Vtest (kV)', 'Itest (A)', 'P_ativa (kW)', 'P_teste (MVA)', 'Q_teste (MVAr)', 'Q Req (MVAr)',
                'Banco SF V (kV)', 'Banco SF Qn (MVAr)', 'Banco SF Qef (MVAr)',
                'SUT Tap Ideal (kV)', 'I_EPS SF (A)', '% Limite EPS SF', 'Status Sobrecorrente SF',
                'Banco CF V (kV)', 'Banco CF Qn (MVAr)', 'Banco CF Qef (MVAr)',
                'SUT Tap Ideal CF (kV)', 'I_EPS CF (A)', '% Limite EPS CF', 'Status Sobrecorrente CF'
            ]
            data_list.append(headers)

            for tap_data in cenarios:
                if isinstance(tap_data, dict) and 'cenarios_do_tap' in tap_data:
                    for cenario in tap_data['cenarios_do_tap']:
                        if isinstance(cenario, dict):
                            tp = cenario.get('test_params_cenario', {})
                            
                            # Análise para SF (Sem Fator de Potência)
                            sf_bank_config = next((conf for conf in cenario.get('cap_bank_sf', {}).get('available_configurations', []) if conf.get('is_default')), {})
                            sut_analysis_sf = next((s for s in cenario.get('sut_eps_analysis', []) if s.get('is_ideal_tap')), {})
                            
                            # Análise para CF (Com Fator de Potência)
                            cf_bank_config = next((conf for conf in cenario.get('cap_bank_cf', {}).get('available_configurations', []) if conf.get('is_default')), {})
                            sut_analysis_cf = next((s for s in cenario.get('sut_eps_analysis', []) if s.get('is_ideal_tap')), {}) # Nota: A lógica pode precisar de um sut_eps_analysis_cf dedicado se existir

                            row_data = [
                                tap_data.get('nome_tap', '-'), 
                                cenario.get('nome_cenario_teste', '-'),
                                cenario.get('status_global', '-'),
                                tp.get('tensao_kv', '-'), 
                                tp.get('corrente_a', '-'), 
                                tp.get('pativa_kw', '-'), 
                                tp.get('pteste_mva', '-'), 
                                tp.get('q_teste_mvar', '-'),
                                cenario.get('cap_required_mvar', '-'),
                                # Dados do Banco SF
                                cenario.get('cap_bank_sf', {}).get('tensao_disp_kv', '-'),
                                sf_bank_config.get('q_provided_mvar', '-'),
                                sf_bank_config.get('q_efetiva_banco_mvar', '-'),
                                # Análise SUT/EPS para SF
                                sut_analysis_sf.get('sut_tap_kv', '-'),
                                sut_analysis_sf.get('corrente_eps_sf_a', '-'),
                                sut_analysis_sf.get('percent_limite_sf', '-'),
                                'Sim' if sut_analysis_sf.get('is_over_positive_sf') else 'Não',
                                # Dados do Banco CF
                                cenario.get('cap_bank_cf', {}).get('tensao_disp_kv', '-'),
                                cf_bank_config.get('q_provided_mvar', '-'),
                                cf_bank_config.get('q_efetiva_banco_mvar', '-'),
                                # Análise SUT/EPS para CF (assumindo mesma estrutura)
                                sut_analysis_cf.get('sut_tap_kv', '-'),
                                sut_analysis_cf.get('corrente_eps_cf_a', '-'),
                                sut_analysis_cf.get('percent_limite_cf', '-'),
                                'Sim' if sut_analysis_cf.get('is_over_positive_cf') else 'Não'
                            ]
                            data_list.append(row_data)

    df_load = pd.DataFrame(data_list) # Let pandas handle columns
    df_load.to_excel(writer, sheet_name='Perdas em Carga', index=False, header=False)



def create_applied_voltage_sheet(writer, module_data: Dict[str, Any]):
    """Cria a planilha com dados de tensão aplicada."""
    form_data = module_data.get('formData', {}) # User inputs like cap_at, cap_bt, cap_ter
    results = module_data.get('results', {}) # Calculated results from service
    
    data_list = []
    data_list.append(['=== ENSAIO DE TENSÃO APLICADA ===', ''])
    
    # Entradas do Usuário
    data_list.append(['--- Entradas do Usuário ---', ''])
    data_list.append(['Capacitância AT - GND (pF)', form_data.get('cap_at', '-')])
    data_list.append(['Capacitância BT - GND (pF)', form_data.get('cap_bt', '-')])
    data_list.append(['Capacitância Terciário - GND (pF)', form_data.get('cap_ter', '-')])
    data_list.append(['Frequência (Hz)', results.get('frequencia', form_data.get('frequencia', '-'))]) # Frequencia pode vir dos results
    data_list.append(['', ''])

    # Resultados Calculados
    if results and isinstance(results, dict):
        data_list.append(['--- Resultados Calculados ---', ''])
        data_list.append(['Lado', 'Tensão Ensaio (kV)', 'Cap. Ajustada (pF)', 'Corrente (A)', 'Pot. Reativa (MVAr)'])
        
        if results.get('tensao_teste_at') is not None and results.get('tensao_teste_at') > 0:
            data_list.append([
                'AT', 
                results.get('tensao_teste_at', '-'), 
                results.get('capacitancia_at', '-'),
                f"{results.get('corrente_teste_at', 0):.3f}" if results.get('corrente_teste_at') is not None else '-',
                f"{(results.get('potencia_reativa_at', 0) / 1e6):.4f}" if results.get('potencia_reativa_at') is not None else '-'
            ])
        if results.get('tensao_teste_bt') is not None and results.get('tensao_teste_bt') > 0:
            data_list.append([
                'BT', 
                results.get('tensao_teste_bt', '-'), 
                results.get('capacitancia_bt', '-'),
                f"{results.get('corrente_teste_bt', 0):.3f}" if results.get('corrente_teste_bt') is not None else '-',
                f"{(results.get('potencia_reativa_bt', 0) / 1e6):.4f}" if results.get('potencia_reativa_bt') is not None else '-'
            ])
        if results.get('tensao_teste_terciario') is not None and results.get('tensao_teste_terciario') > 0:
            data_list.append([
                'Terciário', 
                results.get('tensao_teste_terciario', '-'), 
                results.get('capacitancia_terciario', '-'),
                f"{results.get('corrente_teste_terciario', 0):.3f}" if results.get('corrente_teste_terciario') is not None else '-',
                f"{(results.get('potencia_reativa_terciario', 0) / 1e6):.4f}" if results.get('potencia_reativa_terciario') is not None else '-'
            ])
        data_list.append(['', ''])
        
        # Análise de Viabilidade Ressonante
        analise_viabilidade = results.get('analise_viabilidade_ressonante', {})
        if analise_viabilidade:
            data_list.append(['--- Análise Sistema Ressonante ---', ''])
            data_list.append(['Lado', 'Recomendação'])
            if analise_viabilidade.get('at'):
                data_list.append(['AT', analise_viabilidade.get('at', '-')])
            if analise_viabilidade.get('bt'):
                data_list.append(['BT', analise_viabilidade.get('bt', '-')])
            if analise_viabilidade.get('terciario') and results.get('tensao_teste_terciario', 0) > 0 :
                data_list.append(['Terciário', analise_viabilidade.get('terciario', '-')])
    
    df_applied = pd.DataFrame(data_list)
    df_applied.to_excel(writer, sheet_name='Tensão Aplicada', index=False, header=False)


def create_induced_voltage_sheet(writer, module_data: Dict[str, Any]):
    """Cria a planilha com dados de tensão induzida."""
    form_data = module_data.get('formData', {}) # User inputs: frequencia-teste, capacitancia, tipo-transformador-induced
    results = module_data.get('results', {})   # Calculated results from service
    
    data_list = []
    data_list.append(['=== ENSAIO DE TENSÃO INDUZIDA ===', ''])

    # Entradas do Usuário e do Transformador
    data_list.append(['--- Entradas Chave ---', ''])
    data_list.append(['Tipo de Transformador (Ensaio)', form_data.get('tipo-transformador-induced', results.get('tipo_transformador', '-'))])
    data_list.append(['Frequência de Teste (Hz)', form_data.get('frequencia-teste', results.get('frequencia_teste', '-'))])
    data_list.append(['Capacitância AT-GND (pF)', form_data.get('capacitancia', results.get('capacitancia', '-'))])
    data_list.append(['Tensão de Ensaio (Prova) (kV)', results.get('tensao_induzida', '-')]) # Vem dos dados básicos do trafo
    data_list.append(['Indução Nominal (T)', results.get('inducao_nominal', '-')])
    data_list.append(['Peso do Núcleo (Ton)', results.get('peso_nucleo', '-')])
    data_list.append(['Tipo de Aço', results.get('tipo_aco', '-')])
    data_list.append(['', ''])
    
    if results and isinstance(results, dict):
        data_list.append(['--- Resultados Calculados ---', ''])
        data_list.append(['Indução no Teste (β)', f"{results.get('inducao_teste', 0):.3f} T" if results.get('inducao_teste') is not None else '-'])
        data_list.append(['Tensão Aplicada na Fonte (BT ou Terciário)', f"{results.get('tensao_aplicada_bt', 0):.2f} kV" if results.get('tensao_aplicada_bt') is not None else '-'])
        data_list.append(['Fonte de Tensão Usada', results.get('sut_tertiary_info',{}).get('voltage_source','-')])
        data_list.append(['Status SUT/Terciário', results.get('sut_tertiary_info',{}).get('status','-')])
        
        data_list.append(['Potência Ativa (Pw)', f"{results.get('pot_ativa', 0):.2f} kW" if results.get('pot_ativa') is not None else '-'])
        data_list.append(['Potência Magnética (Sm)', f"{results.get('pot_magnetica', 0):.2f} kVA" if results.get('pot_magnetica') is not None else '-'])
        data_list.append(['Corrente de Excitação (Iexc)', f"{results.get('corrente_excitacao', 0):.2f} A" if results.get('corrente_excitacao') is not None else '-'])

        if results.get('tipo_transformador') == 'Monofásico':
            data_list.append(['Componente Indutiva (Sind)', f"{results.get('pot_induzida', 0):.2f} kVAr ind" if results.get('pot_induzida') is not None else '-'])
            data_list.append(['U para cálculo de Scap', f"{results.get('u_dif', 0):.2f} kV" if results.get('u_dif') is not None else '-'])
            data_list.append(['Potência Capacitiva (Scap)', f"{results.get('pcap', 0):.2f} kVAr cap" if results.get('pcap') is not None else '-'])
        else: # Trifásico
            data_list.append(['Potência de Teste (Total)', f"{results.get('potencia_teste', 0):.2f} kVA" if results.get('potencia_teste') is not None else '-'])
        data_list.append(['', ''])

        eps_analysis = results.get('eps_analysis', {})
        if eps_analysis:
            data_list.append(['--- Análise do EPS ---', ''])
            data_list.append(['Status EPS', eps_analysis.get('status', '-')])
            data_list.append(['Resumo EPS', eps_analysis.get('summary', '-')])
            if eps_analysis.get('violations'):
                for i, viol in enumerate(eps_analysis['violations']):
                    data_list.append([f'Violação EPS {i+1}', f"{viol.get('type')}: {viol.get('description')} ({viol.get('percent_over',0):.1f}% over)"])
            if eps_analysis.get('warnings'):
                for i, warn in enumerate(eps_analysis['warnings']):
                    data_list.append([f'Aviso EPS {i+1}', f"{warn.get('type')}: {warn.get('description')} ({warn.get('percent_usage',0):.1f}% usage)"])
            
            sut_analysis = eps_analysis.get('sut_analysis', {})
            if sut_analysis.get('recommended_taps'):
                data_list.append(['', ''])
                data_list.append(['Taps SUT Recomendados', 'Corrente EPS (A)', '% Limite', 'Status'])
                for tap in sut_analysis['recommended_taps']:
                    data_list.append([
                        f"{tap.get('sut_tap_kv', '-')} kV",
                        f"{tap.get('corrente_eps_a', '-')} A",
                        f"{tap.get('percent_limite_eps', '-')}%",
                        tap.get('status', '-')
                    ])
        data_list.append(['', ''])
        
        freq_analysis = results.get('frequency_analysis_table', {}) # O service retorna como 'frequency_analysis_table'
        if freq_analysis and freq_analysis.get('table_data'):
            data_list.append(['--- Análise de Frequências ---', ''])
            if freq_analysis.get('tipo_transformador') == 'Monofásico':
                data_list.append(['Frequência (Hz)', 'Pw (kW)', 'Sm (kVA)', 'Sind (kVAr ind)', 'Scap (kVAr cap)', 'Scap/Sind'])
                for row in freq_analysis['table_data']:
                    data_list.append([
                        row.get('frequencia', '-'), f"{row.get('pot_ativa', 0):.2f}", f"{row.get('pot_magnetica', 0):.2f}",
                        f"{row.get('pot_induzida', 0):.2f}", f"{row.get('pcap', 0):.2f}", f"{row.get('scap_sind_ratio', 0):.3f}"
                    ])
            else: # Trifásico
                data_list.append(['Frequência (Hz)', 'Pw (kW)', 'Sm (kVA)', 'Scap (kVAr cap)'])
                for row in freq_analysis['table_data']:
                    data_list.append([
                        row.get('frequencia', '-'), f"{row.get('pot_ativa', 0):.2f}", f"{row.get('pot_magnetica', 0):.2f}",
                        f"{row.get('pcap', 0):.2f}"
                    ])


    df_induced = pd.DataFrame(data_list)
    df_induced.to_excel(writer, sheet_name='Tensão Induzida', index=False, header=False)