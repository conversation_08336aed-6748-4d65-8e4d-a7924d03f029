<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhamento da Análise Dielétrica - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF; /* Mais claro para melhor contraste */
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E; /* Fundo mais escuro */
            --card-bg-color: #2D2D2D; /* Fundo do card mais escuro */
            --sidebar-bg-color: #252525; /* Fundo da barra lateral mais escuro */
            --border-color: #6c757d;
            --link-color: #4DA3FF; /* Cor de link mais clara para melhor contraste */
            --link-hover-color: #80BDFF; /* Cor de hover mais clara */
            --heading-color: #FFFFFF; /* Cor de cabeçalho branca para melhor contraste */
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--link-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Markdown content
        const markdownContent = `# Detalhamento da Análise Dielétrica

Este documento detalha as fórmulas e parâmetros usados na análise dielétrica para transformadores.

---

## 1. Parâmetros de Entrada

Estes são os valores fornecidos pelo usuário ou obtidos de dados básicos para a análise dielétrica:

| Parâmetro                     | Descrição                              | Unidade | Variável no Código                   |
| :---------------------------- | :------------------------------------- | :------ | :--------------------------------- |
| Tipo de Transformador         | Configuração (Monofásico/Trifásico)    | -       | \`tipo_transformador\`               |
| Tensão Nominal AT             | Tensão nominal do lado de Alta Tensão  | kV      | \`tensao_at\`                        |
| Tensão Nominal BT             | Tensão nominal do lado de Baixa Tensão | kV      | \`tensao_bt\`                        |
| Classe de Tensão AT           | Classe de tensão do lado de Alta Tensão| kV      | \`classe_tensao_at\`                 |
| Classe de Tensão BT           | Classe de tensão do lado de Baixa Tensão| kV     | \`classe_tensao_bt\`                 |
| Nível Básico de Isolamento    | Nível de isolamento (BIL)              | kV      | \`bil\`                              |
| Nível de Isolamento AC        | Nível de isolamento AC                 | kV      | \`ac_level\`                         |
| Espaçamentos                  | Distâncias entre partes energizadas    | mm      | \`espacamentos\`                     |
| Meio Isolante                 | Tipo de meio isolante (óleo, ar, etc.) | -       | \`meio_isolante\`                    |
| Altitude de Instalação        | Altitude do local de instalação        | m       | \`altitude\`                         |

## 2. Fundamentos Teóricos

### 2.1. Rigidez Dielétrica

A rigidez dielétrica é a máxima intensidade de campo elétrico que um material isolante pode suportar sem sofrer ruptura:

* **Óleo Mineral**: 10-15 kV/mm
* **Papel Impregnado**: 40-60 kV/mm
* **Ar (nível do mar)**: 3 kV/mm
* **SF6**: 7-8 kV/mm

### 2.2. Correção para Altitude

A rigidez dielétrica do ar diminui com o aumento da altitude. O fator de correção é:

* \`K_alt = e^(-h/8150)\`

Onde:
* \`K_alt\` é o fator de correção para altitude
* \`h\` é a altitude em metros
* \`8150\` é uma constante derivada da pressão atmosférica

### 2.3. Distância de Isolamento

A distância mínima de isolamento é calculada como:

* \`d_min = V_max / E_max\`

Onde:
* \`d_min\` é a distância mínima em mm
* \`V_max\` é a tensão máxima em kV
* \`E_max\` é a rigidez dielétrica em kV/mm

## 3. Análise de Espaçamentos

### 3.1. Espaçamentos Fase-Fase

* **Tensão Máxima**: \`V_max = V_linha * k_sobretensao\`
* **Distância Mínima**: \`d_min = V_max / (E_ar * K_alt)\`

### 3.2. Espaçamentos Fase-Terra

* **Tensão Máxima**: \`V_max = V_fase * k_sobretensao\`
* **Distância Mínima**: \`d_min = V_max / (E_ar * K_alt)\`

### 3.3. Espaçamentos em Óleo

* **Distância Mínima**: \`d_min = V_max / E_oleo\`

## 4. Análise de Níveis de Isolamento

### 4.1. Verificação do BIL

* **BIL Mínimo**: Determinado pela classe de tensão conforme normas
* **BIL Corrigido para Altitude**: \`BIL_corrigido = BIL / K_alt\`
* **Verificação**: \`BIL_especificado ≥ BIL_corrigido\`

### 4.2. Verificação do Nível AC

* **AC Mínimo**: Determinado pela classe de tensão conforme normas
* **AC Corrigido para Altitude**: \`AC_corrigido = AC / K_alt\`
* **Verificação**: \`AC_especificado ≥ AC_corrigido\`

## 5. Análise de Coordenação de Isolamento

### 5.1. Fator de Coordenação

* \`K_coord = BIL / (√2 * V_max)\`

Onde:
* \`K_coord\` é o fator de coordenação
* \`BIL\` é o nível básico de isolamento
* \`V_max\` é a tensão máxima do sistema

### 5.2. Verificação da Coordenação

* **Fator Mínimo**: Tipicamente 1.2 para sistemas sem para-raios
* **Verificação**: \`K_coord ≥ K_min\`

## 6. Análise de Distribuição de Tensão em Enrolamentos

### 6.1. Distribuição Inicial

* \`α = √(C_s / C_g)\`
* \`V(x) = V_0 * (cosh(αx) / cosh(α))\`

Onde:
* \`α\` é o parâmetro de distribuição
* \`C_s\` é a capacitância série por unidade de comprimento
* \`C_g\` é a capacitância para terra por unidade de comprimento
* \`V(x)\` é a tensão na posição x do enrolamento
* \`V_0\` é a tensão aplicada
* \`x\` é a posição normalizada (0 a 1)

### 6.2. Fator de Distribuição Não-Uniforme

* \`K_dist = V_max / V_avg\`

Onde:
* \`K_dist\` é o fator de distribuição
* \`V_max\` é a tensão máxima no enrolamento
* \`V_avg\` é a tensão média no enrolamento

## 7. Recomendações para Projeto

### 7.1. Margens de Segurança

* **Espaçamentos**: Adicionar 10-20% à distância mínima calculada
* **BIL**: Especificar BIL pelo menos 10% acima do mínimo requerido
* **Coordenação**: Manter fator de coordenação acima de 1.3

### 7.2. Considerações Especiais

* **Contaminação**: Em ambientes com poluição, aumentar as distâncias de isolamento
* **Umidade**: Em ambientes úmidos, considerar tratamentos especiais para isolantes
* **Temperatura**: Considerar o efeito da temperatura na rigidez dielétrica dos isolantes

---

## Notas Importantes

1. Os valores de BIL e AC são determinados pelas normas técnicas e variam conforme a classe de tensão.
2. A rigidez dielétrica dos materiais pode variar conforme a qualidade, temperatura e envelhecimento.
3. A análise dielétrica deve considerar não apenas condições normais, mas também sobretensões transitórias.
4. A coordenação de isolamento deve ser verificada considerando a proteção por para-raios, quando aplicável.

## 8. Inputs, Tipos e Callbacks

### 8.1. Inputs e Tipos

#### 8.1.1. Parâmetros de Isolamento

| ID do Componente | Tipo | Descrição |
|------------------|------|-----------|
| altitude-input | number | Altitude de instalação em metros |
| temperature-input | number | Temperatura ambiente em °C |
| humidity-input | number | Umidade relativa em % |
| pollution-level | dropdown | Nível de poluição (I/II/III/IV) |

#### 8.1.2. Parâmetros de Espaçamentos

| ID do Componente | Tipo | Descrição |
|------------------|------|-----------|
| phase-phase-spacing | number | Espaçamento fase-fase em mm |
| phase-ground-spacing | number | Espaçamento fase-terra em mm |
| terminal-clearance | number | Distância de escoamento em mm |
| creepage-distance | number | Distância de fuga em mm |

### 8.2. Callbacks Principais

#### 8.2.1. Callbacks de Inicialização e Armazenamento

| Callback | Função | Descrição |
|----------|--------|-----------|
| update_dielectric_page_info_panel | Atualiza painel de informações | Copia o conteúdo do painel global para o painel específico da página |
| load_dielectric_data | Carrega dados do store | Carrega os valores salvos no store para os componentes da interface |

#### 8.2.2. Callbacks de Cálculo

| Callback | Função | Descrição |
|----------|--------|-----------|
| calculate_dielectric_parameters | Calcula parâmetros dielétricos | Executa os cálculos de análise dielétrica e atualiza o store |
| update_correction_factors | Atualiza fatores de correção | Calcula fatores de correção para altitude, temperatura e poluição |
`;

        // Function to generate TOC and add IDs to headings in the actual document
        function generateTOC(html) {
            const container = document.createElement('div');
            container.innerHTML = html;

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const toc = document.getElementById('toc');
            toc.innerHTML = ''; // Clear existing TOC

            // First pass: assign IDs to headings in our temporary container
            headings.forEach((heading, index) => {
                const headingText = heading.textContent.trim();
                // Create a slug from the heading text
                const slug = headingText
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special chars
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen

                // Ensure unique ID by adding index if slug is empty or duplicated
                heading.id = slug ? `${slug}-${index}` : `heading-${index}`;
            });

            // Now find the actual headings in the document and assign the same IDs
            const contentDiv = document.getElementById('markdown-content');
            const actualHeadings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

            actualHeadings.forEach((heading, index) => {
                if (index < headings.length) {
                    heading.id = headings[index].id;
                }
            });

            // Now build the TOC with links to the actual headings
            headings.forEach((heading, index) => {
                const level = parseInt(heading.tagName.substring(1));
                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;

                const link = document.createElement('a');
                link.href = `#${heading.id}`;
                link.textContent = heading.textContent;
                link.setAttribute('data-heading-id', heading.id);

                // Add click handler to ensure smooth scrolling and proper focus
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-heading-id');
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Scroll to the element smoothly
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Set focus to the heading for accessibility
                        targetElement.setAttribute('tabindex', '-1');
                        targetElement.focus();

                        // Update URL hash without jumping
                        history.pushState(null, null, `#${targetId}`);

                        // Add active class to the current TOC item
                        document.querySelectorAll('.toc a').forEach(a => {
                            a.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }

        // Function to highlight search terms
        function highlightSearchTerms(content, term) {
            if (!term) return content;

            const regex = new RegExp(`(${term})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Function to render markdown
        function renderMarkdown() {
            const searchTerm = document.getElementById('search-input').value.trim();
            let content = markdownContent;

            // Configure marked to add IDs to headings
            marked.use({
                headerIds: true,
                headerPrefix: ''
            });

            // Render markdown to HTML
            const renderedHTML = marked.parse(content);

            // Apply search highlighting if there's a search term
            const finalHTML = searchTerm ? highlightSearchTerms(renderedHTML, searchTerm) : renderedHTML;

            // Update content
            document.getElementById('markdown-content').innerHTML = finalHTML;

            // Generate TOC and ensure IDs are properly set
            generateTOC(finalHTML);

            // Initialize syntax highlighting
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });

            // Render math expressions
            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            // Check if there's a hash in the URL and scroll to it after rendering
            setTimeout(() => {
                if (window.location.hash) {
                    const hash = window.location.hash.substring(1);
                    const targetElement = document.getElementById(hash);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Highlight the corresponding TOC item
                        const tocLink = document.querySelector(`.toc a[data-heading-id="${hash}"]`);
                        if (tocLink) {
                            document.querySelectorAll('.toc a').forEach(a => {
                                a.classList.remove('active');
                            });
                            tocLink.classList.add('active');
                        }
                    }
                }
            }, 500); // Small delay to ensure rendering is complete
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            renderMarkdown();

            // Set up search
            document.getElementById('search-input').addEventListener('input', (e) => {
                renderMarkdown();

                // Scroll to first highlight if there is one
                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    </script>
</body>
</html>

