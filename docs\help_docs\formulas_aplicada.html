<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhamento dos Cálculos de Tensão Aplicada - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF; /* Mais claro para melhor contraste */
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E; /* Fundo mais escuro */
            --card-bg-color: #2D2D2D; /* Fundo do card mais escuro */
            --sidebar-bg-color: #252525; /* Fundo da barra lateral mais escuro */
            --border-color: #6c757d;
            --link-color: #4DA3FF; /* Cor de link mais clara para melhor contraste */
            --link-hover-color: #80BDFF; /* Cor de hover mais clara */
            --heading-color: #FFFFFF; /* Cor de cabeçalho branca para melhor contraste */
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--link-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>        // Markdown content
        const markdownContent = `# Detalhamento dos Cálculos de Tensão Aplicada

O ensaio de tensão aplicada é realizado para verificar a integridade do isolamento do transformador quando submetido a tensões de frequência industrial.

## 1. Parâmetros de Entrada

### 1.1. Dados Básicos do Transformador

Os seguintes dados são necessários para o cálculo do ensaio de tensão aplicada:

| Parâmetro                     | Descrição                               | Unidade | Nome no Sistema                      |
|-------------------------------|-----------------------------------------|---------|--------------------------------------|
| Tipo do Transformador         | Monofásico ou Trifásico               | -       | \`tipo_transformador\`               |
| Tensão Nominal AT             | Tensão nominal do lado de Alta Tensão  | kV      | \`tensao_at\`                       |
| Tensão Nominal BT             | Tensão nominal do lado de Baixa Tensão | kV      | \`tensao_bt\`                       |
| Tensão Nominal Terciário      | Tensão nominal do terciário (se houver)| kV      | \`tensao_terciario\`                |
| Frequência Nominal            | Frequência nominal de operação         | Hz      | \`frequencia\`                      |
| Capacitância AT               | Capacitância do enrolamento AT         | pF      | \`cap_at\`                          |
| Capacitância BT               | Capacitância do enrolamento BT         | pF      | \`cap_bt\`                          |
| Capacitância Terciário        | Capacitância do enrolamento terciário  | pF      | \`cap_ter\`                         |

### 1.2. Tensões de Ensaio

As tensões de ensaio são determinadas previamente nos dados básicos:

| Enrolamento | Parâmetro                    | Unidade | Nome no Sistema                      |
|-------------|------------------------------|---------|--------------------------------------|
| AT          | Tensão de ensaio AT          | kV      | \`teste_tensao_aplicada_at\`        |
| BT          | Tensão de ensaio BT          | kV      | \`teste_tensao_aplicada_bt\`        |
| Terciário   | Tensão de ensaio Terciário   | kV      | \`teste_tensao_aplicada_terciario\` |

## 2. Cálculos de Tensão Aplicada

### 2.1. Ajuste da Capacitância

Para o cálculo da corrente de teste, é necessário ajustar a capacitância de cada enrolamento para incluir a capacitância do divisor de tensão:

#### 2.1.1. Fator de Ajuste da Capacitância

O fator de ajuste depende da tensão de ensaio:

\`\`\`python
if tensao_ensaio > 450:  # kV
    capacitancia_adicional = 330  # pF
else:
    capacitancia_adicional = 660  # pF

capacitancia_ajustada = capacitancia_informada + capacitancia_adicional
\`\`\`

### 2.2. Cálculo da Impedância Capacitiva

A impedância capacitiva é calculada para cada enrolamento:

\`\`\`
Zc = 1 / (2 × π × f × C)
\`\`\`

Onde:
- \`Zc\` = Impedância capacitiva (Ω)
- \`f\` = Frequência (Hz)
- \`C\` = Capacitância ajustada (F)

### 2.3. Cálculo da Corrente de Teste

A corrente de teste é calculada para cada enrolamento:

\`\`\`
I = V / Zc
\`\`\`

Onde:
- \`I\` = Corrente de teste (A)
- \`V\` = Tensão de ensaio (V)
- \`Zc\` = Impedância capacitiva (Ω)

### 2.4. Cálculo da Potência Reativa

A potência reativa necessária para o teste:

\`\`\`
Q = V × I
\`\`\`

Onde:
- \`Q\` = Potência reativa (VAr)
- \`V\` = Tensão de ensaio (V)
- \`I\` = Corrente de teste (A)

## 3. Análise de Viabilidade do Sistema Ressonante

### 3.1. Configurações Disponíveis

O sistema analisa a viabilidade de utilização do sistema ressonante High Volt WRM 1800/1350-900-450 com base nas configurações disponíveis:

#### 3.1.1. Módulos 1||2||3 (3 Par.) 450kV
- **Tensão máxima:** 450 kV
- **Capacitância mínima:** 2.0 nF
- **Capacitância máxima:** 23.4 nF
- **Status:** Configuração prioritária

#### 3.1.2. Módulos 1||2||3 (3 Par.) 270kV
- **Tensão máxima:** 270 kV
- **Capacitância mínima:** 2.0 nF
- **Capacitância máxima:** 39.0 nF
- **Status:** Configuração prioritária para tensões menores

#### 3.1.3. Outras Configurações
Para situações onde as configurações prioritárias não são adequadas, o sistema analisa outras configurações disponíveis com limites diferentes de tensão e capacitância.

### 3.2. Critérios de Seleção

O sistema prioriza as configurações "Módulos 1||2||3 (3 Par.)" sempre que possível, seguindo esta lógica:

1. **Verificar se 450kV atende:** Se tensão ≤ 450kV e capacitância dentro dos limites
2. **Verificar se 270kV atende:** Se tensão ≤ 270kV e capacitância dentro dos limites
3. **Buscar configurações alternativas:** Se as prioritárias não atendem

### 3.3. Status de Viabilidade

O sistema retorna um dos seguintes status:

- **✅ Viável - Configuração Ideal:** Módulos 1||2||3 (3 Par.) pode ser usado
- **⚠️ Viável - Configuração Alternativa:** Outras configurações podem ser usadas
- **❌ Não Viável:** Nenhuma configuração atende aos requisitos

### 3.4. Limitações

As principais limitações que podem tornar o teste inviável:

1. **Tensão excessiva:** Acima da tensão máxima do sistema (450kV)
2. **Capacitância baixa:** Abaixo da capacitância mínima (2.0 nF)
3. **Capacitância alta:** Acima da capacitância máxima da configuração

## 4. Exemplo de Cálculo

### 4.1. Dados de Entrada

- Tensão de ensaio AT: 300 kV
- Capacitância AT informada: 5000 pF
- Frequência: 60 Hz

### 4.2. Cálculos

1. **Ajuste da capacitância:**
   \`\`\`
   Capacitância ajustada = 5000 + 660 = 5660 pF = 5.66 nF
   \`\`\`

2. **Impedância capacitiva:**
   \`\`\`
   Zc = 1 / (2 × π × 60 × 5.66 × 10⁻⁹) = 468.7 kΩ
   \`\`\`

3. **Corrente de teste:**
   \`\`\`
   I = 300000 / 468700 = 0.640 A
   \`\`\`

4. **Potência reativa:**
   \`\`\`
   Q = 300000 × 0.640 = 192 kVAr
   \`\`\`

### 4.3. Análise de Viabilidade

- Tensão: 300 kV ≤ 450 kV ✓
- Capacitância: 5.66 nF está entre 2.0 e 23.4 nF ✓
- **Resultado:** Viável com Módulos 1||2||3 (3 Par.) 450kV

## 5. Considerações Práticas

### 5.1. Preparação do Teste

1. **Verificação da capacitância:** Medir a capacitância real do transformador
2. **Configuração do sistema:** Ajustar o sistema ressonante conforme recomendação
3. **Calibração:** Verificar a calibração dos instrumentos de medição

### 5.2. Execução do Teste

1. **Aplicação gradual:** Elevar a tensão gradualmente até o valor de teste
2. **Tempo de aplicação:** Manter por 60 segundos (conforme norma)
3. **Monitoramento:** Observar corrente e potência durante o teste
4. **Redução gradual:** Reduzir a tensão gradualmente até zero

### 5.3. Critérios de Aprovação

- Ausência de descargas disruptivas
- Corrente estável durante o teste
- Nenhum sinal de deterioração do isolamento

### 5.4. Medidas de Segurança

- Isolamento da área de teste
- Aterramento adequado das partes não testadas
- Equipamentos de proteção individual
- Procedimentos de emergência estabelecidos

---

## Notas Importantes

1. **Precisão da capacitância:** A capacitância informada deve ser medida com precisão, pois afeta diretamente os cálculos de corrente e potência.

2. **Divisor de tensão:** Os valores de capacitância adicional (330 pF ou 660 pF) são baseados nas características do divisor de tensão utilizado.

3. **Configurações prioritárias:** O sistema sempre tenta usar as configurações "Módulos 1||2||3 (3 Par.)" por serem mais eficientes e estáveis.

4. **Análise por enrolamento:** Cada enrolamento (AT, BT, Terciário) é analisado independentemente para determinar a viabilidade.

5. **Limitações do equipamento:** As limitações são baseadas nas especificações do equipamento High Volt WRM 1800/1350-900-450.

| Parâmetro                     | Descrição                              | Unidade | Variável no Código                   |
| :---------------------------- | :------------------------------------- | :------ | :--------------------------------- |
| Tipo de Transformador         | Configuração (Monofásico/Trifásico)    | -       | \`tipo_transformador\`               |
| Tensão Nominal AT             | Tensão nominal do lado de Alta Tensão  | kV      | \`tensao_at\`                        |
| Tensão Nominal BT             | Tensão nominal do lado de Baixa Tensão | kV      | \`tensao_bt\`                        |
| Classe de Tensão AT           | Classe de tensão do lado de Alta Tensão| kV      | \`classe_tensao_at\`                 |
| Classe de Tensão BT           | Classe de tensão do lado de Baixa Tensão| kV     | \`classe_tensao_bt\`                 |
| Classe de Tensão Bucha Neutro | Classe de tensão da bucha de neutro    | kV      | \`classe_tensao_bucha_neutro\`       |
| Conexão AT                    | Tipo de conexão do lado de Alta Tensão | -       | \`conexao_at\`                       |
| Conexão BT                    | Tipo de conexão do lado de Baixa Tensão| -       | \`conexao_bt\`                       |
| Conexão Terciário             | Tipo de conexão do terciário (se houver)| -      | \`conexao_terciario\`                |

## 2. Cálculos de Tensão Aplicada

### 2.1. Determinação da Tensão de Teste

A tensão de teste para o ensaio de tensão aplicada é determinada com base na classe de tensão e no tipo de conexão definida em dados básicos:

#### 2.1.1. Para o Lado de Alta Tensão (AT)

* **Se a conexão for Yn (estrela com neutro acessível):**
  * \`tensao_teste_at = classe_tensao_bucha_neutro\`
* **Para outras conexões (Y, D):**
  * \`tensao_teste_at = classe_tensao_at\`

#### 2.1.2. Para o Lado de Baixa Tensão (BT)

* \`tensao_teste_bt = classe_tensao_bt\`

#### 2.1.3. Para o Terciário (se existir)

* \`tensao_teste_terciario = classe_tensao_terciario\`

### 2.2. Ajustes para Capacitância

Para tensões acima de 450 kV, é adicionado um valor de capacitância:
* Se tensão > 450 kV: Adicionar 330 pF
* Se tensão ≤ 450 kV: Adicionar 660 pF

Estes valores representam a capacitância do divisor de tensão utilizado durante o ensaio e são automaticamente adicionados à capacitância do transformador para o cálculo da corrente de teste.

## 3. Cálculo da Corrente de Teste

A corrente de teste é calculada com base na tensão de teste e na capacitância do transformador:

### 3.1. Fórmula da Corrente de Teste

* \`I = 2 * π * f * C * V * 10^-6\`

Onde:
* \`I\` é a corrente de teste em Amperes (A)
* \`f\` é a frequência em Hertz (Hz), geralmente 60 Hz
* \`C\` é a capacitância em picofarads (pF)
* \`V\` é a tensão de teste em Volts (V)
* \`10^-6\` é o fator de conversão de picofarads para farads

### 3.2. Potência Reativa

A potência reativa necessária para o teste é calculada como:

* \`Q = V * I\`

Onde:
* \`Q\` é a potência reativa em volt-amperes reativos (VAr)
* \`V\` é a tensão de teste em Volts (V)
* \`I\` é a corrente de teste em Amperes (A)

## 4. Análise de Viabilidade do Sistema Ressonante

### 4.1. Critérios de Viabilidade

O sistema analisa a viabilidade de utilizar um sistema ressonante para o teste, com base na capacitância e tensão de teste:

#### 4.1.1. Módulos 1||2||3 (3 Par.) 270kV
* **Tensão máxima:** 270 kV
* **Capacitância mínima:** 2.0 nF
* **Capacitância máxima:** 39.0 nF

#### 4.1.2. Módulos 1||2||3 (3 Par.) 450kV
* **Tensão máxima:** 450 kV
* **Capacitância mínima:** 2.0 nF
* **Capacitância máxima:** 23.4 nF

#### 4.1.3. Outras Configurações
Para capacitâncias ou tensões fora destes limites, o sistema recomenda outras configurações ou indica que o teste não é viável com o sistema ressonante disponível.

### 4.2. Equipamento de Teste

* **Fonte de Tensão:** Capaz de fornecer a tensão de teste calculada
* **Capacidade de Corrente:** Suficiente para fornecer a corrente de teste calculada
* **Frequência:** Geralmente 60 Hz (ou conforme especificado)

### 4.3. Procedimento de Teste

1. Aplicar a tensão de teste gradualmente até atingir o valor calculado
2. Manter a tensão pelo tempo especificado na norma (geralmente 60 segundos)
3. Reduzir a tensão gradualmente até zero
4. Verificar se não houve descargas ou falhas durante o teste

### 4.4. Medidas de Segurança

* Garantir que todas as partes não testadas estejam devidamente aterradas
* Verificar a ausência de pessoas na área de teste
* Utilizar equipamentos de proteção individual adequados
* Seguir os procedimentos de segurança específicos para testes de alta tensão

---

## Notas Importantes

1. Os valores de tensão de teste são determinados pelas normas técnicas e podem variar conforme a edição da norma utilizada.
2. Para transformadores com múltiplos enrolamentos, cada enrolamento deve ser testado separadamente.
3. A capacitância do transformador pode variar conforme a temperatura e outras condições, o que pode afetar a corrente de teste.
4. O teste de tensão aplicada é um teste destrutivo e deve ser realizado com cuidado para evitar danos ao transformador.
`;

        // Function to generate TOC and add IDs to headings in the actual document
        function generateTOC(html) {
            const container = document.createElement('div');
            container.innerHTML = html;

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const toc = document.getElementById('toc');
            toc.innerHTML = ''; // Clear existing TOC

            // First pass: assign IDs to headings in our temporary container
            headings.forEach((heading, index) => {
                const headingText = heading.textContent.trim();
                // Create a slug from the heading text
                const slug = headingText
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special chars
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen

                // Ensure unique ID by adding index if slug is empty or duplicated
                heading.id = slug ? `${slug}-${index}` : `heading-${index}`;
            });

            // Now find the actual headings in the document and assign the same IDs
            const contentDiv = document.getElementById('markdown-content');
            const actualHeadings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

            actualHeadings.forEach((heading, index) => {
                if (index < headings.length) {
                    heading.id = headings[index].id;
                }
            });

            // Now build the TOC with links to the actual headings
            headings.forEach((heading, index) => {
                const level = parseInt(heading.tagName.substring(1));
                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;

                const link = document.createElement('a');
                link.href = `#${heading.id}`;
                link.textContent = heading.textContent;
                link.setAttribute('data-heading-id', heading.id);

                // Add click handler to ensure smooth scrolling and proper focus
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-heading-id');
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Scroll to the element smoothly
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Set focus to the heading for accessibility
                        targetElement.setAttribute('tabindex', '-1');
                        targetElement.focus();

                        // Update URL hash without jumping
                        history.pushState(null, null, `#${targetId}`);

                        // Add active class to the current TOC item
                        document.querySelectorAll('.toc a').forEach(a => {
                            a.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }

        // Function to highlight search terms
        function highlightSearchTerms(content, term) {
            if (!term) return content;

            const regex = new RegExp(`(${term})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Function to render markdown
        function renderMarkdown() {
            const searchTerm = document.getElementById('search-input').value.trim();
            let content = markdownContent;

            // Configure marked to add IDs to headings
            marked.use({
                headerIds: true,
                headerPrefix: ''
            });

            // Render markdown to HTML
            const renderedHTML = marked.parse(content);

            // Apply search highlighting if there's a search term
            const finalHTML = searchTerm ? highlightSearchTerms(renderedHTML, searchTerm) : renderedHTML;

            // Update content
            document.getElementById('markdown-content').innerHTML = finalHTML;

            // Generate TOC and ensure IDs are properly set
            generateTOC(finalHTML);

            // Initialize syntax highlighting
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });

            // Render math expressions
            if (window.MathJax) {
                MathJax.typesetPromise();
            }


        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            renderMarkdown();

            // Set up search
            document.getElementById('search-input').addEventListener('input', (e) => {
                renderMarkdown();

                // Scroll to first highlight if there is one
                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });

            // Check if there's a hash in the URL and scroll to it after rendering
            setTimeout(() => {
                if (window.location.hash) {
                    const hash = window.location.hash.substring(1);
                    const targetElement = document.getElementById(hash);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        // Highlight the corresponding TOC item
                        const tocLink = document.querySelector(`.toc a[data-heading-id="${hash}"]`);
                        if (tocLink) {
                            document.querySelectorAll('.toc a').forEach(a => {
                                a.classList.remove('active');
                            });
                            tocLink.classList.add('active');
                        }
                    }
                }
            }, 500); // Small delay to ensure rendering is complete
        });
    </script>
</body>
</html>

