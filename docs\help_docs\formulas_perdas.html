<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhamento dos Cálculos de Perdas - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #E1F0FF;
            --text-color: #f8f9fa;
            --bg-color: #1E1E1E;
            --card-bg-color: #2D2D2D;
            --sidebar-bg-color: #252525;
            --border-color: #6c757d;
            --link-color: #4DA3FF;
            --link-hover-color: #80BDFF;
            --heading-color: #FFFFFF;
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--sidebar-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--link-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc a.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content">
                        <h1 id="detalhamento-dos-calculos-de-perdas-em-transformadores">Detalhamento dos Cálculos de Perdas em Transformadores</h1>

<h2 id="sumario-do-documento">Sumário do Documento</h2>

<ul>
<li><strong>1. Introdução</strong>: Conceitos básicos de perdas em transformadores</li>
<li><strong>2. Perdas em Vazio (No-Load Losses)</strong>: Parâmetros, cálculos e análise SUT/EPS</li>
<li><strong>3. Perdas em Carga (Load Losses)</strong>: Cenários, bancos de capacitores e compensação</li>
<li><strong>4. Configuração dos Bancos de Capacitores</strong>: Disponibilidade e lógica de seleção</li>
<li><strong>5. Considerações Finais</strong>: Implementação e observações técnicas</li>
</ul>

<hr />

<h2 id="1-introducao">1. Introdução</h2>

<p>As perdas em transformadores são divididas em duas categorias principais:</p>

<ul>
<li><strong>Perdas em Vazio (No-Load Losses) - P<sub>Fe</sub></strong>: Perdas no núcleo magnético devido à histerese e correntes parasitas. Ocorrem quando o transformador está energizado, mesmo sem carga. São dependentes da tensão e frequência aplicadas.</li>
<li><strong>Perdas em Carga (Load Losses) - P<sub>Cu</sub></strong>: Perdas nos enrolamentos devido à resistência ôhmica. Ocorrem quando há corrente circulando pelos enrolamentos. São proporcionais ao quadrado da corrente de carga.</li>
</ul>

<p>Este documento detalha as fórmulas e metodologias implementadas no sistema para calcular e analisar essas perdas, baseando-se no serviço <code>losses_service.py</code>.</p>

<hr />

<h2 id="2-perdas-em-vazio">2. Perdas em Vazio (No-Load Losses)</h2>

<p>As perdas em vazio são medidas aplicando tensão nominal (ou suas variações como 1,1 pu e 1,2 pu) no lado de baixa tensão do transformador, mantendo o lado de alta tensão em circuito aberto.</p>

<h3 id="21-parametros-de-entrada">2.1. Parâmetros de Entrada</h3>

<table>
<thead>
<tr>
<th><strong>Parâmetro</strong></th>
<th><strong>Descrição</strong></th>
<th><strong>Unidade</strong></th>
<th><strong>Origem</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>perdas_vazio_ui</td>
<td>Perdas em vazio de projeto</td>
<td>kW</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>peso_nucleo_ui</td>
<td>Peso do núcleo</td>
<td>ton</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>corrente_excitacao_ui</td>
<td>Corrente de excitação nominal</td>
<td>%</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>inducao_ui</td>
<td>Indução magnética nominal</td>
<td>T</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>corrente_exc_1_1_ui</td>
<td>Corrente de excitação a 110% Un</td>
<td>%</td>
<td>Interface do usuário (opcional)</td>
</tr>
<tr>
<td>corrente_exc_1_2_ui</td>
<td>Corrente de excitação a 120% Un</td>
<td>%</td>
<td>Interface do usuário (opcional)</td>
</tr>
<tr>
<td>frequencia</td>
<td>Frequência nominal</td>
<td>Hz</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>tensao_bt_kv</td>
<td>Tensão nominal BT</td>
<td>kV</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>corrente_nominal_bt</td>
<td>Corrente nominal BT</td>
<td>A</td>
<td>Dados do transformador</td>
</tr>
</tbody>
</table>

<h3 id="22-calculos-base-aco-m4">2.2. Cálculos Base (Aço M4)</h3>

<p>O sistema utiliza tabelas de referência do aço M4 para interpolar valores de perdas específicas em função da indução magnética e frequência.</p>

<h4 id="221-perdas-especificas-interpoladas">2.2.1. Perdas Específicas Interpoladas</h4>

<p><strong>Fórmula base:</strong></p>
<p><span class="math display">$$W_{esp,M4} = \text{interpolação}(\text{indução\_ui}, \text{frequencia})$$</span></p>

<p>Onde a interpolação é feita na tabela de perdas específicas do aço M4 (W/kg).</p>

<h4 id="222-perdas-totais-base-m4">2.2.2. Perdas em Carga Totais Base M4</h4>

<p><span class="math display">$$P_{Fe,M4} = W_{esp,M4} \times \text{peso\_nucleo\_ui} \times 1000$$</span></p>

<p>Onde:</p>
<ul>
<li><code>peso_nucleo_ui</code> está em toneladas</li>
<li>O fator 1000 converte de W para kW</li>
</ul>

<h3 id="23-calculos-base-projeto">2.3. Cálculos Base Projeto</h3>

<h4 id="231-fator-de-correcao">2.3.1. Fator de Correção</h4>

<p><span class="math display">$$\text{fator\_correcao} = \frac{\text{perdas\_vazio\_ui}}{P_{Fe,M4}}$$</span></p>

<h4 id="232-perdas-especificas-corrigidas">2.3.2. Perdas Específicas Corrigidas</h4>

<p><span class="math display">$$W_{esp,projeto} = W_{esp,M4} \times \text{fator\_correcao}$$</span></p>

<h3 id="24-correntes-de-excitacao">2.4. Correntes de Excitação</h3>

<h4 id="241-corrente-base-100">2.4.1. Corrente Base (100%)</h4>

<p><span class="math display">$$I_{exc,100\%} = \frac{\text{corrente\_excitacao\_ui} \times \text{corrente\_nominal\_bt}}{100}$$</span></p>

<h4 id="242-correntes-110-e-120">2.4.2. Correntes 110% e 120%</h4>

<p>Se fornecidas pelo usuário:</p>
<p><span class="math display">$$I_{exc,110\%} = \frac{\text{corrente\_exc\_1\_1\_ui} \times \text{corrente\_nominal\_bt}}{100}$$</span></p>

<p><span class="math display">$$I_{exc,120\%} = \frac{\text{corrente\_exc\_1\_2\_ui} \times \text{corrente\_nominal\_bt}}{100}$$</span></p>

<p>Se não fornecidas, são calculadas por interpolação baseada na variação das perdas com a tensão.</p>

<hr />

<h2 id="3-perdas-em-carga">3. Perdas em Carga (Load Losses)</h2>

<p>As perdas em carga são medidas com o transformador sob corrente nominal, mantendo o lado de alta tensão em curto-circuito e aplicando tensão variável no lado de baixa tensão.</p>

<h3 id="31-parametros-de-entrada">3.1. Parâmetros de Entrada</h3>

<table>
<thead>
<tr>
<th><strong>Parâmetro</strong></th>
<th><strong>Descrição</strong></th>
<th><strong>Unidade</strong></th>
<th><strong>Origem</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>perdas_carga_ui</td>
<td>Perdas em carga de projeto</td>
<td>kW</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>corrente_curto_ui</td>
<td>Corrente de curto-circuito</td>
<td>%</td>
<td>Interface do usuário</td>
</tr>
<tr>
<td>taps_at</td>
<td>Lista de taps do lado AT</td>
<td>kV</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>tensao_at_kv</td>
<td>Tensão nominal AT</td>
<td>kV</td>
<td>Dados do transformador</td>
</tr>
<tr>
<td>corrente_nominal_at</td>
<td>Corrente nominal AT</td>
<td>A</td>
<td>Dados do transformador</td>
</tr>
</tbody>
</table>

<h3 id="32-cenarios-de-calculo">3.2. Cenários de Cálculo</h3>

<p>O sistema calcula perdas em carga para três cenários principais:</p>

<ol>
<li><strong>Teste a Frio</strong>: Sem aquecimento prévio</li>
<li><strong>Teste a Quente</strong>: Após aquecimento do transformador</li>
<li><strong>Teste com Banco de Capacitores</strong>: Utilizando compensação reativa</li>
</ol>

<h3 id="33-calculos-por-tap">3.3. Cálculos por Tap</h3>

<h4 id="331-tensao-de-curto-circuito">3.3.1. Tensão de Curto-Circuito</h4>

<p>Para cada tap:</p>
<p><span class="math display">$$U_{cc,tap} = \frac{\text{corrente\_curto\_ui}}{100} \times \text{tap\_valor}$$</span></p>

<h4 id="332-corrente-refletida-ao-bt">3.3.2. Corrente Refletida ao BT</h4>

<p><span class="math display">$$I_{ref,BT} = \text{corrente\_nominal\_at} \times \frac{\text{tap\_valor}}{\text{tensao\_bt\_kv}}$$</span></p>

<h4 id="333-perdas-por-tap">3.3.3. Perdas por Tap</h4>

<p><span class="math display">$$P_{Cu,tap} = \text{perdas\_carga\_ui} \times \left(\frac{\text{tap\_valor}}{\text{tensao\_at\_kv}}\right)^2$$</span></p>

<h3 id="34-banco-de-capacitores">3.4. Banco de Capacitores</h3>

<h4 id="341-calculo-da-potencia-requerida">3.4.1. Cálculo da Potência Requerida</h4>

<p>Para compensar a componente reativa:</p>
<p><span class="math display">$$Q_{banco} = \sqrt{S_{DUT}^2 - P_{Cu}^2}$$</span></p>

<p>Onde <span class="math inline">$S_{DUT}$</span> é a potência aparente do ensaio.</p>

<h4 id="342-configuracao-disponivel">3.4.2. Configuração Disponível</h4>

<p>O sistema possui bancos de capacitores configuráveis por nível de tensão:</p>

<table>
<thead>
<tr>
<th><strong>Tensão (kV)</strong></th>
<th><strong>Capacitores Disponíveis (kVAr)</strong></th>
<th><strong>Potência das Chaves (kVAr)</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>13.8</td>
<td>[25, 50, 100, 200, 400]</td>
<td>[30, 60, 120, 240, 480]</td>
</tr>
<tr>
<td>34.5</td>
<td>[25, 50, 100, 200, 400, 800]</td>
<td>[30, 60, 120, 240, 480, 960]</td>
</tr>
<tr>
<td>69.0</td>
<td>[25, 50, 100, 200, 400, 800]</td>
<td>[30, 60, 120, 240, 480, 960]</td>
</tr>
<tr>
<td>138.0</td>
<td>[50, 100, 200, 400, 800]</td>
<td>[60, 120, 240, 480, 960]</td>
</tr>
</tbody>
</table>

<h4 id="343-logica-de-selecao">3.4.3. Lógica de Seleção</h4>

<p>O algoritmo seleciona a melhor combinação de capacitores que:</p>
<ol>
<li>Atenda à potência requerida sem exceder limites</li>
<li>Minimize o número de unidades utilizadas</li>
<li>Respeite as restrições de tensão do SUT</li>
</ol>

<hr />

<h2 id="4-analise-sut-eps">4. Análise SUT/EPS</h2>

<h3 id="41-limites-do-sistema">4.1. Limites do Sistema</h3>

<table>
<thead>
<tr>
<th><strong>Componente</strong></th>
<th><strong>Parâmetro</strong></th>
<th><strong>Valor</strong></th>
<th><strong>Unidade</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>EPS</td>
<td>Corrente máxima</td>
<td>2000</td>
<td>A</td>
</tr>
<tr>
<td>EPS</td>
<td>Tensão máxima</td>
<td>480</td>
<td>V</td>
</tr>
<tr>
<td>SUT</td>
<td>Tensão AT mínima</td>
<td>14000</td>
<td>V</td>
</tr>
<tr>
<td>SUT</td>
<td>Tensão AT máxima</td>
<td>140000</td>
<td>V</td>
</tr>
<tr>
<td>SUT</td>
<td>Passo de tensão</td>
<td>3000</td>
<td>V</td>
</tr>
</tbody>
</table>

<h3 id="42-verificacao-de-viabilidade">4.2. Verificação de Viabilidade</h3>

<p>Para cada cenário de teste, o sistema verifica:</p>

<ol>
<li><strong>Corrente EPS ≤ 2000 A</strong></li>
<li><strong>Tensão SUT AT ≥ 14 kV e ≤ 140 kV</strong></li>
<li><strong>Tensão disponível em múltiplos de 3 kV</strong></li>
<li><strong>Potência do DUT ≤ 1350 kW</strong></li>
</ol>

<h3 id="43-compensacao-de-corrente">4.3. Compensação de Corrente</h3>

<p>Quando utilizado banco de capacitores:</p>
<p><span class="math display">$$I_{compensada} = I_{DUT} - I_{capacitor}$$</span></p>

<p>Onde:</p>
<p><span class="math display">$$I_{capacitor} = \frac{Q_{banco}}{U_{teste} \times \sqrt{3}}$$</span></p>

<hr />

<h2 id="5-consideracoes-finais">5. Considerações Finais</h2>

<h3 id="51-precisao-dos-calculos">5.1. Precisão dos Cálculos</h3>

<ul>
<li><strong>Interpolação</strong>: As tabelas de aço M4 utilizam interpolação bilinear para valores intermediários</li>
<li><strong>Aproximações</strong>: A compensação de corrente assume comportamento puramente reativo dos capacitores</li>
<li><strong>Temperatura</strong>: Correções de temperatura são aplicadas quando especificado</li>
</ul>

<h3 id="52-limitacoes">5.2. Limitações</h3>

<ul>
<li>Análise fasorial simplificada para compensação reativa</li>
<li>Banco de capacitores com configuração fixa por nível de tensão</li>
<li>Interpolação limitada aos ranges das tabelas de referência</li>
</ul>

<h3 id="53-implementacao">5.3. Implementação</h3>

<p>Todos os cálculos são implementados no módulo <code>losses_service.py</code>, com funções específicas para:</p>
<ul>
<li><code>calculate_no_load_losses()</code> - Perdas em vazio</li>
<li><code>calculate_load_losses()</code> - Perdas em carga</li>
<li><code>calculate_cap_bank()</code> - Dimensionamento do banco</li>
<li><code>calculate_sut_eps_analysis()</code> - Análise de viabilidade</li>
</ul>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to generate TOC and add IDs to headings in the actual document
        function generateTOC(htmlContent) {
            const container = document.createElement('div');
            container.innerHTML = htmlContent;

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const tocList = document.getElementById('toc');
            tocList.innerHTML = '';

            headings.forEach((heading, index) => {
                const headingId = heading.getAttribute('id') || `heading-${index}`;
                heading.setAttribute('id', headingId);

                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = `#${headingId}`;
                a.textContent = heading.textContent;
                a.classList.add(`toc-${heading.tagName.toLowerCase()}`);
                a.setAttribute('data-heading-id', headingId);

                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetElement = document.getElementById(headingId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        document.querySelectorAll('.toc a').forEach(link => {
                            link.classList.remove('active');
                        });
                        a.classList.add('active');

                        window.history.pushState(null, null, `#${headingId}`);
                    }
                });

                li.appendChild(a);
                tocList.appendChild(li);
            });

            // Update the actual document with the new IDs
            const actualContent = document.getElementById('markdown-content');
            actualContent.innerHTML = container.innerHTML;
        }

        function applySearchAndHighlight() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const contentDiv = document.getElementById('markdown-content');
            
            // Remove existing highlights
            const existingHighlights = contentDiv.querySelectorAll('.highlight');
            existingHighlights.forEach(highlight => {
                const parent = highlight.parentNode;
                parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                parent.normalize();
            });

            if (searchTerm.length < 2) {
                return;
            }

            // Get all text nodes
            const walker = document.createTreeWalker(
                contentDiv,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            // Search and highlight
            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                
                if (regex.test(text)) {
                    const highlightedText = text.replace(regex, '<span class="highlight">$1</span>');
                    const wrapper = document.createElement('div');
                    wrapper.innerHTML = highlightedText;
                    
                    const fragment = document.createDocumentFragment();
                    while (wrapper.firstChild) {
                        fragment.appendChild(wrapper.firstChild);
                    }
                    
                    textNode.parentNode.replaceChild(fragment, textNode);
                }
            });

            // After potentially modifying the content, re-render MathJax
            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            hljs.highlightAll();
        }

        // Initialize on DOMContentLoaded
        document.addEventListener('DOMContentLoaded', () => {
            const contentDiv = document.getElementById('markdown-content');

            hljs.highlightAll();
            generateTOC(contentDiv.innerHTML);

            if (window.MathJax) {
                MathJax.typesetPromise();
            }

            setTimeout(() => {
                if (window.location.hash) {
                    const hash = window.location.hash.substring(1);
                    const targetElement = document.getElementById(hash);

                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

                        const tocLink = document.querySelector(`.toc a[data-heading-id="${hash}"]`);
                        if (tocLink) {
                            document.querySelectorAll('.toc a').forEach(a => {
                                a.classList.remove('active');
                            });
                            tocLink.classList.add('active');
                        }
                    }
                }
            }, 500);

            document.getElementById('search-input').addEventListener('input', (e) => {
                applySearchAndHighlight();

                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    </script>
</body>
</html>
