// public/scripts/history.js - ATUALIZADO

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { apiDataSystem, collectFormData, fillFormWithData, waitForApiSystem } from './api_persistence.js'; // Importa o sistema de persistência e funções auxiliares

// Obtém o store de sessões
const historyStore = apiDataSystem.getStore('sessions'); // Usar 'sessions' conforme o backend

// Variável global para rastrear a sessão atual
let currentSessionId = null;

// Função helper para definir mensagens de forma segura
function setActionMessage(html, timeout = 3000) {
    const messageEl = document.getElementById('history-action-message');
    if (messageEl) {
        messageEl.innerHTML = html;
        if (timeout > 0) {
            setTimeout(() => {
                const messageEl = document.getElementById('history-action-message');
                if (messageEl) messageEl.innerHTML = '';
            }, timeout);
        }
    }
}

// Função para carregar informações da sessão atual
async function loadCurrentSession() {
    try {
        const currentSessionStore = apiDataSystem.getStore('currentSession');
        const sessionData = await currentSessionStore.getData();
        if (sessionData && sessionData.sessionId) {
            currentSessionId = sessionData.sessionId;
            console.log('[history] Sessão atual carregada:', currentSessionId);
        }
    } catch (error) {
        console.warn('[history] Erro ao carregar sessão atual:', error);
    }
}

// Função para definir a sessão atual
async function setCurrentSession(sessionId) {
    try {
        currentSessionId = sessionId;
        const currentSessionStore = apiDataSystem.getStore('currentSession');
        await currentSessionStore.updateData({ sessionId: sessionId });
        console.log('[history] Sessão atual definida:', sessionId);
    } catch (error) {
        console.error('[history] Erro ao definir sessão atual:', error);
    }
}

// Função para carregar sessões
async function loadSessions() {
    try {
        await waitForApiSystem(); // Garante que o sistema de persistência esteja pronto

        // Tenta carregar do backend primeiro
        try {
            const response = await fetch('http://localhost:8000/api/data/sessions');
            if (response.ok) {
                const data = await response.json();
                let sessions = data.sessions || [];

                // Converte formato do backend para formato do frontend
                sessions = sessions.map(session => ({
                    id: session.session_id,
                    name: session.session_name || session.session_id, // Usa session_name separadamente
                    notes: session.description || '', // description são as notas
                    timestamp: session.created_at, // Data de criação
                    lastModified: session.updated_at || null // Data de última modificação
                }));

                console.log('[loadSessions] Sessões carregadas do backend:', sessions);
                return sessions;
            }
        } catch (backendError) {
            console.warn('[loadSessions] Erro ao carregar do backend, tentando fallback:', backendError);
        }

        // Fallback para o store local se backend falhar
        let sessions = await historyStore.getData();

        // Garante que sessions seja sempre um array
        if (!sessions || !Array.isArray(sessions)) {
            sessions = [];
        }

        if (sessions.length === 0) {
            // Se não houver sessões na API, cria algumas de exemplo APENAS EM MEMÓRIA
            console.log('[loadSessions] Nenhuma sessão encontrada, criando dados mock em memória');
            sessions = [
                { id: 'sessao_1', name: 'Simulação Inicial', notes: 'Primeiro projeto de teste', timestamp: new Date(2023, 0, 15, 10, 30).toISOString() },
                { id: 'sessao_2', name: 'Trafo Grande EOL', notes: 'Configuração para parque eólico', timestamp: new Date(2023, 1, 20, 14, 0).toISOString() },
                { id: 'sessao_3', name: 'Otimização Perdas', notes: 'Foco em perdas em vazio', timestamp: new Date(2023, 2, 5, 9, 0).toISOString() },
            ];
            // NÃO salva no backend para evitar erro 422
        }
        return sessions;
    } catch (error) {
        console.error('[loadSessions] Erro ao carregar sessões:', error);
        return []; // Retorna array vazio em caso de erro
    }
}

// Função para atualizar metadados da sessão (nome e notas)
async function updateSessionMetadata(sessionId, newName, newNotes) {
    try {
        console.log('[updateSessionMetadata] Iniciando atualização:', { sessionId, newName, newNotes });

        // Carrega os dados atuais da sessão
        const sessions = await loadSessions();
        const session = sessions.find(s => s.id === sessionId);
        if (!session) {
            console.error('Sessão não encontrada:', sessionId);
            setActionMessage('<div class="alert alert-danger py-1 px-2">Sessão não encontrada.</div>');
            return false;
        }

        // Prepara os dados para atualização
        const updateData = {
            session_name: newName !== null ? newName : session.name,
            description: newNotes !== null ? newNotes : session.notes
        };

        console.log('[updateSessionMetadata] Dados para atualização:', updateData);

        // Tenta atualizar no backend primeiro
        const response = await fetch(`http://localhost:8000/api/data/sessions/${sessionId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });

        console.log('[updateSessionMetadata] Resposta do servidor:', response.status, response.statusText);

        if (response.ok) {
            const responseData = await response.json();
            console.log('[updateSessionMetadata] Atualização bem-sucedida:', responseData);
            setActionMessage('<div class="alert alert-success py-1 px-2">Sessão atualizada com sucesso!</div>');
        } else {
            const errorData = await response.json();
            console.error('Erro ao atualizar metadados no backend:', errorData);
            setActionMessage(`<div class="alert alert-danger py-1 px-2">Erro ao atualizar no servidor: ${errorData.detail || response.statusText}</div>`);
            return false;
        }

        // Recarrega a tabela para mostrar as alterações
        await renderSessionsTable(await loadSessions());
        // Mensagem já tem timeout automático da função setActionMessage

        return true;

    } catch (error) {
        console.error('Erro ao atualizar metadados da sessão:', error);
        setActionMessage('<div class="alert alert-danger py-1 px-2">Erro ao atualizar sessão.</div>');
        return false;
    }
}

// Função para garantir que o modal de edição existe e tem os listeners corretos
function ensureEditModalExists() {
    const editModalEl = document.getElementById('history-edit-session-modal');
    if (!editModalEl) {
        console.error('[history] Modal de edição não encontrado no HTML!');
        return false;
    }

    // Inicializar listeners se ainda não foram inicializados
    if (!editModalEl.dataset.listenersInitialized) {
        initEditModalListeners();
        editModalEl.dataset.listenersInitialized = 'true';
    }

    return true;
}

// Função para inicializar os event listeners do modal de edição
function initEditModalListeners() {
    const editModalEl = document.getElementById('history-edit-session-modal');
    if (!editModalEl) {
        console.error('[history] Modal de edição não encontrado para inicializar listeners.');
        return;
    }

    const confirmButton = document.getElementById('history-edit-modal-confirm-button');
    // const cancelButton = document.getElementById('history-edit-modal-cancel-button'); // data-bs-dismiss handles this

    // Remover listener antigo para evitar duplicação, se houver
    const newConfirmButton = confirmButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

    newConfirmButton.addEventListener('click', async () => {
        console.log('[history] Botão confirmar edição clicado');
        const sessionId = editModalEl.dataset.sessionId;
        const nameInput = document.getElementById('history-edit-session-name-input');
        const notesInput = document.getElementById('history-edit-session-notes-input');
        const errorDiv = document.getElementById('history-edit-modal-error');

        if (!sessionId) {
            if (errorDiv) errorDiv.textContent = 'Erro: ID da sessão não encontrado.';
            return;
        }
        const newName = nameInput?.value?.trim() || '';
        const newNotes = notesInput?.value?.trim() || '';

        if (!newName) {
            if (errorDiv) errorDiv.textContent = 'O nome da sessão é obrigatório.';
            return;
        }

        const sessions = await loadSessions();
        const existingSession = sessions.find(s => s.name.toLowerCase() === newName.toLowerCase() && s.id !== sessionId);
        if (existingSession) {
            if (errorDiv) errorDiv.textContent = 'Já existe uma sessão com este nome.';
            return;
        }

        const success = await updateSessionMetadata(sessionId, newName, newNotes);
        if (success) {
            const editModalInstance = bootstrap.Modal.getInstance(editModalEl);
            if (editModalInstance) {
                editModalInstance.hide();
            }
        }
    });
    console.log('[history] Event listeners do modal de edição (re)inicializados.');
}


// Função para abrir o modal de edição
function openEditModal(sessionId, currentName, currentNotes) {
    console.log('[openEditModal] Abrindo modal para sessão:', sessionId, 'Nome:', currentName, 'Notas:', currentNotes);

    if (!ensureEditModalExists()) {
        alert('Erro: Modal de edição não encontrado.');
        return;
    }

    // Adia a manipulação do modal para o próximo tick para garantir que o Bootstrap o processe
    setTimeout(() => {
        const editModalEl = document.getElementById('history-edit-session-modal');
        const nameInput = document.getElementById('history-edit-session-name-input');
        const notesInput = document.getElementById('history-edit-session-notes-input');
        const errorDiv = document.getElementById('history-edit-modal-error');

        if (!editModalEl || !nameInput || !notesInput) {
            console.error('[history] Elementos do modal de edição não encontrados.');
            alert('Erro crítico: Elementos do modal de edição não encontrados.');
            return;
        }

        nameInput.value = currentName || '';
        notesInput.value = currentNotes || '';
        if (errorDiv) errorDiv.textContent = '';
        editModalEl.dataset.sessionId = sessionId;

        console.log('[openEditModal] Valores definidos - Nome:', nameInput.value, 'Notas:', notesInput.value);

        const editModalInstance = bootstrap.Modal.getOrCreateInstance(editModalEl);
        editModalInstance.show();
        console.log('[history] Modal de edição aberto para sessão:', sessionId);
    }, 0);
}


// Função para renderizar a tabela de sessões
async function renderSessionsTable(sessions) {
    const tableBody = document.getElementById('history-table-body-content');
    if (!tableBody) return;

    // Garante que sessions seja um array
    if (!Array.isArray(sessions)) {
        console.error('[renderSessionsTable] sessions não é um array:', sessions);
        sessions = [];
    }

    if (sessions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-muted text-center py-5">Nenhuma sessão encontrada.</td></tr>';
        return;
    }

    tableBody.innerHTML = sessions.map(session => {
        const isCurrentSession = session.id === currentSessionId;
        // Usar fundo escuro com fonte clara para contraste adequado
        const rowClass = isCurrentSession ? 'table-dark' : ''; // Bootstrap 5 table-dark handles text color
        const currentIndicator = isCurrentSession ? '<i class="fas fa-star text-warning me-1" title="Projeto Atual"></i>' : '';
        const updateButtonDisabled = !isCurrentSession ? 'disabled' : '';
        const updateButtonClass = !isCurrentSession ? 'btn-secondary' : 'btn-warning';

        // Ícones de editar aparecem apenas na sessão atual
        const editNameIcon = isCurrentSession ? `
            <span class="edit-name-btn" data-id="${session.id}" data-current-name="${session.name}" title="Editar Nome" style="cursor: pointer; margin-left: 5px;">
                <i class="fas fa-edit"></i>
            </span>` : ''; // text-light/muted removed, rely on parent rowClass or default

        const editNotesIcon = isCurrentSession ? `
            <span class="edit-notes-btn" data-id="${session.id}" data-current-notes="${session.notes || ''}" title="Editar Notas" style="cursor: pointer; margin-left: 5px;">
                <i class="fas fa-edit"></i>
            </span>` : '';

        return `
        <tr class="align-middle ${rowClass}">
            <td class="text-center small">${new Date(session.timestamp).toLocaleString('pt-BR')}</td>
            <td class="text-center fw-bold">
                ${currentIndicator}${session.name}
                ${editNameIcon}
            </td>
            <td class="text-center small">
                <span class="notes-display">${session.notes || '-'}</span>
                ${editNotesIcon}
            </td>
            <td class="text-center small text-muted">${session.lastModified ? new Date(session.lastModified).toLocaleString('pt-BR') : 'Não modificado'}</td>
            <td class="text-center">
                <button class="btn btn-sm btn-info me-1 load-session-btn" data-id="${session.id}" title="Carregar Sessão">
                    <i class="fas fa-folder-open"></i>
                </button>
                <button class="btn btn-sm ${updateButtonClass} me-1 update-session-btn" data-id="${session.id}" data-name="${session.name}" data-notes="${session.notes || ''}" title="Atualizar Sessão Atual" ${updateButtonDisabled}>
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="btn btn-sm btn-danger delete-session-btn" data-id="${session.id}" title="Excluir Sessão">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        </tr>
        `;
    }).join('');

    // Adicionar listeners para edição de nome
    tableBody.querySelectorAll('.edit-name-btn').forEach(button => {
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            const sessionId = e.currentTarget.dataset.id;
            const currentName = e.currentTarget.dataset.currentName || '';
            const sessionsData = await loadSessions();
            const session = sessionsData.find(s => s.id === sessionId);
            openEditModal(sessionId, currentName, session ? session.notes || '' : '');
        });
    });

    // Adicionar listeners para edição de notas
    tableBody.querySelectorAll('.edit-notes-btn').forEach(button => {
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            const sessionId = e.currentTarget.dataset.id;
            const currentNotes = e.currentTarget.dataset.currentNotes || '';
            const sessionsData = await loadSessions();
            const session = sessionsData.find(s => s.id === sessionId);
            openEditModal(sessionId, session ? session.name || '' : '', currentNotes);
        });
    });

    // Adicionar listeners aos botões de ação
    tableBody.querySelectorAll('.load-session-btn').forEach(button => {
        button.addEventListener('click', async (e) => {
            const sessionId = e.currentTarget.dataset.id;
            console.log(`Carregar sessão: ${sessionId}`);

            try {
                // Tenta restaurar do backend primeiro
                const response = await fetch(`http://localhost:8000/api/data/sessions/${sessionId}/restore`, {
                    method: 'POST'
                });

                if (response.ok) {
                    console.log('Sessão restaurada do backend com sucesso');
                    await setCurrentSession(sessionId);
                    setActionMessage('<div class="alert alert-success py-1 px-2">Sessão carregada com sucesso! Redirecionando...</div>', 0);
                    document.dispatchEvent(new CustomEvent('sessionRestored', { detail: { sessionId } }));
                    await renderSessionsTable(await loadSessions());

                    setTimeout(() => {
                        console.log('[history] Redirecionando para transformer_inputs...');
                        if (typeof window.loadModulePage === 'function') {
                            window.loadModulePage('transformer_inputs');
                        } else {
                            window.location.hash = '#transformer_inputs';
                        }
                    }, 1000);
                } else {
                    const errorData = await response.json();
                    console.warn('Erro ao restaurar do backend:', errorData.detail || response.statusText);
                    setActionMessage(`<div class="alert alert-warning py-1 px-2">Erro ao carregar sessão do servidor: ${errorData.detail || response.statusText}</div>`);
                }
            } catch (error) {
                console.error('Erro ao restaurar sessão:', error);
                setActionMessage('<div class="alert alert-danger py-1 px-2">Erro ao carregar sessão.</div>');
            }
        });
    });

    tableBody.querySelectorAll('.update-session-btn').forEach(button => {
        button.addEventListener('click', async (e) => {
            const sessionId = e.currentTarget.dataset.id;
            const sessionName = e.currentTarget.dataset.name;
            
            if (!confirm(`Tem certeza que deseja ATUALIZAR a sessão "${sessionName}" com o estado atual da aplicação? Todos os dados desta sessão serão sobrescritos.`)) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/api/data/sessions/${sessionId}/update`, {
                    method: 'POST' // Endpoint para sobrescrever dados da sessão
                });

                if (response.ok) {
                    console.log('Sessão atualizada no backend com sucesso');
                    setActionMessage('<div class="alert alert-warning py-1 px-2">Sessão atualizada com o estado atual!</div>');
                } else {
                    const errorData = await response.json();
                    console.warn('Erro ao atualizar no backend:', errorData.detail || response.statusText);
                    setActionMessage(`<div class="alert alert-danger py-1 px-2">Erro ao atualizar sessão no servidor: ${errorData.detail || response.statusText}.</div>`);
                }
            } catch (error) {
                console.error('Erro ao atualizar sessão:', error);
                setActionMessage('<div class="alert alert-danger py-1 px-2">Erro ao atualizar sessão.</div>');
            }
            await renderSessionsTable(await loadSessions()); // Refresh table
            // Mensagem já tem timeout automático da função setActionMessage
        });
    });

    tableBody.querySelectorAll('.delete-session-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            const sessionId = e.currentTarget.dataset.id;
            const deleteModalEl = document.getElementById('history-delete-session-modal');
            if (deleteModalEl) {
                const deleteModal = bootstrap.Modal.getOrCreateInstance(deleteModalEl);
                document.getElementById('history-delete-modal-confirm-button').dataset.sessionId = sessionId;
                deleteModal.show();
                 // Store ref for closing, though data-bs-dismiss should also work
                deleteModalEl._currentModalInstance = deleteModal;
            }
        });
    });

    // Atualizar estatísticas
    document.getElementById('history-stats-total-sessions').textContent = sessions.length;
}

// Função de inicialização do módulo Histórico
async function initHistory() {
    console.log('Módulo Histórico carregado e pronto para interatividade.');
    await loadCurrentSession();

    if (typeof bootstrap === 'undefined') {
        console.error('[history] Bootstrap não está disponível!');
        return;
    }
    
    // Ensure edit modal structure exists early
    ensureEditModalExists();

    document.addEventListener('transformerDataUpdated', async (event) => {
        await renderSessionsTable(await loadSessions());
    });
    
    // Aguarda um pouco para garantir que o DOM esteja completamente carregado
    await new Promise(resolve => setTimeout(resolve, 100));

    const saveModalEl = document.getElementById('history-save-session-modal');
    const openSaveModalButton = document.getElementById('history-open-save-modal-button');

    if (!saveModalEl || !openSaveModalButton) {
        console.error('[history] Elementos do modal de salvar não encontrados.');
        return;
    }
    const saveModal = bootstrap.Modal.getOrCreateInstance(saveModalEl);

    openSaveModalButton.addEventListener('click', () => {
        const nameInput = document.getElementById('history-session-name-input');
        const notesInput = document.getElementById('history-session-notes-input');
        const errorDiv = document.getElementById('history-save-modal-error');
        if (nameInput) nameInput.value = '';
        if (notesInput) notesInput.value = '';
        if (errorDiv) errorDiv.textContent = '';
        saveModal.show();
    });

    const saveConfirmButton = document.getElementById('history-save-modal-confirm-button');
    if (saveConfirmButton) {
        saveConfirmButton.addEventListener('click', async () => {
            const sessionName = document.getElementById('history-session-name-input').value.trim();
            const sessionNotes = document.getElementById('history-session-notes-input').value.trim();
            const errorMessageDiv = document.getElementById('history-save-modal-error');

            if (!sessionName) {
                errorMessageDiv.textContent = 'O nome da sessão é obrigatório.'; return;
            }
            let sessions = await loadSessions();
            if (sessions.some(s => s.name.toLowerCase() === sessionName.toLowerCase())) {
                errorMessageDiv.textContent = 'Já existe uma sessão com este nome.'; return;
            }
            const sessionId = `sessao_${Date.now()}`;
            try {
                const response = await fetch('http://localhost:8000/api/data/sessions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ session_id: sessionId, session_name: sessionName, description: sessionNotes })
                });
                if (response.ok) {
                    await setCurrentSession(sessionId);
                } else { // Fallback local
                    const errorData = await response.json();
                    console.warn('Erro ao salvar no backend, usando fallback local:', errorData.detail || response.statusText);
                    // No local fallback for new session creation if backend fails initially
                    errorMessageDiv.textContent = `Erro do servidor: ${errorData.detail || response.statusText}`; return;
                }
            } catch (error) { // Network error or similar
                console.error('Erro de rede ao salvar no backend:', error);
                errorMessageDiv.textContent = 'Erro de rede ao salvar sessão.'; return;
            }
            errorMessageDiv.textContent = '';
            saveModal.hide();
            await renderSessionsTable(await loadSessions());
            setActionMessage('<div class="alert alert-success py-1 px-2">Sessão salva com sucesso!</div>');
        });
    }


    const deleteModalEl = document.getElementById('history-delete-session-modal');
    if (!deleteModalEl) {
        console.error('[history] Elemento modal de exclusão não encontrado.');
        return;
    }
    const deleteModal = bootstrap.Modal.getOrCreateInstance(deleteModalEl);
    const deleteConfirmButton = document.getElementById('history-delete-modal-confirm-button');

    if (deleteConfirmButton) {
        deleteConfirmButton.addEventListener('click', async (e) => {
            const sessionIdToDelete = e.currentTarget.dataset.sessionId;
            const modalInstance = bootstrap.Modal.getInstance(deleteModalEl);
            if (modalInstance) modalInstance.hide();
            
            try {
                const response = await fetch(`http://localhost:8000/api/data/sessions/${sessionIdToDelete}`, { method: 'DELETE' });
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `Erro HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Erro ao deletar sessão no backend:', error);
                setActionMessage(`<div class="alert alert-danger py-1 px-2">Erro ao excluir: ${error.message}</div>`);
                return; // Stop if backend delete failed
            }
            await renderSessionsTable(await loadSessions());
            setActionMessage('<div class="alert alert-success py-1 px-2">Sessão excluída com sucesso!</div>');
        });
    }

    // Lógica de busca e filtro
    const searchInput = document.getElementById('history-search-input');
    const searchButton = document.getElementById('history-search-button');
    async function filterSessions() {
        const searchTerm = searchInput.value.toLowerCase();
        const allSessions = await loadSessions();
        if (!searchTerm) { await renderSessionsTable(allSessions); return; }
        const filtered = allSessions.filter(session => 
            (session.name && session.name.toLowerCase().includes(searchTerm)) ||
            (session.notes && session.notes.toLowerCase().includes(searchTerm)) ||
            (session.timestamp && new Date(session.timestamp).toLocaleDateString('pt-BR').includes(searchTerm)) ||
            (session.id && session.id.toLowerCase().includes(searchTerm))
        );
        await renderSessionsTable(filtered);
    }
    if (searchButton) searchButton.addEventListener('click', filterSessions);
    if (searchInput) searchInput.addEventListener('input', filterSessions);

    const downloadAllDataBtn = document.getElementById('downloadAllDataBtn');
    if (downloadAllDataBtn) downloadAllDataBtn.addEventListener('click', showDownloadModal);
    const confirmDownloadBtn = document.getElementById('confirmDownloadBtn');
    if (confirmDownloadBtn) confirmDownloadBtn.addEventListener('click', downloadAllHistoryData);

    const sessions = await loadSessions();
    await renderSessionsTable(sessions);
    if (downloadAllDataBtn && sessions && sessions.length > 0) {
        downloadAllDataBtn.disabled = false;
    }
}

function showDownloadModal() {
    const modalEl = document.getElementById('downloadAllDataModal');
    if (!modalEl) return;
    const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
    document.getElementById('downloadPasswordInput').value = '';
    document.getElementById('downloadPasswordError').textContent = '';
    modal.show();
}

async function downloadAllHistoryData() {
    const passwordInput = document.getElementById('downloadPasswordInput');
    const errorDiv = document.getElementById('downloadPasswordError');
    const confirmBtn = document.getElementById('confirmDownloadBtn');
    const spinner = document.getElementById('downloadSpinner');
    const password = passwordInput.value.trim();

    if (!password) { errorDiv.textContent = 'Por favor, digite a senha.'; return; }

    try {
        confirmBtn.disabled = true; spinner.classList.remove('d-none'); errorDiv.textContent = '';
        const response = await fetch('/api/data/download-all-history', {
            method: 'POST', headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ password: password })
        });
        if (!response.ok) { const errorData = await response.json(); throw new Error(errorData.detail || `Erro HTTP ${response.status}`); }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a'); a.style.display = 'none'; a.href = url;
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
        a.download = `historico_completo_tts_${timestamp}.xlsx`;
        document.body.appendChild(a); a.click(); window.URL.revokeObjectURL(url); document.body.removeChild(a);
        
        const modalEl = document.getElementById('downloadAllDataModal');
        if (modalEl) { const modal = bootstrap.Modal.getInstance(modalEl); if (modal) modal.hide(); }

        setActionMessage('<div class="alert alert-success py-2 px-3"><i class="fas fa-check-circle me-2"></i>Dados históricos baixados com sucesso!</div>', 5000);
    } catch (error) {
        console.error('[downloadAllHistoryData] Erro ao baixar dados:', error);
        errorDiv.textContent = (error.message.includes('senha') || error.message.includes('password') || error.message.includes('401')) ?
            'Senha incorreta. Tente novamente.' : `Erro ao baixar dados: ${error.message}`;
    } finally {
        confirmBtn.disabled = false; spinner.classList.add('d-none');
    }
}

document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'history') {
        initHistory();
    }
});
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('history-table-body-content')) { // Check for a unique element of this page
        initHistory();
    }
});